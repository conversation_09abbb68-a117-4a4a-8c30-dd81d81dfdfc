---
description:
globs:
alwaysApply: false
---
# Research Context Navigation Rules

## Overview
The SISTEP platform includes a research functionality that tracks users through different steps of a usability study. This context must be maintained across navigation between the Laravel backend (app.sistep.com.br) and Nuxt frontend (sistep.com.br).

## Key Components

### Research Banner (@ResearchBanner.vue)
- Detects research context via `localStorage.getItem('researchParticipantId')`
- Manages current step via `localStorage.getItem('researchCurrentStep')`
- Handles navigation between Laravel and Nuxt with step preservation

### URL Parameters for Navigation
When navigating between Laravel and Nuxt, always include:
- `participant_id`: The research participant identifier
- `current_step`: The current step in the research process (1-4)
- `stepCompleted`: When returning from external steps (dass, panel)

### Research Steps
1. TCLE acceptance
2. DASS-21 test execution
3. Psychologist panel access
4. Google Forms questionnaire

## Implementation Patterns

### Step Detection and Persistence
```typescript
// Detect research context
const isResearchContext = computed(() => {
    return typeof localStorage !== 'undefined' && localStorage.getItem('researchParticipantId');
});

// Auto-process URL parameters
onMounted(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const queryParticipantId = urlParams.get('participant_id');
    const queryCurrentStep = urlParams.get('current_step');
    
    if (queryParticipantId && queryCurrentStep) {
        localStorage.setItem('researchParticipantId', queryParticipantId);
        localStorage.setItem('researchCurrentStep', queryCurrentStep);
    }
});
```

### Cross-Platform Navigation
```typescript
// Always include current step when redirecting
function redirectToNuxt() {
    const participantId = localStorage.getItem('researchParticipantId');
    const currentStep = localStorage.getItem('researchCurrentStep');
    window.location.href = `http://localhost:3000/pesquisa?participant_id=${participantId}&current_step=${currentStep}`;
}

// For specific steps, include the expected step number
const sistepPanelLink = computed(() => {
    return `${SISTEP_PANEL_BASE_URL}?participant_id=${participantId.value}&current_step=3&research_return_url=${encodeURIComponent(returnUrl)}`;
});

const dass21Link = computed(() => {
    return `${DASS21_BASE_URL}?participant_id=${participantId.value}&current_step=2&research_return_url=${encodeURIComponent(returnUrl)}`;
});
```

### TCLE Auto-Accept
In research context, TCLE should be automatically accepted and disabled:
```vue
<input 
    type="checkbox" 
    :checked="form.tcle"
    :disabled="!!isResearchContext"
    @change="!isResearchContext && (form.tcle = $event.target.checked)"
/>
```

## Rules
1. Always maintain research context when navigating between platforms
2. Pass current step as URL parameter in cross-platform navigation
3. Auto-accept TCLE in research context
4. Clean URL parameters after processing to maintain clean URLs
5. Handle both step progression (stepCompleted) and direct navigation (current_step)

## Files Affected
- @ResearchBanner.vue
- @Register.vue (auth)
- Any page that might be accessed during research flow
