# SISTEP - Sistema de Testes Psicológicos

## Informações Gerais

### Plataforma
- Backend: Laravel 12 e Inertia.js para o painel administrativo e API
- Frontend: Nuxt

### Arquitetura
- `app.sistep.com.br`: Backend em Laravel 12 (API e administração)
- `sistep.com.br`: Frontend em Nuxt (testes e páginas institucionais)

### Nota sobre componentes UI
- O projeto utiliza Shadcn-Vue para componentes de UI
- Para adicionar novos componentes, usar: `npx shadcn-vue@latest add <component-name>`

### Tipos de Usuários
- **Administrador**: Acesso total ao sistema
- **Psicólogo**: Acesso a testes e resultados de seus pacientes, possui campo CRP
- **Estudante**: Acesso a testes e resultados limitados, possui campo de instituição
- **Paciente**: Acesso apenas aos seus próprios resultados
- **Paciente Anônimo**: Apenas para testes sem login formal

### Campos de Usuário
Os campos presentes na tabela de usuários incluem:
- id, name, email, email_verified_at
- crp (para psicólogos)
- institution (para estudantes)
- birthdate, gender, phone
- active (para controle de status)
- is_anonymous, anonymous_id (para pacientes anônimos)
- Relacionamentos com roles (papéis) via Spatie/Permission

### GitHub
- Repositório: https://github.com/agenciamav/SISTEP
- Projeto: https://github.com/orgs/agenciamav/projects/14 (SISTEP - Migração Laravel 12)

## Regras e Padrões do Projeto

### Componentes UI
- Implementados diversos componentes de interface:
  - Botões, inputs, select, radio-group, etc.
  - Componentes seguem padrão de design consistente

### Rotas e Controladores
- Rota `/anonymous` para usuários anônimos
- Rota `/admin` para administradores
- Controllers específicos para cada tipo de usuário

### Sistema de Permissões
- Utiliza biblioteca Spatie/Permission
- Permissões específicas para cada tipo de usuário
- Acesso controlado a recursos baseado em papéis

### Formulários
- Validações específicas por tipo de usuário
- Campos condicionais baseados no tipo de usuário selecionado

### Migração e Banco de Dados
- Migrations para todos os campos de usuário
- Seeders para popular dados iniciais
- Factory para gerar dados de teste

## Convenções de Código

### Frontend
- Componentes Vue com TypeScript
- Padrão de nomenclatura: PascalCase para componentes
- Idioma PT-BR para interface, EN para código e comentários

### Backend
- Laravel 12 com API RESTful
- PSR-12 para padrões de código PHP
- Controladores especializados por funcionalidade

## Observações Importantes
- Manter todas as validações de dados no backend
- Processar cálculos de testes no backend
- Manter coerência entre formulários de registro e perfil
- Seguir convenções de design e UX consistentes

### Issues Principais
- #1: Plano de Migração para Laravel 12
- #12: Definir estrutura de banco de dados
- #13: Implementar sistema de autenticação (Sanctum/Passport)
- #14: Implementar API para DASS-21
- #15: Configurar CORS para comunicação com o frontend Nuxt
- #16: Integração do Frontend Nuxt com a API Laravel

### Instruções de Desenvolvimento
- Seguir arquitetura API RESTful (Laravel) + Frontend Nuxt
- Implementar autenticação via Laravel Sanctum/Passport
- Processar cálculos e lógica de negócios no backend
- Garantir conformidade com LGPD

### Requisitos
- Teste DASS-21 com cálculo de resultados
- Sistema de usuários (pacientes e psicólogos)
- Questionários SUS e UEQ para pesquisa
- Dashboard para visualização de resultados

### Observações
- Projeto tem dupla finalidade: acadêmica (mestrado) e comercial
- Foco inicial na implementação do DASS-21 para a pesquisa acadêmica 