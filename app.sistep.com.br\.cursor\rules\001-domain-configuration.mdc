---
description: 
globs: 
alwaysApply: false
---
# Domain Configuration Guidelines

## Project Architecture
This is a Laravel application with Inertia.js + Vue 3 frontend, not a separate Nuxt application.

## Current Domain Setup
- **Admin <PERSON>**: `app.sistep.com.br.local`
- **Frontend Target**: `sistep.com.br.local` (root domain)

## Configuration Files

### Environment Configuration
- @.env: Contains APP_URL and VITE_APP_URL settings
- @config/app.php: Laravel application configuration

### Frontend Configuration  
- @vite.config.ts: Vite build configuration
- @package.json: Frontend dependencies and scripts

## Domain Routing Options

### Option 1: Subdomain Routing (Current)
- Keep current Laravel + Inertia setup
- Use middleware to differentiate admin vs public routes
- Configure virtual hosts for different subdomains

### Option 2: Separate Frontend (Future)
- Extract frontend to separate Nuxt application
- Configure Laravel as API-only backend
- Set up CORS for cross-domain communication

## Implementation Guidelines

1. **Virtual Hosts**: Configure web server (Apache/Nginx) with separate virtual hosts
2. **Environment Variables**: Adjust APP_URL and VITE_APP_URL accordingly  
3. **Route Groups**: Use route groups with domain restrictions
4. **Middleware**: Implement domain-specific middleware for routing logic
5. **Asset Building**: Configure Vite for proper asset URLs per domain

## Security Considerations
- CORS configuration for cross-domain requests
- CSRF token handling between domains
- Session management across subdomains

- SSL certificate configuration for both domains