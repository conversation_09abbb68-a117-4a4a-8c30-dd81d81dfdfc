<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class AdminController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        return Inertia::render('admin/Dashboard', [
            'usersCount' => User::count(),
            'rolesCount' => Role::count(),
        ]);
    }
    
    /**
     * Display a listing of users.
     */
    public function users()
    {
        $users = User::with('roles')->get();
        
        return Inertia::render('admin/users/Index', [
            'users' => $users
        ]);
    }
    
    /**
     * Display the form to edit a user.
     */
    public function editUser(User $user)
    {
        $roles = Role::all();
        
        return Inertia::render('admin/users/Edit', [
            'user' => $user->load('roles'),
            'roles' => $roles
        ]);
    }
    
    /**
     * Update the user's roles.
     */
    public function updateUser(Request $request, User $user)
    {
        $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);
        
        $user->syncRoles($request->roles);
        
        return redirect()->route('admin.users')
            ->with('success', 'Perfis de usuário atualizados com sucesso.');
    }
} 