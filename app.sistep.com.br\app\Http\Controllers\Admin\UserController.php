<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    /**
     * Exibe a lista de todos os usuários.
     */
    public function index()
    {
        $users = User::with('roles')->get();
        $roles = Role::all();
        
        return Inertia::render('admin/users/Index', [
            'users' => $users,
            'roles' => $roles,
        ]);
    }

    /**
     * Exibe o formulário para criar um novo usuário.
     */
    public function create()
    {
        $roles = Role::all();
        
        return Inertia::render('admin/users/create', [
            'roles' => $roles,
        ]);
    }

    /**
     * Armazena um novo usuário.
     */
    public function store(Request $request)
    {
        // Log para depuração dos dados recebidos
        Log::info('UserController store - Dados recebidos:', [
            'all' => $request->all(),
            'roles' => $request->roles,
        ]);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users',
            'password' => 'required|min:8|confirmed',
            'roles' => 'required|array|min:1',
            'crp' => 'nullable|string|max:20',
            'institution' => 'nullable|string|max:255',
            'birthdate' => 'nullable|date',
            'gender' => ['nullable', Rule::in(['M', 'F', 'O'])],
            'phone' => 'nullable|string|max:20',
            'active' => 'boolean',
        ]);
        
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'crp' => $request->crp,
            'institution' => $request->institution,
            'birthdate' => $request->birthdate,
            'gender' => $request->gender,
            'phone' => $request->phone,
            'active' => $request->active ?? true,
        ]);
        
        // Certifique-se de que os roles estão sendo corretamente processados
        if (is_array($request->roles) && !empty($request->roles)) {
            // Busque as roles pelos IDs
            $roles = Role::whereIn('id', $request->roles)->get();
            
            // Log para depuração das roles encontradas
            Log::info('UserController store - Roles encontradas:', [
                'roles' => $roles->pluck('name', 'id')->toArray(),
                'count' => $roles->count(),
            ]);
            
            // Sincronize as roles com o usuário
            $user->syncRoles($roles);
        }
        
        return redirect()->route('admin.users.index')
            ->with('success', 'Usuário criado com sucesso.');
    }

    /**
     * Exibe os detalhes de um usuário específico.
     */
    public function show(User $user)
    {
        $user->load('roles');
        
        return Inertia::render('admin/users/show', [
            'user' => $user,
        ]);
    }

    /**
     * Exibe o formulário para editar um usuário.
     */
    public function edit(User $user)
    {
        $user->load('roles');
        $roles = Role::all();
        
        return Inertia::render('admin/users/edit', [
            'user' => $user,
            'roles' => $roles,
            'userRoles' => $user->roles->pluck('name'),
        ]);
    }

    /**
     * Atualiza um usuário específico.
     */
    public function update(Request $request, User $user)
    {
        // Log para depuração dos dados recebidos
        Log::info('UserController update - Dados recebidos:', [
            'all' => $request->all(),
            'roles' => $request->roles,
        ]);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'required|array|min:1',
            'crp' => 'nullable|string|max:20',
            'institution' => 'nullable|string|max:255',
            'birthdate' => 'nullable|date',
            'gender' => ['nullable', Rule::in(['M', 'F', 'O'])],
            'phone' => 'nullable|string|max:20',
            'active' => 'boolean',
        ]);
        
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'crp' => $request->crp,
            'institution' => $request->institution,
            'birthdate' => $request->birthdate,
            'gender' => $request->gender,
            'phone' => $request->phone,
            'active' => $request->active ?? $user->active,
        ]);
        
        // Atualiza a senha apenas se foi fornecida
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'min:8|confirmed',
            ]);
            
            $user->update([
                'password' => bcrypt($request->password),
            ]);
        }
        
        // Certifique-se de que os roles estão sendo corretamente processados
        if (is_array($request->roles) && !empty($request->roles)) {
            // Busque as roles pelos IDs
            $roles = Role::whereIn('id', $request->roles)->get();
            
            // Log para depuração das roles encontradas
            Log::info('UserController update - Roles encontradas:', [
                'roles' => $roles->pluck('name', 'id')->toArray(),
                'count' => $roles->count(),
            ]);
            
            // Sincronize as roles com o usuário
            $user->syncRoles($roles);
        }
        
        return redirect()->route('admin.users.index')
            ->with('success', 'Usuário atualizado com sucesso.');
    }

    /**
     * Remove um usuário específico.
     */
    public function destroy(User $user)
    {
        // Impede que o usuário exclua a si mesmo
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Você não pode excluir seu próprio usuário.');
        }
        
        $user->delete();
        
        return redirect()->route('admin.users.index')
            ->with('success', 'Usuário excluído com sucesso.');
    }
    
    /**
     * Alterna o status ativo/inativo do usuário.
     */
    public function toggleStatus(User $user)
    {
        // Impede que o usuário desative a si mesmo
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Você não pode desativar seu próprio usuário.');
        }
        
        $user->update([
            'active' => !$user->active,
        ]);
        
        $status = $user->active ? 'ativado' : 'desativado';
        
        return redirect()->route('admin.users.index')
            ->with('success', "Usuário {$status} com sucesso.");
    }
} 