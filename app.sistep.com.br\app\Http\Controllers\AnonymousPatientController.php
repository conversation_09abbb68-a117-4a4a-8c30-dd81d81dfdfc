<?php

namespace App\Http\Controllers;

use App\Models\TestResult;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class AnonymousPatientController extends Controller
{
    /**
     * Criar um usuário paciente anônimo
     */
    public function createAnonymousUser(Request $request)
    {
        $request->validate([
            'gender' => ['nullable', Rule::in(['M', 'F', 'O'])],
            'birthdate' => ['nullable', 'date', 'before:today'],
        ]);
        
        // Gerar email e senha temporários
        $randomEmail = 'anonymous_' . uniqid() . '@sistep.com.br';
        $randomPassword = Str::random(12);
        
        // Criar usuário anônimo
        $user = User::create([
            'name' => 'Paciente Anônimo',
            'email' => $randomEmail,
            'password' => Hash::make($randomPassword),
            'is_anonymous' => true,
            'gender' => $request->gender,
            'birthdate' => $request->birthdate,
            'anonymous_id' => md5(uniqid() . time()),
        ]);
        
        // Atribuir papel de paciente
        $user->assignRole('patient');
        
        // Armazenar o ID anônimo na sessão
        session(['anonymous_user_id' => $user->id]);
        session(['anonymous_id' => $user->anonymous_id]);
        
        return response()->json([
            'success' => true,
            'message' => 'Usuário anônimo criado com sucesso',
            'anonymous_id' => $user->anonymous_id,
        ]);
    }
    
    /**
     * Salvar resultado de teste para um usuário anônimo
     */
    public function saveTestResult(Request $request)
    {
        $request->validate([
            'anonymous_id' => ['required', 'string'],
            'test_type' => ['required', 'string'],
            'score' => ['required', 'array'],
            'result_data' => ['required', 'array'],
        ]);
        
        // Buscar usuário pelo anonymous_id
        $user = User::where('anonymous_id', $request->anonymous_id)->first();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Usuário anônimo não encontrado'
            ], 404);
        }
        
        // Criar registro de resultado
        $testResult = TestResult::create([
            'user_id' => $user->id,
            'test_type' => $request->test_type,
            'score' => $request->score,
            'result_data' => $request->result_data,
            'completed_at' => now(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Resultado salvo com sucesso',
            'test_result_id' => $testResult->id,
        ]);
    }
    
    /**
     * Visualizar resultados de testes anônimos
     */
    public function viewResults(Request $request, $anonymousId)
    {
        // Buscar usuário pelo anonymous_id
        $user = User::where('anonymous_id', $anonymousId)->first();
        
        if (!$user) {
            return Inertia::render('AnonymousResults/NotFound');
        }
        
        // Obter resultados de testes
        $testResults = $user->testResults()->orderBy('completed_at', 'desc')->get();
        
        return Inertia::render('AnonymousResults/Index', [
            'testResults' => $testResults,
            'anonymousId' => $anonymousId,
        ]);
    }
} 