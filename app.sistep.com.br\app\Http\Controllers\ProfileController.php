<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Role;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        return Inertia::render('settings/Profile', [
            'status' => session('status'),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $user = $request->user();
        $oldUserType = null;
        
        // Identificar o tipo de usuário atual antes da atualização
        if ($user->hasRole('psychologist')) {
            $oldUserType = 'psychologist';
        } elseif ($user->hasRole('student')) {
            $oldUserType = 'student';
        } elseif ($user->hasRole('patient')) {
            $oldUserType = 'patient';
        }
        
        // Preencher os dados básicos
        $user->fill($request->validated());
        
        // Processar a mudança de tipo de usuário se necessário
        if ($request->has('user_type') && $oldUserType !== $request->user_type) {
            // Remover os papéis anteriores
            $user->roles()->detach();
            
            // Atribuir o novo papel
            $role = Role::where('name', $request->user_type)->first();
            if ($role) {
                $user->assignRole($role);
            }
            
            // Limpar os campos específicos de tipo que não se aplicam mais
            if ($request->user_type !== 'psychologist') {
                $user->crp = null;
            }
            
            if ($request->user_type !== 'student') {
                $user->institution = null;
            }
        }
        
        $user->save();

        return Redirect::route('profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
} 