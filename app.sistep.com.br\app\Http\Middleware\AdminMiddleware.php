<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verifica se o usuário está autenticado e possui o papel de admin
        if (!$request->user() || !$request->user()->hasRole('admin')) {
            // Verifica se é uma solicitação AJAX ou espera JSON
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Acesso negado. Você não tem permissão para acessar este recurso.'], 403);
            }
            
            // Redireciona para a página inicial com mensagem de erro
            return redirect()->route('home')->with('error', '<PERSON>sso negado. Você não tem permissão para acessar este recurso.');
        }

        return $next($request);
    }
}
