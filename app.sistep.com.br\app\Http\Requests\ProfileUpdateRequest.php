<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', Rule::unique(User::class)->ignore($this->user()->id)],
            'user_type' => ['required', 'string', Rule::in(['psychologist', 'student'])],
            'crp' => ['nullable', 'required_if:user_type,psychologist', 'string', 'max:20'],
            'institution' => ['nullable', 'required_if:user_type,student', 'string', 'max:255'],
        ];
    }

    /**
     * Get the validation error messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'O nome é obrigatório.',
            'name.string' => 'O nome deve ser um texto válido.',
            'name.max' => 'O nome não pode ter mais de 255 caracteres.',
            'email.required' => 'O e-mail é obrigatório.',
            'email.email' => 'O e-mail deve ter um formato válido.',
            'email.unique' => 'Este e-mail já está sendo usado por outra conta.',
            'user_type.required' => 'O tipo de usuário é obrigatório.',
            'user_type.in' => 'O tipo de usuário deve ser psicólogo ou estudante.',
            'crp.required_if' => 'O CRP é obrigatório para psicólogos.',
            'crp.string' => 'O CRP deve ser um texto válido.',
            'crp.max' => 'O CRP não pode ter mais de 20 caracteres.',
            'institution.required_if' => 'A instituição de ensino é obrigatória para estudantes.',
            'institution.string' => 'A instituição deve ser um texto válido.',
            'institution.max' => 'A instituição não pode ter mais de 255 caracteres.',
        ];
    }
} 