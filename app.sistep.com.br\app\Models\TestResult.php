<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TestResult extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'test_type',
        'score',
        'result_data',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'score' => 'array',
        'result_data' => 'array',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the test result.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
} 