<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'crp',
        'institution',
        'phone',
        'birthdate',
        'gender',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'birthdate' => 'date',
        'is_active' => 'boolean',
    ];
    
    /**
     * Get the test results for the user.
     */
    public function testResults(): HasMany
    {
        return $this->hasMany(TestResult::class);
    }
    
    /**
     * Verifica se o usuário é um psicólogo.
     */
    public function isPsychologist(): bool
    {
        return $this->hasRole('psychologist');
    }
    
    /**
     * Verifica se o usuário é um estudante.
     */
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }
    
    /**
     * Verifica se o usuário é um paciente.
     */
    public function isPatient(): bool
    {
        return $this->hasRole('patient');
    }
    
    /**
     * Verifica se o usuário é anônimo.
     */
    public function isAnonymous(): bool
    {
        return $this->is_anonymous;
    }
    
    /**
     * Gera um ID anônimo único para o usuário.
     */
    public function generateAnonymousId(): void
    {
        if (empty($this->anonymous_id)) {
            $this->anonymous_id = md5($this->id . time() . uniqid());
            $this->save();
        }
    }
}
