<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TestResult>
 */
class TestResultFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'test_type' => $this->faker->randomElement(['DASS21', 'SUS', 'UEQ']),
            'completed_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
        ];
    }

    /**
     * Configure o resultado como teste DASS-21
     */
    public function dass21(): static
    {
        return $this->state(function (array $attributes) {
            // Gerar pontuações para DASS-21
            $depression = $this->faker->numberBetween(0, 21);
            $anxiety = $this->faker->numberBetween(0, 21);
            $stress = $this->faker->numberBetween(0, 21);
            
            return [
                'test_type' => 'DASS21',
                'score' => [
                    'depression' => $depression,
                    'anxiety' => $anxiety,
                    'stress' => $stress,
                    'total' => $depression + $anxiety + $stress,
                ],
                'result_data' => [
                    'questions' => $this->generateDass21Questions(),
                    'depression_level' => $this->getDass21Level($depression, 'depression'),
                    'anxiety_level' => $this->getDass21Level($anxiety, 'anxiety'),
                    'stress_level' => $this->getDass21Level($stress, 'stress'),
                ],
            ];
        });
    }
    
    /**
     * Configure o resultado como teste SUS
     */
    public function sus(): static
    {
        return $this->state(function (array $attributes) {
            $questions = [];
            $totalScore = 0;
            
            // SUS tem 10 questões
            for ($i = 1; $i <= 10; $i++) {
                $score = $this->faker->numberBetween(1, 5);
                $questions[$i] = $score;
                
                // Nas questões ímpares o cálculo é (score - 1)
                // Nas questões pares o cálculo é (5 - score)
                if ($i % 2 === 1) {
                    $totalScore += ($score - 1);
                } else {
                    $totalScore += (5 - $score);
                }
            }
            
            // Total multiplicado por 2.5 para escala 0-100
            $finalScore = $totalScore * 2.5;
            
            return [
                'test_type' => 'SUS',
                'score' => [
                    'sus_score' => $finalScore,
                ],
                'result_data' => [
                    'questions' => $questions,
                    'usability_level' => $this->getSusUsabilityLevel($finalScore),
                ],
            ];
        });
    }
    
    /**
     * Gera respostas aleatórias para as 21 perguntas do DASS-21
     */
    private function generateDass21Questions(): array
    {
        $questions = [];
        for ($i = 1; $i <= 21; $i++) {
            $questions[$i] = $this->faker->numberBetween(0, 3); // 0-3 são as opções do DASS-21
        }
        return $questions;
    }
    
    /**
     * Retorna o nível de severidade com base na pontuação e tipo
     */
    private function getDass21Level(int $score, string $type): string
    {
        $levels = [
            'depression' => [
                [0, 9, 'Normal'],
                [10, 13, 'Leve'],
                [14, 20, 'Moderada'],
                [21, 27, 'Severa'],
                [28, 42, 'Extremamente Severa']
            ],
            'anxiety' => [
                [0, 7, 'Normal'],
                [8, 9, 'Leve'],
                [10, 14, 'Moderada'],
                [15, 19, 'Severa'],
                [20, 42, 'Extremamente Severa']
            ],
            'stress' => [
                [0, 14, 'Normal'],
                [15, 18, 'Leve'],
                [19, 25, 'Moderada'],
                [26, 33, 'Severa'],
                [34, 42, 'Extremamente Severa']
            ]
        ];
        
        foreach ($levels[$type] as $level) {
            if ($score >= $level[0] && $score <= $level[1]) {
                return $level[2];
            }
        }
        
        return 'Desconhecido';
    }
    
    /**
     * Retorna o nível de usabilidade com base no score SUS
     */
    private function getSusUsabilityLevel(float $score): string
    {
        if ($score >= 85) return 'Excelente';
        if ($score >= 72) return 'Bom';
        if ($score >= 52) return 'Regular';
        if ($score >= 38) return 'Ruim';
        return 'Péssimo';
    }
} 