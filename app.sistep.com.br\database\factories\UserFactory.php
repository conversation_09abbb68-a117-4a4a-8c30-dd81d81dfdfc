<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'is_anonymous' => false,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Configure o usuário como admin.
     */
    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => fake()->name() . ' (Admin)',
            ];
        })->afterCreating(function (User $user) {
            $user->assignRole('admin');
        });
    }

    /**
     * Configure o usuário como psicólogo.
     */
    public function psychologist(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Dr(a). ' . fake()->name(),
                'crp' => fake()->numerify('######-#'),
            ];
        })->afterCreating(function (User $user) {
            $user->assignRole('psychologist');
        });
    }

    /**
     * Configure o usuário como estudante.
     */
    public function student(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => fake()->name(),
                'institution' => fake()->randomElement([
                    'UFRGS', 'PUCRS', 'UNISINOS', 'UNISC', 'UFCSPA', 
                    'ULBRA', 'UNIPAMPA', 'UFPEL', 'FURG', 'UNICRUZ'
                ]),
            ];
        })->afterCreating(function (User $user) {
            $user->assignRole('student');
        });
    }

    /**
     * Configure o usuário como paciente.
     */
    public function patient(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'birthdate' => fake()->dateTimeBetween('-70 years', '-18 years')->format('Y-m-d'),
                'gender' => fake()->randomElement(['M', 'F', 'O']),
                'phone' => fake()->numerify('(##) #####-####'),
            ];
        })->afterCreating(function (User $user) {
            $user->assignRole('patient');
        });
    }

    /**
     * Configure o usuário como paciente anônimo.
     */
    public function anonymousPatient(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Paciente Anônimo',
                'email' => 'anonymous_' . uniqid() . '@sistep.com.br',
                'birthdate' => fake()->dateTimeBetween('-70 years', '-18 years')->format('Y-m-d'),
                'gender' => fake()->randomElement(['M', 'F', 'O']),
                'is_anonymous' => true,
                'anonymous_id' => md5(uniqid() . time()),
            ];
        })->afterCreating(function (User $user) {
            $user->assignRole('patient');
        });
    }
}
