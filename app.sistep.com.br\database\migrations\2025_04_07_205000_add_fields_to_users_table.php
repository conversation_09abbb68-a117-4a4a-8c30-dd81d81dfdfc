<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('crp')->nullable()->after('email')->comment('Número de registro no Conselho Regional de Psicologia');
            $table->string('institution')->nullable()->after('crp')->comment('Instituição de ensino para estudantes');
            $table->date('birthdate')->nullable()->after('email_verified_at');
            $table->enum('gender', ['M', 'F', 'O'])->nullable()->after('birthdate')->comment('M: Masculino, F: Feminino, O: Outro');
            $table->string('phone', 20)->nullable()->after('gender');
            $table->boolean('active')->default(true)->after('remember_token');
            $table->boolean('is_anonymous')->default(false)->after('active')->comment('Indica se é um usuário anônimo (paciente)');
            $table->string('anonymous_id')->nullable()->after('is_anonymous')->comment('Identificador único para usuários anônimos');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['crp', 'institution', 'birthdate', 'gender', 'phone', 'active', 'is_anonymous', 'anonymous_id']);
        });
    }
}; 