<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('test_type')->comment('Tipo de teste: DASS21, SUS, UEQ, etc.');
            $table->json('score')->nullable()->comment('Pontuações por categorias');
            $table->json('result_data')->nullable()->comment('Dados completos do teste');
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            // Índices para melhorar performance
            $table->index(['user_id', 'test_type']);
            $table->index('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_results');
    }
}; 