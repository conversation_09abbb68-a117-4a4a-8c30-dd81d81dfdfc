<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Primeiro criamos os papéis e permissões básicas + usuários admin fixos
        $this->call(RolesAndPermissionsSeeder::class);
        
        // Depois criamos usuários de teste adicionais
        if (app()->environment('local', 'development', 'testing')) {
            $this->call(UserSeeder::class);
            
            // Por último, criamos dados de resultados de testes
            $this->call(TestResultSeeder::class);
        }
    }
}
