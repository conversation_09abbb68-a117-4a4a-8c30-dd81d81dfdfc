<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Resetar cache de permissões
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Criar permissões
        $permissions = [
            'users_manage',
            'roles_manage',
            'tests_manage',
            'results_view',
            'dashboard_access',
            'own_results_view',
            'patient_results_view',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Criar perfis e atribuir permissões
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $psychologistRole = Role::create(['name' => 'psychologist']);
        $psychologistRole->givePermissionTo([
            'tests_manage',
            'results_view',
            'dashboard_access',
            'patient_results_view',
            'own_results_view',
        ]);
        
        $studentRole = Role::create(['name' => 'student']);
        $studentRole->givePermissionTo([
            'tests_manage',
            'own_results_view',
            'dashboard_access',
        ]);

        $patientRole = Role::create(['name' => 'patient']);
        $patientRole->givePermissionTo([
            'own_results_view',
        ]);        
    }
} 