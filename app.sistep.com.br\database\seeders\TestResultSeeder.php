<?php

namespace Database\Seeders;

use App\Models\TestResult;
use App\Models\User;
use Illuminate\Database\Seeder;

class TestResultSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Obter pacientes
        $patients = User::role('patient')->get();
        
        // Para cada paciente, criar 0-3 resultados de testes DASS21
        foreach ($patients as $patient) {
            $numTests = rand(0, 3);
            
            for ($i = 0; $i < $numTests; $i++) {
                TestResult::factory()
                    ->dass21()
                    ->create([
                        'user_id' => $patient->id,
                        'completed_at' => fake()->dateTimeBetween('-6 months', 'now'),
                    ]);
            }
            
            // Adicionar SUS para alguns pacientes (30% de chance)
            if (rand(1, 100) <= 30) {
                TestResult::factory()
                    ->sus()
                    ->create([
                        'user_id' => $patient->id,
                        'completed_at' => fake()->dateTimeBetween('-1 month', 'now'),
                    ]);
            }
        }
    }
} 