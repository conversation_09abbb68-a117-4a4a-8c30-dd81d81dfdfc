<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar psicólogos para teste (5)
        User::factory()
            ->count(5)
            ->psychologist()
            ->create();
            
        // Criar estudantes para teste (8)
        User::factory()
            ->count(8)
            ->student()
            ->create();
            
        // Criar pacientes para teste (15)
        User::factory()
            ->count(15)
            ->patient()
            ->create();
            
        // Criar pacientes anônimos para teste (5)
        User::factory()
            ->count(5)
            ->anonymousPatient()
            ->create();
            
        // Criar mais alguns admins (2)
        User::factory()
            ->count(2)
            ->admin()
            ->create();

        // Criar usuários admin fixos
        $admins = [           
            [
                'name' => 'Luciano T.',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
            ],
            [
                'name' => 'Angélica',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
            ],
        ];

        foreach ($admins as $adminData) {
            $admin = User::updateOrCreate(
                ['email' => $adminData['email']],
                $adminData
            );
            $admin->assignRole('admin');
        }
    }
} 