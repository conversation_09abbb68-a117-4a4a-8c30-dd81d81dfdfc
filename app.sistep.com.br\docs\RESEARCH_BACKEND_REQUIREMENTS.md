# Requisitos do Backend para Integração da Pesquisa SISTEP

## Visão Geral

Este documento descreve os requisitos do backend necessários para integrar o fluxo de pesquisa entre o frontend Nuxt (`sistep.com.br`) e o painel administrativo Laravel (`app.sistep.com.br`).

## Fluxo da Pesquisa

1. **Pesquisa Page** (`sistep.com.br/pesquisa`) - Gerencia o fluxo da pesquisa
2. **DASS-21 Apply** (`sistep.com.br/DASS-21/apply`) - Usuário responde como paciente
3. **Admin Panel** (`app.sistep.com.br`) - Usuário vê resultados como psicólogo
4. **Google Forms** - Questionários SUS/UEQ finais

## Requisitos do Backend Laravel

### 1. Controlador de Pesquisa

Criar `App\Http\Controllers\ResearchController` com os seguintes métodos:

```php
<?php

namespace App\Http\Controllers;

class ResearchController extends Controller
{
    /**
     * Salva resultados do teste DASS-21 para participante da pesquisa
     * POST /research/test-results
     */
    public function saveTestResults(Request $request)
    {
        // Validar dados recebidos
        $validated = $request->validate([
            'participant_id' => 'required|string',
            'test_type' => 'required|string',
            'results' => 'required|array',
            'answers' => 'required|array',
        ]);

        // Salvar no banco de dados
        // Criar tabela research_participants se necessário
        
        return response()->json(['success' => true]);
    }

    /**
     * Exibe análise dos resultados no contexto profissional
     * GET /research/participant/analysis
     */
    public function showParticipantAnalysis(Request $request)
    {
        $participantId = $request->query('participant_id');
        $returnUrl = $request->query('research_return_url');
        
        // Buscar dados do participante
        $participantData = $this->getParticipantData($participantId);
        
        return Inertia::render('ResearchAnalysis', [
            'participant_id' => $participantId,
            'return_url' => $returnUrl,
            'test_results' => $participantData,
            'research_context' => true
        ]);
    }

    /**
     * Busca resultados de um participante específico
     * GET /research/participant/{participantId}/results
     */
    public function getParticipantResults($participantId)
    {
        // Implementar busca no banco
        return response()->json($results);
    }
}
```

### 2. Migrations Necessárias

```php
// Migration: create_research_participants_table
Schema::create('research_participants', function (Blueprint $table) {
    $table->id();
    $table->string('participant_id')->unique();
    $table->json('test_results')->nullable();
    $table->json('detailed_answers')->nullable();
    $table->string('test_type')->default('DASS-21');
    $table->timestamp('test_completed_at')->nullable();
    $table->timestamps();
});
```

### 3. Rotas Atualizadas

No arquivo `routes/web.php`, implementar as rotas comentadas:

```php
Route::prefix('research')->name('research.')->group(function () {
    Route::get('/participant/analysis', [ResearchController::class, 'showParticipantAnalysis'])
        ->name('participant.analysis');
    
    Route::post('/test-results', [ResearchController::class, 'saveTestResults'])
        ->name('test-results.store');
    
    Route::get('/participant/{participantId}/results', [ResearchController::class, 'getParticipantResults'])
        ->name('participant.results');
});
```

### 4. Componente Vue para Análise

Criar `resources/js/pages/ResearchAnalysis.vue`:

```vue
<template>
    <AppLayout>
        <!-- Indicador de contexto de pesquisa -->
        <div class="research-context-banner bg-blue-100 border-l-4 border-blue-500 p-4 mb-6">
            <h3 class="text-blue-800 font-semibold">Contexto da Pesquisa - Visão do Psicólogo</h3>
            <p class="text-blue-700">Você está visualizando como um psicólogo analisaria estes resultados.</p>
        </div>

        <!-- Resultados do DASS-21 com análise profissional -->
        <div class="professional-analysis">
            <!-- Implementar visualização profissional dos resultados -->
        </div>

        <!-- Botão para continuar pesquisa -->
        <div class="mt-6">
            <Button @click="proceedToNextStep">
                Continuar para Questionários da Pesquisa
            </Button>
        </div>
    </AppLayout>
</template>

<script setup>
// Implementar lógica do componente
</script>
```

## Requisitos do Frontend DASS-21

### 1. Integração da API

No arquivo `sistep.com.br/pages/DASS-21/apply.vue`, implementar chamada à API:

```javascript
async function proceedToPanel() {
  if (isResearchContext.value) {
    try {
      // Salvar resultados via API
      await $fetch('/api/research/test-results', {
        method: 'POST',
        body: {
          participant_id: route.query.participant_id,
          test_type: 'DASS-21',
          results: results.value,
          answers: answers.value
        }
      });
      
      // Redirecionar para painel
      const returnUrl = route.query.research_return_url;
      window.location.href = `${returnUrl}&participant_id=${route.query.participant_id}`;
    } catch (error) {
      console.error('Erro ao salvar resultados:', error);
    }
  }
}
```

### 2. Configuração da API

Adicionar proxy para API no `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  nitro: {
    devProxy: {
      '/api': {
        target: process.env.NODE_ENV === 'development' 
          ? 'https://app.sistep.com.br.local' 
          : 'https://app.sistep.com.br',
        changeOrigin: true,
        prependPath: true,
      }
    }
  }
})
```

## Checklist de Implementação

- [ ] Criar `ResearchController`
- [ ] Implementar migration `research_participants`
- [ ] Adicionar rotas de pesquisa
- [ ] Criar componente `ResearchAnalysis.vue`
- [ ] Configurar proxy da API no Nuxt
- [ ] Implementar salvamento de resultados
- [ ] Testar fluxo completo da pesquisa
- [ ] Adicionar logs e tratamento de erros
- [ ] Implementar backup dos dados de pesquisa

## Considerações de Segurança

1. **Anonimização**: Garantir que os participant_id não sejam associáveis a dados pessoais
2. **LGPD**: Implementar políticas de retenção de dados da pesquisa
3. **Validação**: Validar todos os dados recebidos do frontend
4. **Rate Limiting**: Implementar limitação de taxa para APIs de pesquisa

## Testes

1. **Teste unitário**: Testar salvamento e recuperação de dados
2. **Teste de integração**: Testar fluxo completo da pesquisa
3. **Teste de performance**: Validar tempo de resposta das APIs
4. **Teste de segurança**: Verificar anonimização dos dados 