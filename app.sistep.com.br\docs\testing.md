# Guia de Testes do SISTEP

Este documento descreve os testes automatizados implementados no projeto SISTEP.

## Configuração

O projeto utiliza PHPUnit integrado ao Laravel para testes automatizados. Os testes estão organizados em duas categorias principais:

1. **Tests\Unit**: Testes unitários que avaliam componentes isolados do sistema.
2. **Tests\Feature**: Testes de funcionalidades que avaliam o comportamento de componentes interconectados.

## Executando os Testes

Para executar todos os testes:

```bash
php artisan test
```

Para executar testes específicos:

```bash
php artisan test --filter=UserRolesTest
php artisan test --filter=TestResultTest
php artisan test --filter=SeederTest
```

## Categorias de Testes

### Testes de Usuários e Permissões

Arquivo: `tests/Feature/UserRolesTest.php`

Estes testes verificam:
- Criação de usuários com diferentes funções (admin, psicólogo, paciente)
- Atribuição correta de permissões
- Existência de usuários administradores fixos
- Personalização de dados para cada tipo de usuário

### Testes de Resultados de Testes Psicológicos

Arquivo: `tests/Feature/TestResultTest.php`

Estes testes verificam:
- Criação de resultados de testes DASS-21
- Criação de resultados de testes SUS
- Relacionamento entre usuários e resultados de testes
- Estrutura correta dos dados de resultados

### Testes de Seeders

Arquivo: `tests/Feature/SeederTest.php`

Estes testes verificam:
- Funcionamento do seeder de papéis e permissões
- Funcionamento do seeder de usuários
- Funcionamento do seeder de resultados de testes
- Integração entre todos os seeders

## Relatório de Cobertura

Para gerar um relatório de cobertura de código, execute:

```bash
XDEBUG_MODE=coverage php artisan test --coverage
```

O relatório será gerado em:
- HTML: `public/coverage/`
- Texto: `public/coverage.txt`

## Boas Práticas

1. **Sempre execute os testes antes de submeter alterações**:
   ```bash
   php artisan test
   ```

2. **Mantenha os testes atualizados** quando adicionar ou modificar funcionalidades.

3. **Use o banco de dados de teste** para não afetar os dados de desenvolvimento:
   ```php
   use RefreshDatabase;
   ```

4. **Isole os testes** para que possam ser executados em qualquer ordem.

5. **Documente os testes complexos** com comentários explicativos. 