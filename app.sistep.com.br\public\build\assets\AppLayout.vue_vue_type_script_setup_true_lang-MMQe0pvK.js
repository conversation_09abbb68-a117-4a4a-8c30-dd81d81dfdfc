import{S as M,n as W,c as I,a as g,B as X,o as Q,f as E,I as Y,$ as Z,P as J,X as ee,t as te,g as se,U as ae,q as oe,h as ne,H,b as K,i as re,z as le,k as de,K as ce,u as ie,l as ue,m as pe,p as fe,O as L,v as _e,r as me,s as he,w as ge,y as ye,x as be,A as ve,C as xe,D as we,_ as V}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{d as c,g as i,o as r,u as e,K as B,L as C,w as o,r as p,a as h,n as v,c as b,B as w,e as l,k as F,b as m,O as $e,F as S,y as A,x as z,h as D,t as k,P as O,A as ke,H as T,l as Be}from"./app-DIEHtcz0.js";import{a as Ce,u as ze,b as Se}from"./index-Cree0lnl.js";const De=c({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(a,{emit:t}){const d=M(a,t);return(u,_)=>(r(),i(e(W),B(C(e(d))),{default:o(()=>[p(u.$slots,"default")]),_:3},16))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=I("BookOpenIcon",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=I("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=I("FolderIcon",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=I("LayoutGridIcon",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=I("LogOutIcon",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=I("MenuIcon",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=I("SearchIcon",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=I("SettingsIcon",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=I("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ne=c({__name:"SheetHeader",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("div",{class:v(e(g)("flex flex-col gap-y-2 text-center sm:text-left",t.class))},[p(s.$slots,"default")],2))}}),je=c({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const t=a,s=b(()=>{const{class:n,...d}=t;return d});return(n,d)=>(r(),i(e(X),w({class:e(g)("text-lg font-semibold text-foreground",t.class)},s.value),{default:o(()=>[p(n.$slots,"default")]),_:3},16,["class"]))}}),He=c({__name:"SheetTrigger",props:{asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(Q),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}}),Ke=E("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Fe=c({inheritAttrs:!1,__name:"SheetContent",props:{class:{},side:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(a,{emit:t}){const s=a,n=t,d=b(()=>{const{class:_,side:y,...$}=s;return $}),u=M(d,n);return(_,y)=>(r(),i(e(Y),null,{default:o(()=>[l(e(Z),{class:"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"}),l(e(J),w({class:e(g)(e(Ke)({side:_.side}),s.class)},{...e(u),..._.$attrs}),{default:o(()=>[p(_.$slots,"default"),l(e(ee),{class:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary"},{default:o(()=>[l(e(Ee),{class:"h-4 w-4 text-muted-foreground"})]),_:1})]),_:3},16,["class"])]),_:3}))}}),Re="sidebar:state",qe=60*60*24*7,Ue="16rem",Ge="3rem",We="b",[ss,Xe]=te("Sidebar"),Qe=c({__name:"SidebarInset",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("main",{class:v(e(g)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t.class))},[p(s.$slots,"default")],2))}}),Ye=c({__name:"Tooltip",props:{defaultOpen:{type:Boolean},open:{type:Boolean},delayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},emits:["update:open"],setup(a,{emit:t}){const d=M(a,t);return(u,_)=>(r(),i(e(se),B(C(e(d))),{default:o(()=>[p(u.$slots,"default")]),_:3},16))}}),Ze=c({inheritAttrs:!1,__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},class:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(a,{emit:t}){const s=a,n=t,d=b(()=>{const{class:_,...y}=s;return y}),u=M(d,n);return(_,y)=>(r(),i(e(oe),null,{default:o(()=>[l(e(ae),w({...e(u),..._.$attrs},{class:e(g)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s.class)}),{default:o(()=>[p(_.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Je=c({__name:"TooltipTrigger",props:{asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(ne),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}}),et=c({__name:"SidebarProvider",props:{defaultOpen:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},class:{}},emits:["update:open"],setup(a,{emit:t}){const s=a,n=t,d=Ce("(max-width: 768px)"),u=F(!1),_=ze(s,"open",n,{defaultValue:s.defaultOpen??!1,passive:s.open===void 0});function y(x){_.value=x,document.cookie=`${Re}=${_.value}; path=/; max-age=${qe}`}function $(x){u.value=x}function P(){return d.value?$(!u.value):y(!_.value)}Se("keydown",x=>{x.key===We&&(x.metaKey||x.ctrlKey)&&(x.preventDefault(),P())});const f=b(()=>_.value?"expanded":"collapsed");return Xe({state:f,open:_,setOpen:y,isMobile:d,openMobile:u,setOpenMobile:$,toggleSidebar:P}),(x,Zt)=>(r(),i(e(H),{"delay-duration":0},{default:o(()=>[m("div",{style:$e({"--sidebar-width":e(Ue),"--sidebar-width-icon":e(Ge)}),class:v(e(g)("group/sidebar-wrapper flex min-h-svh w-full text-sidebar-foreground has-[[data-variant=inset]]:bg-sidebar",s.class))},[p(x.$slots,"default")],6)]),_:3}))}}),tt=c({__name:"AppContent",props:{variant:{},class:{}},setup(a){const t=a,s=b(()=>t.class);return(n,d)=>t.variant==="sidebar"?(r(),i(e(Qe),{key:0,class:v(s.value)},{default:o(()=>[p(n.$slots,"default")]),_:3},8,["class"])):(r(),h("main",{key:1,class:v(["mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",s.value])},[p(n.$slots,"default")],2))}}),st={class:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground"},at=c({__name:"AppLogo",setup(a){return(t,s)=>(r(),h(S,null,[m("div",st,[l(K,{class:"size-5 fill-current text-white dark:text-black"})]),s[0]||(s[0]=m("div",{class:"ml-1 grid flex-1 text-left text-sm"},[m("span",{class:"mb-0.5 truncate font-semibold leading-none"},"Laravel Starter Kit")],-1))],64))}}),ot=c({__name:"Breadcrumb",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("nav",{"aria-label":"breadcrumb",class:v(t.class)},[p(s.$slots,"default")],2))}}),nt=c({__name:"BreadcrumbItem",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("li",{class:v(e(g)("inline-flex items-center gap-1.5",t.class))},[p(s.$slots,"default")],2))}}),rt=c({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(re),{as:s.as,"as-child":s.asChild,class:v(e(g)("transition-colors hover:text-foreground",t.class))},{default:o(()=>[p(s.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),lt=c({__name:"BreadcrumbList",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("ol",{class:v(e(g)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t.class))},[p(s.$slots,"default")],2))}}),dt=c({__name:"BreadcrumbPage",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("span",{role:"link","aria-disabled":"true","aria-current":"page",class:v(e(g)("font-normal text-foreground",t.class))},[p(s.$slots,"default")],2))}}),ct=c({__name:"BreadcrumbSeparator",props:{class:{}},setup(a){const t=a;return(s,n)=>(r(),h("li",{role:"presentation","aria-hidden":"true",class:v(e(g)("[&>svg]:h-3.5 [&>svg]:w-3.5",t.class))},[p(s.$slots,"default",{},()=>[l(e(Me))])],2))}}),it=c({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(a){return(t,s)=>(r(),i(e(ot),null,{default:o(()=>[l(e(lt),null,{default:o(()=>[(r(!0),h(S,null,A(t.breadcrumbs,(n,d)=>(r(),h(S,{key:d},[l(e(nt),null,{default:o(()=>[d===t.breadcrumbs.length-1?(r(),i(e(dt),{key:0},{default:o(()=>[D(k(n.title),1)]),_:2},1024)):(r(),i(e(rt),{key:1,"as-child":""},{default:o(()=>[l(e(O),{href:n.href??"#"},{default:o(()=>[D(k(n.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),d!==t.breadcrumbs.length-1?(r(),i(e(ct),{key:0})):z("",!0)],64))),128))]),_:1})]),_:1}))}}),R=c({__name:"Avatar",props:{class:{},size:{default:"sm"},shape:{default:"circle"}},setup(a){const t=a;return(s,n)=>(r(),i(e(le),{class:v(e(g)(e(ut)({size:s.size,shape:s.shape}),t.class))},{default:o(()=>[p(s.$slots,"default")]),_:3},8,["class"]))}}),q=c({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(de),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}}),U=c({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(ce),w(t,{class:"h-full w-full object-cover"}),null,16))}}),ut=E("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{size:{sm:"h-10 w-10 text-xs",base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl"},shape:{circle:"rounded-full",square:"rounded-md"}}}),pt=c({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(a,{emit:t}){const d=M(a,t);return(u,_)=>(r(),i(e(ie),B(C(e(d))),{default:o(()=>[p(u.$slots,"default")]),_:3},16))}}),ft=c({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(a,{emit:t}){const s=a,n=t,d=b(()=>{const{class:_,...y}=s;return y}),u=M(d,n);return(_,y)=>(r(),i(e(ue),null,{default:o(()=>[l(e(pe),w(e(u),{class:e(g)("z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s.class)}),{default:o(()=>[p(_.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),_t=c({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(fe),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}}),N=c({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(a){const t=a,s=b(()=>{const{class:d,...u}=t;return u}),n=L(s);return(d,u)=>(r(),i(e(_e),w(e(n),{class:e(g)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",d.inset&&"pl-8",t.class)}),{default:o(()=>[p(d.$slots,"default")]),_:3},16,["class"]))}}),mt=c({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(a){const t=a,s=b(()=>{const{class:d,...u}=t;return u}),n=L(s);return(d,u)=>(r(),i(e(me),w(e(n),{class:e(g)("px-2 py-1.5 text-sm font-semibold",d.inset&&"pl-8",t.class)}),{default:o(()=>[p(d.$slots,"default")]),_:3},16,["class"]))}}),j=c({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const t=a,s=b(()=>{const{class:n,...d}=t;return d});return(n,d)=>(r(),i(e(he),w(s.value,{class:e(g)("-mx-1 my-1 h-px bg-muted",t.class)}),null,16,["class"]))}}),ht=c({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(a){const s=L(a);return(n,d)=>(r(),i(e(ge),w({class:"outline-none"},e(s)),{default:o(()=>[p(n.$slots,"default")]),_:3},16))}}),gt={class:"absolute left-0 top-full flex justify-center"},yt=c({__name:"NavigationMenuViewport",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const t=a,s=b(()=>{const{class:d,...u}=t;return u}),n=L(s);return(d,u)=>(r(),h("div",gt,[l(e(ye),w(e(n),{class:e(g)("origin-top-center relative mt-1.5 h-[--radix-navigation-menu-viewport-height] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[--radix-navigation-menu-viewport-width]",t.class)}),null,16,["class"])]))}}),bt=c({__name:"NavigationMenu",props:{modelValue:{},defaultValue:{},dir:{},orientation:{},delayDuration:{},skipDelayDuration:{},disableClickTrigger:{type:Boolean},disableHoverTrigger:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(a,{emit:t}){const s=a,n=t,d=b(()=>{const{class:_,...y}=s;return y}),u=M(d,n);return(_,y)=>(r(),i(e(be),w(e(u),{class:e(g)("relative z-10 flex max-w-max flex-1 items-center justify-center",s.class)}),{default:o(()=>[p(_.$slots,"default"),l(yt)]),_:3},16,["class"]))}}),vt=c({__name:"NavigationMenuItem",props:{value:{},asChild:{type:Boolean},as:{}},setup(a){const t=a;return(s,n)=>(r(),i(e(ve),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}}),xt=c({__name:"NavigationMenuLink",props:{active:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["select"],setup(a,{emit:t}){const d=M(a,t);return(u,_)=>(r(),i(e(xe),B(C(e(d))),{default:o(()=>[p(u.$slots,"default")]),_:3},16))}}),wt=c({__name:"NavigationMenuList",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const t=a,s=b(()=>{const{class:d,...u}=t;return u}),n=L(s);return(d,u)=>(r(),i(e(we),w(e(n),{class:e(g)("group flex flex-1 list-none items-center justify-center gap-x-1",t.class)}),{default:o(()=>[p(d.$slots,"default")]),_:3},16,["class"]))}}),$t=E("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),kt=c({__name:"TooltipProvider",props:{delayDuration:{},skipDelayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},setup(a){const t=a;return(s,n)=>(r(),i(e(H),B(C(t)),{default:o(()=>[p(s.$slots,"default")]),_:3},16))}});function G(a){if(!a)return"";const t=a.trim().split(" ");return t.length===0?"":t.length===1?t[0].charAt(0).toUpperCase():`${t[0].charAt(0)}${t[t.length-1].charAt(0)}`.toUpperCase()}function Bt(){return{getInitials:G}}const Ct={class:"grid flex-1 text-left text-sm leading-tight"},zt={class:"truncate font-medium"},St={key:0,class:"truncate text-xs text-muted-foreground"},Dt=c({__name:"UserInfo",props:{user:{},showEmail:{type:Boolean,default:!1}},setup(a){const t=a,{getInitials:s}=Bt(),n=b(()=>t.user.avatar&&t.user.avatar!=="");return(d,u)=>(r(),h(S,null,[l(e(R),{class:"h-8 w-8 overflow-hidden rounded-lg"},{default:o(()=>[n.value?(r(),i(e(U),{key:0,src:d.user.avatar,alt:d.user.name},null,8,["src","alt"])):z("",!0),l(e(q),{class:"rounded-lg text-black dark:text-white"},{default:o(()=>[D(k(e(s)(d.user.name)),1)]),_:1})]),_:1}),m("div",Ct,[m("span",zt,k(d.user.name),1),d.showEmail?(r(),h("span",St,k(d.user.email),1)):z("",!0)])],64))}}),It={class:"flex items-center gap-2 px-1 py-1.5 text-left text-sm"},Mt=c({__name:"UserMenuContent",props:{user:{}},setup(a){return(t,s)=>(r(),h(S,null,[l(e(mt),{class:"p-0 font-normal"},{default:o(()=>[m("div",It,[l(Dt,{user:t.user,"show-email":!0},null,8,["user"])])]),_:1}),l(e(j)),l(e(_t),null,{default:o(()=>[l(e(N),{"as-child":!0},{default:o(()=>[l(e(O),{class:"block w-full",href:t.route("profile.edit"),as:"button"},{default:o(()=>[l(e(Te),{class:"mr-2 h-4 w-4"}),s[0]||(s[0]=D(" Settings "))]),_:1},8,["href"])]),_:1})]),_:1}),l(e(j)),l(e(N),{"as-child":!0},{default:o(()=>[l(e(O),{class:"block w-full",method:"post",href:t.route("logout"),as:"button"},{default:o(()=>[l(e(Ae),{class:"mr-2 h-4 w-4"}),s[1]||(s[1]=D(" Log out "))]),_:1},8,["href"])]),_:1})],64))}}),Ot={class:"border-b border-sidebar-border/80"},Pt={class:"mx-auto flex h-16 items-center px-4 md:max-w-7xl"},At={class:"lg:hidden"},Lt={class:"flex h-full flex-1 flex-col justify-between space-y-4 py-6"},Vt={class:"-mx-3 space-y-1"},Tt={class:"flex flex-col space-y-4"},Et=["href"],Nt={class:"hidden h-full lg:flex lg:flex-1"},jt={key:0,class:"absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"},Ht={class:"ml-auto flex items-center space-x-2"},Kt={class:"relative flex items-center space-x-1"},Ft={class:"hidden space-x-1 lg:flex"},Rt=["href"],qt={class:"sr-only"},Ut={key:0,class:"flex w-full border-b border-sidebar-border/70"},Gt={class:"mx-auto flex h-12 w-full items-center justify-start px-4 text-neutral-500 md:max-w-7xl"},Wt=c({__name:"AppHeader",props:{breadcrumbs:{default:()=>[]}},setup(a){const t=a,s=ke(),n=b(()=>s.props.auth),d=b(()=>$=>s.url===$),u=b(()=>$=>d.value($)?"text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100":""),_=[{title:"Dashboard",href:"/dashboard",icon:Pe}],y=[{title:"Repository",href:"https://github.com/laravel/vue-starter-kit",icon:Oe},{title:"Documentation",href:"https://laravel.com/docs/starter-kits",icon:Ie}];return($,P)=>(r(),h("div",null,[m("div",Ot,[m("div",Pt,[m("div",At,[l(e(De),null,{default:o(()=>[l(e(He),{"as-child":!0},{default:o(()=>[l(e(V),{variant:"ghost",size:"icon",class:"mr-2 h-9 w-9"},{default:o(()=>[l(e(Le),{class:"h-5 w-5"})]),_:1})]),_:1}),l(e(Fe),{side:"left",class:"w-[300px] p-6"},{default:o(()=>[l(e(je),{class:"sr-only"},{default:o(()=>P[0]||(P[0]=[D("Navigation Menu")])),_:1}),l(e(Ne),{class:"flex justify-start text-left"},{default:o(()=>[l(K,{class:"size-6 fill-current text-black dark:text-white"})]),_:1}),m("div",Lt,[m("nav",Vt,[(r(),h(S,null,A(_,f=>l(e(O),{key:f.title,href:f.href,class:v(["flex items-center gap-x-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent",u.value(f.href)])},{default:o(()=>[f.icon?(r(),i(T(f.icon),{key:0,class:"h-5 w-5"})):z("",!0),D(" "+k(f.title),1)]),_:2},1032,["href","class"])),64))]),m("div",Tt,[(r(),h(S,null,A(y,f=>m("a",{key:f.title,href:f.href,target:"_blank",rel:"noopener noreferrer",class:"flex items-center space-x-2 text-sm font-medium"},[f.icon?(r(),i(T(f.icon),{key:0,class:"h-5 w-5"})):z("",!0),m("span",null,k(f.title),1)],8,Et)),64))])])]),_:1})]),_:1})]),l(e(O),{href:$.route("dashboard"),class:"flex items-center gap-x-2"},{default:o(()=>[l(at)]),_:1},8,["href"]),m("div",Nt,[l(e(bt),{class:"ml-10 flex h-full items-stretch"},{default:o(()=>[l(e(wt),{class:"flex h-full items-stretch space-x-2"},{default:o(()=>[(r(),h(S,null,A(_,(f,x)=>l(e(vt),{key:x,class:"relative flex h-full items-center"},{default:o(()=>[l(e(O),{href:f.href},{default:o(()=>[l(e(xt),{class:v([e($t)(),u.value(f.href),"h-9 cursor-pointer px-3"])},{default:o(()=>[f.icon?(r(),i(T(f.icon),{key:0,class:"mr-2 h-4 w-4"})):z("",!0),D(" "+k(f.title),1)]),_:2},1032,["class"])]),_:2},1032,["href"]),d.value(f.href)?(r(),h("div",jt)):z("",!0)]),_:2},1024)),64))]),_:1})]),_:1})]),m("div",Ht,[m("div",Kt,[l(e(V),{variant:"ghost",size:"icon",class:"group h-9 w-9 cursor-pointer"},{default:o(()=>[l(e(Ve),{class:"size-5 opacity-80 group-hover:opacity-100"})]),_:1}),m("div",Ft,[(r(),h(S,null,A(y,f=>l(e(kt),{key:f.title,"delay-duration":0},{default:o(()=>[l(e(Ye),null,{default:o(()=>[l(e(Je),null,{default:o(()=>[l(e(V),{variant:"ghost",size:"icon","as-child":"",class:"group h-9 w-9 cursor-pointer"},{default:o(()=>[m("a",{href:f.href,target:"_blank",rel:"noopener noreferrer"},[m("span",qt,k(f.title),1),(r(),i(T(f.icon),{class:"size-5 opacity-80 group-hover:opacity-100"}))],8,Rt)]),_:2},1024)]),_:2},1024),l(e(Ze),null,{default:o(()=>[m("p",null,k(f.title),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)),64))])]),l(e(pt),null,{default:o(()=>[l(e(ht),{"as-child":!0},{default:o(()=>[l(e(V),{variant:"ghost",size:"icon",class:"relative size-10 w-auto rounded-full p-1 focus-within:ring-2 focus-within:ring-primary"},{default:o(()=>[l(e(R),{class:"size-8 overflow-hidden rounded-full"},{default:o(()=>[n.value.user.avatar?(r(),i(e(U),{key:0,src:n.value.user.avatar,alt:n.value.user.name},null,8,["src","alt"])):z("",!0),l(e(q),{class:"rounded-lg bg-neutral-200 font-semibold text-black dark:bg-neutral-700 dark:text-white"},{default:o(()=>{var f;return[D(k(e(G)((f=n.value.user)==null?void 0:f.name)),1)]}),_:1})]),_:1})]),_:1})]),_:1}),l(e(ft),{align:"end",class:"w-56"},{default:o(()=>[l(Mt,{user:n.value.user},null,8,["user"])]),_:1})]),_:1})])])]),t.breadcrumbs.length>1?(r(),h("div",Ut,[m("div",Gt,[l(it,{breadcrumbs:$.breadcrumbs},null,8,["breadcrumbs"])])])):z("",!0)]))}}),Xt={key:0,class:"flex min-h-screen w-full flex-col"},Qt=c({__name:"AppShell",props:{variant:{}},setup(a){const t=F(!0);Be(()=>{t.value=localStorage.getItem("sidebar")!=="false"});const s=n=>{t.value=n,localStorage.setItem("sidebar",String(n))};return(n,d)=>n.variant==="header"?(r(),h("div",Xt,[p(n.$slots,"default")])):(r(),i(e(et),{key:1,"default-open":t.value,open:t.value,"onUpdate:open":s},{default:o(()=>[p(n.$slots,"default")]),_:3},8,["default-open","open"]))}}),Yt=c({__name:"AppHeaderLayout",props:{breadcrumbs:{default:()=>[]}},setup(a){return(t,s)=>(r(),i(Qt,{class:"flex-col"},{default:o(()=>[l(Wt,{breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"]),l(tt,null,{default:o(()=>[p(t.$slots,"default")]),_:3})]),_:3}))}}),as=c({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(a){return(t,s)=>(r(),i(Yt,{breadcrumbs:t.breadcrumbs},{default:o(()=>[p(t.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{Ee as X,as as _};
