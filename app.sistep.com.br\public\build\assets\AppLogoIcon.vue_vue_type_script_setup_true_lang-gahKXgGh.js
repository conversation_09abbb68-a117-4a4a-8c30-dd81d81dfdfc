import{c as D,k as O,V as Cn,q as j,Q as On,R as En,Y as Se,u as f,d as E,g as k,o as A,w as C,r as B,K as te,L as ie,B as I,Z as ye,l as le,x as st,e as U,S as Te,i as Bo,_ as _n,$ as fe,j as An,p as Pn,s as Q,a0 as Rt,D as ko,J as To,N as Do,a as ot,z as $t,F as Bn,a1 as kn,a2 as Mo,a3 as nn,a4 as So,T as Ro,a5 as Tn,a6 as $o,H as Io,a7 as No,a8 as Lo,a9 as Fo,aa as ut,W as ne,ab as zo,h as Vo,t as Wo,ac as Dn,v as jo,ad as Mn,O as Sn,ae as Rn,af as Ko,ag as Ho,n as Go,b as Uo}from"./app-DIEHtcz0.js";function $n(e){var t,n,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=$n(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function In(){for(var e,t,n=0,o="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=$n(e))&&(o&&(o+=" "),o+=t);return o}const on=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,rn=In,qo=(e,t)=>n=>{var o;if((t==null?void 0:t.variants)==null)return rn(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:r,defaultVariants:a}=t,i=Object.keys(r).map(c=>{const s=n==null?void 0:n[c],d=a==null?void 0:a[c];if(s===null)return null;const p=on(s)||on(d);return r[c][p]}),l=n&&Object.entries(n).reduce((c,s)=>{let[d,p]=s;return p===void 0||(c[d]=p),c},{}),u=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((c,s)=>{let{class:d,className:p,...v}=s;return Object.entries(v).every(g=>{let[h,m]=g;return Array.isArray(m)?m.includes({...a,...l}[h]):{...a,...l}[h]===m})?[...c,d,p]:c},[]);return rn(e,i,u,n==null?void 0:n.class,n==null?void 0:n.className)},Xo=["top","right","bottom","left"],_e=Math.min,J=Math.max,rt=Math.round,Je=Math.floor,se=e=>({x:e,y:e}),Yo={left:"right",right:"left",bottom:"top",top:"bottom"},Zo={start:"end",end:"start"};function Et(e,t,n){return J(e,_e(t,n))}function be(e,t){return typeof e=="function"?e(t):e}function we(e){return e.split("-")[0]}function ze(e){return e.split("-")[1]}function It(e){return e==="x"?"y":"x"}function Nt(e){return e==="y"?"height":"width"}function Ae(e){return["top","bottom"].includes(we(e))?"y":"x"}function Lt(e){return It(Ae(e))}function Jo(e,t,n){n===void 0&&(n=!1);const o=ze(e),r=Lt(e),a=Nt(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=at(i)),[i,at(i)]}function Qo(e){const t=at(e);return[_t(e),t,_t(t)]}function _t(e){return e.replace(/start|end/g,t=>Zo[t])}function er(e,t,n){const o=["left","right"],r=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?a:i;default:return[]}}function tr(e,t,n,o){const r=ze(e);let a=er(we(e),n==="start",o);return r&&(a=a.map(i=>i+"-"+r),t&&(a=a.concat(a.map(_t)))),a}function at(e){return e.replace(/left|right|bottom|top/g,t=>Yo[t])}function nr(e){return{top:0,right:0,bottom:0,left:0,...e}}function Nn(e){return typeof e!="number"?nr(e):{top:e,right:e,bottom:e,left:e}}function it(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function an(e,t,n){let{reference:o,floating:r}=e;const a=Ae(t),i=Lt(t),l=Nt(i),u=we(t),c=a==="y",s=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,p=o[l]/2-r[l]/2;let v;switch(u){case"top":v={x:s,y:o.y-r.height};break;case"bottom":v={x:s,y:o.y+o.height};break;case"right":v={x:o.x+o.width,y:d};break;case"left":v={x:o.x-r.width,y:d};break;default:v={x:o.x,y:o.y}}switch(ze(t)){case"start":v[i]-=p*(n&&c?-1:1);break;case"end":v[i]+=p*(n&&c?-1:1);break}return v}const or=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:a=[],platform:i}=n,l=a.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:s,y:d}=an(c,o,u),p=o,v={},g=0;for(let h=0;h<l.length;h++){const{name:m,fn:y}=l[h],{x:w,y:b,data:P,reset:T}=await y({x:s,y:d,initialPlacement:o,placement:p,strategy:r,middlewareData:v,rects:c,platform:i,elements:{reference:e,floating:t}});s=w??s,d=b??d,v={...v,[m]:{...v[m],...P}},T&&g<=50&&(g++,typeof T=="object"&&(T.placement&&(p=T.placement),T.rects&&(c=T.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):T.rects),{x:s,y:d}=an(c,p,u)),h=-1)}return{x:s,y:d,placement:p,strategy:r,middlewareData:v}};async function Ge(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:a,rects:i,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:v=0}=be(t,e),g=Nn(v),m=l[p?d==="floating"?"reference":"floating":d],y=it(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(m)))==null||n?m:m.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),w=d==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,b=await(a.getOffsetParent==null?void 0:a.getOffsetParent(l.floating)),P=await(a.isElement==null?void 0:a.isElement(b))?await(a.getScale==null?void 0:a.getScale(b))||{x:1,y:1}:{x:1,y:1},T=it(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-T.top+g.top)/P.y,bottom:(T.bottom-y.bottom+g.bottom)/P.y,left:(y.left-T.left+g.left)/P.x,right:(T.right-y.right+g.right)/P.x}}const rr=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:a,platform:i,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=be(e,t)||{};if(c==null)return{};const d=Nn(s),p={x:n,y:o},v=Lt(r),g=Nt(v),h=await i.getDimensions(c),m=v==="y",y=m?"top":"left",w=m?"bottom":"right",b=m?"clientHeight":"clientWidth",P=a.reference[g]+a.reference[v]-p[v]-a.floating[g],T=p[v]-a.reference[v],F=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let S=F?F[b]:0;(!S||!await(i.isElement==null?void 0:i.isElement(F)))&&(S=l.floating[b]||a.floating[g]);const z=P/2-T/2,W=S/2-h[g]/2-1,x=_e(d[y],W),M=_e(d[w],W),_=x,K=S-h[g]-M,N=S/2-h[g]/2+z,X=Et(_,N,K),H=!u.arrow&&ze(r)!=null&&N!==X&&a.reference[g]/2-(N<_?x:M)-h[g]/2<0,Z=H?N<_?N-_:N-K:0;return{[v]:p[v]+Z,data:{[v]:X,centerOffset:N-X-Z,...H&&{alignmentOffset:Z}},reset:H}}}),ar=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:a,rects:i,initialPlacement:l,platform:u,elements:c}=t,{mainAxis:s=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,...m}=be(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const y=we(r),w=Ae(l),b=we(l)===l,P=await(u.isRTL==null?void 0:u.isRTL(c.floating)),T=p||(b||!h?[at(l)]:Qo(l)),F=g!=="none";!p&&F&&T.push(...tr(l,h,g,P));const S=[l,...T],z=await Ge(t,m),W=[];let x=((o=a.flip)==null?void 0:o.overflows)||[];if(s&&W.push(z[y]),d){const N=Jo(r,i,P);W.push(z[N[0]],z[N[1]])}if(x=[...x,{placement:r,overflows:W}],!W.every(N=>N<=0)){var M,_;const N=(((M=a.flip)==null?void 0:M.index)||0)+1,X=S[N];if(X)return{data:{index:N,overflows:x},reset:{placement:X}};let H=(_=x.filter(Z=>Z.overflows[0]<=0).sort((Z,oe)=>Z.overflows[1]-oe.overflows[1])[0])==null?void 0:_.placement;if(!H)switch(v){case"bestFit":{var K;const Z=(K=x.filter(oe=>{if(F){const Y=Ae(oe.placement);return Y===w||Y==="y"}return!0}).map(oe=>[oe.placement,oe.overflows.filter(Y=>Y>0).reduce((Y,Ze)=>Y+Ze,0)]).sort((oe,Y)=>oe[1]-Y[1])[0])==null?void 0:K[0];Z&&(H=Z);break}case"initialPlacement":H=l;break}if(r!==H)return{reset:{placement:H}}}return{}}}};function ln(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function sn(e){return Xo.some(t=>e[t]>=0)}const ir=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=be(e,t);switch(o){case"referenceHidden":{const a=await Ge(t,{...r,elementContext:"reference"}),i=ln(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:sn(i)}}}case"escaped":{const a=await Ge(t,{...r,altBoundary:!0}),i=ln(a,n.floating);return{data:{escapedOffsets:i,escaped:sn(i)}}}default:return{}}}}};async function lr(e,t){const{placement:n,platform:o,elements:r}=e,a=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=we(n),l=ze(n),u=Ae(n)==="y",c=["left","top"].includes(i)?-1:1,s=a&&u?-1:1,d=be(t,e);let{mainAxis:p,crossAxis:v,alignmentAxis:g}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof g=="number"&&(v=l==="end"?g*-1:g),u?{x:v*s,y:p*c}:{x:p*c,y:v*s}}const sr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:a,placement:i,middlewareData:l}=t,u=await lr(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+u.x,y:a+u.y,data:{...u,placement:i}}}}},ur=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:l={fn:m=>{let{x:y,y:w}=m;return{x:y,y:w}}},...u}=be(e,t),c={x:n,y:o},s=await Ge(t,u),d=Ae(we(r)),p=It(d);let v=c[p],g=c[d];if(a){const m=p==="y"?"top":"left",y=p==="y"?"bottom":"right",w=v+s[m],b=v-s[y];v=Et(w,v,b)}if(i){const m=d==="y"?"top":"left",y=d==="y"?"bottom":"right",w=g+s[m],b=g-s[y];g=Et(w,g,b)}const h=l.fn({...t,[p]:v,[d]:g});return{...h,data:{x:h.x-n,y:h.y-o,enabled:{[p]:a,[d]:i}}}}}},dr=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:a,middlewareData:i}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=be(e,t),s={x:n,y:o},d=Ae(r),p=It(d);let v=s[p],g=s[d];const h=be(l,t),m=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){const b=p==="y"?"height":"width",P=a.reference[p]-a.floating[b]+m.mainAxis,T=a.reference[p]+a.reference[b]-m.mainAxis;v<P?v=P:v>T&&(v=T)}if(c){var y,w;const b=p==="y"?"width":"height",P=["top","left"].includes(we(r)),T=a.reference[d]-a.floating[b]+(P&&((y=i.offset)==null?void 0:y[d])||0)+(P?0:m.crossAxis),F=a.reference[d]+a.reference[b]+(P?0:((w=i.offset)==null?void 0:w[d])||0)-(P?m.crossAxis:0);g<T?g=T:g>F&&(g=F)}return{[p]:v,[d]:g}}}},cr=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:a,platform:i,elements:l}=t,{apply:u=()=>{},...c}=be(e,t),s=await Ge(t,c),d=we(r),p=ze(r),v=Ae(r)==="y",{width:g,height:h}=a.floating;let m,y;d==="top"||d==="bottom"?(m=d,y=p===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(y=d,m=p==="end"?"top":"bottom");const w=h-s.top-s.bottom,b=g-s.left-s.right,P=_e(h-s[m],w),T=_e(g-s[y],b),F=!t.middlewareData.shift;let S=P,z=T;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(z=b),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(S=w),F&&!p){const x=J(s.left,0),M=J(s.right,0),_=J(s.top,0),K=J(s.bottom,0);v?z=g-2*(x!==0||M!==0?x+M:J(s.left,s.right)):S=h-2*(_!==0||K!==0?_+K:J(s.top,s.bottom))}await u({...t,availableWidth:z,availableHeight:S});const W=await i.getDimensions(l.floating);return g!==W.width||h!==W.height?{reset:{rects:!0}}:{}}}};function dt(){return typeof window<"u"}function De(e){return Ft(e)?(e.nodeName||"").toLowerCase():"#document"}function ee(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function pe(e){var t;return(t=(Ft(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ft(e){return dt()?e instanceof Node||e instanceof ee(e).Node:!1}function re(e){return dt()?e instanceof Element||e instanceof ee(e).Element:!1}function de(e){return dt()?e instanceof HTMLElement||e instanceof ee(e).HTMLElement:!1}function un(e){return!dt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ee(e).ShadowRoot}function qe(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ae(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function fr(e){return["table","td","th"].includes(De(e))}function ct(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function zt(e){const t=Vt(),n=re(e)?ae(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function pr(e){let t=Pe(e);for(;de(t)&&!Le(t);){if(zt(t))return t;if(ct(t))return null;t=Pe(t)}return null}function Vt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Le(e){return["html","body","#document"].includes(De(e))}function ae(e){return ee(e).getComputedStyle(e)}function ft(e){return re(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Pe(e){if(De(e)==="html")return e;const t=e.assignedSlot||e.parentNode||un(e)&&e.host||pe(e);return un(t)?t.host:t}function Ln(e){const t=Pe(e);return Le(t)?e.ownerDocument?e.ownerDocument.body:e.body:de(t)&&qe(t)?t:Ln(t)}function Ue(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Ln(e),a=r===((o=e.ownerDocument)==null?void 0:o.body),i=ee(r);if(a){const l=At(i);return t.concat(i,i.visualViewport||[],qe(r)?r:[],l&&n?Ue(l):[])}return t.concat(r,Ue(r,[],n))}function At(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Fn(e){const t=ae(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=de(e),a=r?e.offsetWidth:n,i=r?e.offsetHeight:o,l=rt(n)!==a||rt(o)!==i;return l&&(n=a,o=i),{width:n,height:o,$:l}}function Wt(e){return re(e)?e:e.contextElement}function Ie(e){const t=Wt(e);if(!de(t))return se(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:a}=Fn(t);let i=(a?rt(n.width):n.width)/o,l=(a?rt(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const vr=se(0);function zn(e){const t=ee(e);return!Vt()||!t.visualViewport?vr:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function mr(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==ee(e)?!1:t}function ke(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),a=Wt(e);let i=se(1);t&&(o?re(o)&&(i=Ie(o)):i=Ie(e));const l=mr(a,n,o)?zn(a):se(0);let u=(r.left+l.x)/i.x,c=(r.top+l.y)/i.y,s=r.width/i.x,d=r.height/i.y;if(a){const p=ee(a),v=o&&re(o)?ee(o):o;let g=p,h=At(g);for(;h&&o&&v!==g;){const m=Ie(h),y=h.getBoundingClientRect(),w=ae(h),b=y.left+(h.clientLeft+parseFloat(w.paddingLeft))*m.x,P=y.top+(h.clientTop+parseFloat(w.paddingTop))*m.y;u*=m.x,c*=m.y,s*=m.x,d*=m.y,u+=b,c+=P,g=ee(h),h=At(g)}}return it({width:s,height:d,x:u,y:c})}function jt(e,t){const n=ft(e).scrollLeft;return t?t.left+n:ke(pe(e)).left+n}function Vn(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:jt(e,o)),a=o.top+t.scrollTop;return{x:r,y:a}}function gr(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const a=r==="fixed",i=pe(o),l=t?ct(t.floating):!1;if(o===i||l&&a)return n;let u={scrollLeft:0,scrollTop:0},c=se(1);const s=se(0),d=de(o);if((d||!d&&!a)&&((De(o)!=="body"||qe(i))&&(u=ft(o)),de(o))){const v=ke(o);c=Ie(o),s.x=v.x+o.clientLeft,s.y=v.y+o.clientTop}const p=i&&!d&&!a?Vn(i,u,!0):se(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-u.scrollTop*c.y+s.y+p.y}}function hr(e){return Array.from(e.getClientRects())}function yr(e){const t=pe(e),n=ft(e),o=e.ownerDocument.body,r=J(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),a=J(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+jt(e);const l=-n.scrollTop;return ae(o).direction==="rtl"&&(i+=J(t.clientWidth,o.clientWidth)-r),{width:r,height:a,x:i,y:l}}function br(e,t){const n=ee(e),o=pe(e),r=n.visualViewport;let a=o.clientWidth,i=o.clientHeight,l=0,u=0;if(r){a=r.width,i=r.height;const c=Vt();(!c||c&&t==="fixed")&&(l=r.offsetLeft,u=r.offsetTop)}return{width:a,height:i,x:l,y:u}}function wr(e,t){const n=ke(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,a=de(e)?Ie(e):se(1),i=e.clientWidth*a.x,l=e.clientHeight*a.y,u=r*a.x,c=o*a.y;return{width:i,height:l,x:u,y:c}}function dn(e,t,n){let o;if(t==="viewport")o=br(e,n);else if(t==="document")o=yr(pe(e));else if(re(t))o=wr(t,n);else{const r=zn(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return it(o)}function Wn(e,t){const n=Pe(e);return n===t||!re(n)||Le(n)?!1:ae(n).position==="fixed"||Wn(n,t)}function xr(e,t){const n=t.get(e);if(n)return n;let o=Ue(e,[],!1).filter(l=>re(l)&&De(l)!=="body"),r=null;const a=ae(e).position==="fixed";let i=a?Pe(e):e;for(;re(i)&&!Le(i);){const l=ae(i),u=zt(i);!u&&l.position==="fixed"&&(r=null),(a?!u&&!r:!u&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||qe(i)&&!u&&Wn(e,i))?o=o.filter(s=>s!==i):r=l,i=Pe(i)}return t.set(e,o),o}function Cr(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?ct(t)?[]:xr(t,this._c):[].concat(n),o],l=i[0],u=i.reduce((c,s)=>{const d=dn(t,s,r);return c.top=J(d.top,c.top),c.right=_e(d.right,c.right),c.bottom=_e(d.bottom,c.bottom),c.left=J(d.left,c.left),c},dn(t,l,r));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Or(e){const{width:t,height:n}=Fn(e);return{width:t,height:n}}function Er(e,t,n){const o=de(t),r=pe(t),a=n==="fixed",i=ke(e,!0,a,t);let l={scrollLeft:0,scrollTop:0};const u=se(0);if(o||!o&&!a)if((De(t)!=="body"||qe(r))&&(l=ft(t)),o){const p=ke(t,!0,a,t);u.x=p.x+t.clientLeft,u.y=p.y+t.clientTop}else r&&(u.x=jt(r));const c=r&&!o&&!a?Vn(r,l):se(0),s=i.left+l.scrollLeft-u.x-c.x,d=i.top+l.scrollTop-u.y-c.y;return{x:s,y:d,width:i.width,height:i.height}}function ht(e){return ae(e).position==="static"}function cn(e,t){if(!de(e)||ae(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return pe(e)===n&&(n=n.ownerDocument.body),n}function jn(e,t){const n=ee(e);if(ct(e))return n;if(!de(e)){let r=Pe(e);for(;r&&!Le(r);){if(re(r)&&!ht(r))return r;r=Pe(r)}return n}let o=cn(e,t);for(;o&&fr(o)&&ht(o);)o=cn(o,t);return o&&Le(o)&&ht(o)&&!zt(o)?n:o||pr(e)||n}const _r=async function(e){const t=this.getOffsetParent||jn,n=this.getDimensions,o=await n(e.floating);return{reference:Er(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Ar(e){return ae(e).direction==="rtl"}const Pr={convertOffsetParentRelativeRectToViewportRelativeRect:gr,getDocumentElement:pe,getClippingRect:Cr,getOffsetParent:jn,getElementRects:_r,getClientRects:hr,getDimensions:Or,getScale:Ie,isElement:re,isRTL:Ar};function Kn(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Br(e,t){let n=null,o;const r=pe(e);function a(){var l;clearTimeout(o),(l=n)==null||l.disconnect(),n=null}function i(l,u){l===void 0&&(l=!1),u===void 0&&(u=1),a();const c=e.getBoundingClientRect(),{left:s,top:d,width:p,height:v}=c;if(l||t(),!p||!v)return;const g=Je(d),h=Je(r.clientWidth-(s+p)),m=Je(r.clientHeight-(d+v)),y=Je(s),b={rootMargin:-g+"px "+-h+"px "+-m+"px "+-y+"px",threshold:J(0,_e(1,u))||1};let P=!0;function T(F){const S=F[0].intersectionRatio;if(S!==u){if(!P)return i();S?i(!1,S):o=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!Kn(c,e.getBoundingClientRect())&&i(),P=!1}try{n=new IntersectionObserver(T,{...b,root:r.ownerDocument})}catch{n=new IntersectionObserver(T,b)}n.observe(e)}return i(!0),a}function kr(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:u=!1}=o,c=Wt(e),s=r||a?[...c?Ue(c):[],...Ue(t)]:[];s.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),a&&y.addEventListener("resize",n)});const d=c&&l?Br(c,n):null;let p=-1,v=null;i&&(v=new ResizeObserver(y=>{let[w]=y;w&&w.target===c&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=v)==null||b.observe(t)})),n()}),c&&!u&&v.observe(c),v.observe(t));let g,h=u?ke(e):null;u&&m();function m(){const y=ke(e);h&&!Kn(h,y)&&n(),h=y,g=requestAnimationFrame(m)}return n(),()=>{var y;s.forEach(w=>{r&&w.removeEventListener("scroll",n),a&&w.removeEventListener("resize",n)}),d==null||d(),(y=v)==null||y.disconnect(),v=null,u&&cancelAnimationFrame(g)}}const Tr=sr,Dr=ur,fn=ar,Mr=cr,Sr=ir,Rr=rr,$r=dr,Ir=(e,t,n)=>{const o=new Map,r={platform:Pr,...n},a={...r.platform,_c:o};return or(e,t,{...r,platform:a})};function Nr(e){return e!=null&&typeof e=="object"&&"$el"in e}function Pt(e){if(Nr(e)){const t=e.$el;return Ft(t)&&De(t)==="#comment"?null:t}return e}function $e(e){return typeof e=="function"?e():f(e)}function Lr(e){return{name:"arrow",options:e,fn(t){const n=Pt($e(e.element));return n==null?{}:Rr({element:n,padding:e.padding}).fn(t)}}}function Hn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function pn(e,t){const n=Hn(e);return Math.round(t*n)/n}function Fr(e,t,n){n===void 0&&(n={});const o=n.whileElementsMounted,r=D(()=>{var S;return(S=$e(n.open))!=null?S:!0}),a=D(()=>$e(n.middleware)),i=D(()=>{var S;return(S=$e(n.placement))!=null?S:"bottom"}),l=D(()=>{var S;return(S=$e(n.strategy))!=null?S:"absolute"}),u=D(()=>{var S;return(S=$e(n.transform))!=null?S:!0}),c=D(()=>Pt(e.value)),s=D(()=>Pt(t.value)),d=O(0),p=O(0),v=O(l.value),g=O(i.value),h=Cn({}),m=O(!1),y=D(()=>{const S={position:v.value,left:"0",top:"0"};if(!s.value)return S;const z=pn(s.value,d.value),W=pn(s.value,p.value);return u.value?{...S,transform:"translate("+z+"px, "+W+"px)",...Hn(s.value)>=1.5&&{willChange:"transform"}}:{position:v.value,left:z+"px",top:W+"px"}});let w;function b(){if(c.value==null||s.value==null)return;const S=r.value;Ir(c.value,s.value,{middleware:a.value,placement:i.value,strategy:l.value}).then(z=>{d.value=z.x,p.value=z.y,v.value=z.strategy,g.value=z.placement,h.value=z.middlewareData,m.value=S!==!1})}function P(){typeof w=="function"&&(w(),w=void 0)}function T(){if(P(),o===void 0){b();return}if(c.value!=null&&s.value!=null){w=o(c.value,s.value,b);return}}function F(){r.value||(m.value=!1)}return j([a,i,l,r],b,{flush:"sync"}),j([c,s],T,{flush:"sync"}),j(r,F,{flush:"sync"}),On()&&En(P),{x:Se(d),y:Se(p),strategy:Se(v),placement:Se(g),middlewareData:Se(h),isPositioned:Se(m),floatingStyles:y,update:b}}function q(e,t){const n=typeof e=="string"&&!t?`${e}Context`:t,o=Symbol(n);return[r=>{const a=An(o,r);if(a||a===null)return a;throw new Error(`Injection \`${o.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(", ")}`:`\`${e}\``}`)},r=>(Pn(o,r),r)]}function Gn(e,t,n){const o=n.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),o.dispatchEvent(r)}function zr(e,t){var n;const o=Cn();return ne(()=>{o.value=e()},{...t,flush:(n=void 0)!=null?n:"sync"}),Tn(o)}function Ve(e){return On()?(En(e),!0):!1}function Vr(){const e=new Set,t=n=>{e.delete(n)};return{on:n=>{e.add(n);const o=()=>t(n);return Ve(o),{off:o}},off:t,trigger:(...n)=>Promise.all(Array.from(e).map(o=>o(...n)))}}function Wr(e){let t=!1,n;const o=Dn(!0);return(...r)=>(t||(n=o.run(()=>e(...r)),t=!0),n)}function Un(e){let t=0,n,o;const r=()=>{t-=1,o&&t<=0&&(o.stop(),n=void 0,o=void 0)};return(...a)=>(t+=1,n||(o=Dn(!0),n=o.run(()=>e(...a))),Ve(r),n)}function ue(e){return typeof e=="function"?e():f(e)}const xe=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const jr=e=>typeof e<"u",Kr=Object.prototype.toString,Hr=e=>Kr.call(e)==="[object Object]",Bt=()=>{},vn=Gr();function Gr(){var e,t;return xe&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function Ur(e,t){function n(...o){return new Promise((r,a)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(r).catch(a)})}return n}function qr(e,t={}){let n,o,r=Bt;const a=i=>{clearTimeout(i),r(),r=Bt};return i=>{const l=ue(e),u=ue(t.maxWait);return n&&a(n),l<=0||u!==void 0&&u<=0?(o&&(a(o),o=null),Promise.resolve(i())):new Promise((c,s)=>{r=t.rejectOnCancel?s:c,u&&!o&&(o=setTimeout(()=>{n&&a(n),o=null,c(i())},u)),n=setTimeout(()=>{o&&a(o),o=null,c(i())},l)})}}function Xr(e){return Te()}function Kt(e,t=1e4){return Fo((n,o)=>{let r=ue(e),a;const i=()=>setTimeout(()=>{r=ue(e),o()},ue(t));return Ve(()=>{clearTimeout(a)}),{get(){return n(),r},set(l){r=l,o(),clearTimeout(a),a=i()}}})}function Yr(e,t=200,n={}){return Ur(qr(t,n),e)}function Zr(e,t){Xr()&&jo(e,t)}function qn(e,t,n={}){const{immediate:o=!0}=n,r=O(!1);let a=null;function i(){a&&(clearTimeout(a),a=null)}function l(){r.value=!1,i()}function u(...c){i(),r.value=!0,a=setTimeout(()=>{r.value=!1,a=null,e(...c)},ue(t))}return o&&(r.value=!0,xe&&u()),Ve(l),{isPending:Tn(r),start:u,stop:l}}function ce(e){var t;const n=ue(e);return(t=n==null?void 0:n.$el)!=null?t:n}const pt=xe?window:void 0;function Fe(...e){let t,n,o,r;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,r]=e,t=pt):[t,n,o,r]=e,!t)return Bt;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const a=[],i=()=>{a.forEach(s=>s()),a.length=0},l=(s,d,p,v)=>(s.addEventListener(d,p,v),()=>s.removeEventListener(d,p,v)),u=j(()=>[ce(t),ue(r)],([s,d])=>{if(i(),!s)return;const p=Hr(d)?{...d}:d;a.push(...n.flatMap(v=>o.map(g=>l(s,v,g,p))))},{immediate:!0,flush:"post"}),c=()=>{u(),i()};return Ve(c),c}function Jr(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function Qr(...e){let t,n,o={};e.length===3?(t=e[0],n=e[1],o=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:r=pt,eventName:a="keydown",passive:i=!1,dedupe:l=!1}=o,u=Jr(t);return Fe(r,a,c=>{c.repeat&&ue(l)||u(c)&&n(c)},i)}function Xn(){const e=O(!1),t=Te();return t&&le(()=>{e.value=!0},t),e}function ea(e){const t=Xn();return D(()=>(t.value,!!e()))}function ta(e){return JSON.parse(JSON.stringify(e))}function na(e,t,n={}){const{window:o=pt,...r}=n;let a;const i=ea(()=>o&&"ResizeObserver"in o),l=()=>{a&&(a.disconnect(),a=void 0)},u=D(()=>Array.isArray(e)?e.map(d=>ce(d)):[ce(e)]),c=j(u,d=>{if(l(),i.value&&o){a=new ResizeObserver(t);for(const p of d)p&&a.observe(p,r)}},{immediate:!0,flush:"post"}),s=()=>{l(),c()};return Ve(s),{isSupported:i,stop:s}}function Me(e,t,n,o={}){var r,a,i;const{clone:l=!1,passive:u=!1,eventName:c,deep:s=!1,defaultValue:d,shouldEmit:p}=o,v=Te(),g=n||(v==null?void 0:v.emit)||((r=v==null?void 0:v.$emit)==null?void 0:r.bind(v))||((i=(a=v==null?void 0:v.proxy)==null?void 0:a.$emit)==null?void 0:i.bind(v==null?void 0:v.proxy));let h=c;t||(t="modelValue"),h=h||`update:${t.toString()}`;const m=b=>l?typeof l=="function"?l(b):ta(b):b,y=()=>jr(e[t])?m(e[t]):d,w=b=>{p?p(b)&&g(h,b):g(h,b)};if(u){const b=y(),P=O(b);let T=!1;return j(()=>e[t],F=>{T||(T=!0,P.value=m(F),Q(()=>T=!1))}),j(P,F=>{!T&&(F!==e[t]||s)&&w(F)},{deep:s}),P}else return D({get(){return y()},set(b){w(b)}})}function Ht(e){return e?e.flatMap(t=>t.type===Bn?Ht(t.children):[t]):[]}function G(){let e=document.activeElement;if(e==null)return null;for(;e!=null&&e.shadowRoot!=null&&e.shadowRoot.activeElement!=null;)e=e.shadowRoot.activeElement;return e}const oa=["INPUT","TEXTAREA"];function Yn(e,t,n,o={}){if(!t||o.enableIgnoredElement&&oa.includes(t.nodeName))return null;const{arrowKeyOptions:r="both",attributeName:a="[data-radix-vue-collection-item]",itemsArray:i=[],loop:l=!0,dir:u="ltr",preventScroll:c=!0,focus:s=!1}=o,[d,p,v,g,h,m]=[e.key==="ArrowRight",e.key==="ArrowLeft",e.key==="ArrowUp",e.key==="ArrowDown",e.key==="Home",e.key==="End"],y=v||g,w=d||p;if(!h&&!m&&(!y&&!w||r==="vertical"&&w||r==="horizontal"&&y))return null;const b=n?Array.from(n.querySelectorAll(a)):i;if(!b.length)return null;c&&e.preventDefault();let P=null;return w||y?P=Zn(b,t,{goForward:y?g:u==="ltr"?d:p,loop:l}):h?P=b.at(0)||null:m&&(P=b.at(-1)||null),s&&(P==null||P.focus()),P}function Zn(e,t,n,o=e.length){if(--o===0)return null;const r=e.indexOf(t),a=n.goForward?r+1:r-1;if(!n.loop&&(a<0||a>=e.length))return null;const i=(a+e.length)%e.length,l=e[i];return l?l.hasAttribute("disabled")&&l.getAttribute("disabled")!=="false"?Zn(e,l,n,o):l:null}function yt(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function kt(e,t,n=".",o){if(!yt(t))return kt(e,{},n);const r=Object.assign({},t);for(const a in e){if(a==="__proto__"||a==="constructor")continue;const i=e[a];i!=null&&(Array.isArray(i)&&Array.isArray(r[a])?r[a]=[...i,...r[a]]:yt(i)&&yt(r[a])?r[a]=kt(i,r[a],(n?`${n}.`:"")+a.toString()):r[a]=i)}return r}function ra(e){return(...t)=>t.reduce((n,o)=>kt(n,o,""),{})}const aa=ra(),[Gt,Rl]=q("ConfigProvider");let ia="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",la=(e=21)=>{let t="",n=e;for(;n--;)t+=ia[Math.random()*64|0];return t};const sa=Un(()=>{const e=O(new Map),t=O(),n=D(()=>{for(const i of e.value.values())if(i)return!0;return!1}),o=Gt({scrollBody:O(!0)});let r=null;const a=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=t.value??"",vn&&(r==null||r()),t.value=void 0};return j(n,(i,l)=>{var u;if(!xe)return;if(!i){l&&a();return}t.value===void 0&&(t.value=document.body.style.overflow);const c=window.innerWidth-document.documentElement.clientWidth,s={padding:c,margin:0},d=(u=o.scrollBody)!=null&&u.value?typeof o.scrollBody.value=="object"?aa({padding:o.scrollBody.value.padding===!0?c:o.scrollBody.value.padding,margin:o.scrollBody.value.margin===!0?c:o.scrollBody.value.margin},s):s:{padding:0,margin:0};c>0&&(document.body.style.paddingRight=typeof d.padding=="number"?`${d.padding}px`:String(d.padding),document.body.style.marginRight=typeof d.margin=="number"?`${d.margin}px`:String(d.margin),document.body.style.setProperty("--scrollbar-width",`${c}px`),document.body.style.overflow="hidden"),vn&&(r=Fe(document,"touchmove",p=>ua(p),{passive:!1})),Q(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function Jn(e){const t=la(6),n=sa();n.value.set(t,e??!1);const o=D({get:()=>n.value.get(t)??!1,set:r=>n.value.set(t,r)});return Zr(()=>{n.value.delete(t)}),o}function Qn(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:Qn(n)}}function ua(e){const t=e||window.event,n=t.target;return n instanceof Element&&Qn(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.cancelable&&t.preventDefault(),!1)}const da="data-radix-vue-collection-item";function Ut(e,t=da){const n=e??Symbol();return{createCollection:o=>{const r=O([]);function a(){const i=ce(o);return i?r.value=Array.from(i.querySelectorAll(`[${t}]:not([data-disabled])`)):r.value=[]}return No(()=>{r.value=[]}),le(a),Lo(a),j(()=>o==null?void 0:o.value,a,{immediate:!0}),Pn(n,r),r},injectCollection:()=>An(n,O([]))}}function vt(e){const t=Gt({dir:O("ltr")});return D(()=>{var n;return(e==null?void 0:e.value)||((n=t.dir)==null?void 0:n.value)||"ltr"})}function Xe(e){const t=Te(),n=t==null?void 0:t.type.emits,o={};return n!=null&&n.length||console.warn(`No emitted event found. Please check component: ${t==null?void 0:t.type.__name}`),n==null||n.forEach(r=>{o[So(_n(r))]=(...a)=>e(r,...a)}),o}let bt=0;function ca(){ne(e=>{if(!xe)return;const t=document.querySelectorAll("[data-radix-focus-guard]");document.body.insertAdjacentElement("afterbegin",t[0]??mn()),document.body.insertAdjacentElement("beforeend",t[1]??mn()),bt++,e(()=>{bt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(n=>n.remove()),bt--})})}function mn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function fa(e){return D(()=>{var t;return ue(e)?!!((t=ce(e))!=null&&t.closest("form")):!0})}function eo(e){const t=Te(),n=Object.keys((t==null?void 0:t.type.props)??{}).reduce((r,a)=>{const i=(t==null?void 0:t.type.props[a]).default;return i!==void 0&&(r[a]=i),r},{}),o=Bo(e);return D(()=>{const r={},a=(t==null?void 0:t.vnode.props)??{};return Object.keys(a).forEach(i=>{r[_n(i)]=a[i]}),Object.keys({...n,...r}).reduce((i,l)=>(o.value[l]!==void 0&&(i[l]=o.value[l]),i),{})})}function Ye(e,t){const n=eo(e),o=t?Xe(t):{};return D(()=>({...n.value,...o}))}function R(){const e=Te(),t=O(),n=D(()=>{var i,l;return["#text","#comment"].includes((i=t.value)==null?void 0:i.$el.nodeName)?(l=t.value)==null?void 0:l.$el.nextElementSibling:ce(t)}),o=Object.assign({},e.exposed),r={};for(const i in e.props)Object.defineProperty(r,i,{enumerable:!0,configurable:!0,get:()=>e.props[i]});if(Object.keys(o).length>0)for(const i in o)Object.defineProperty(r,i,{enumerable:!0,configurable:!0,get:()=>o[i]});Object.defineProperty(r,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=r;function a(i){t.value=i,i&&(Object.defineProperty(r,"$el",{enumerable:!0,configurable:!0,get:()=>i instanceof Element?i:i.$el}),e.exposed=r)}return{forwardRef:a,currentRef:t,currentElement:n}}function pa(e,t){const n=Kt(!1,300),o=O(null),r=Vr();function a(){o.value=null,n.value=!1}function i(l,u){const c=l.currentTarget,s={x:l.clientX,y:l.clientY},d=va(s,c.getBoundingClientRect()),p=ma(s,d),v=ga(u.getBoundingClientRect()),g=ya([...p,...v]);o.value=g,n.value=!0}return ne(l=>{if(e.value&&t.value){const u=s=>i(s,t.value),c=s=>i(s,e.value);e.value.addEventListener("pointerleave",u),t.value.addEventListener("pointerleave",c),l(()=>{var s,d;(s=e.value)==null||s.removeEventListener("pointerleave",u),(d=t.value)==null||d.removeEventListener("pointerleave",c)})}}),ne(l=>{var u;if(o.value){const c=s=>{var d,p;if(!o.value)return;const v=s.target,g={x:s.clientX,y:s.clientY},h=((d=e.value)==null?void 0:d.contains(v))||((p=t.value)==null?void 0:p.contains(v)),m=!ha(g,o.value),y=!!v.closest("[data-grace-area-trigger]");h?a():(m||y)&&(a(),r.trigger())};(u=e.value)==null||u.ownerDocument.addEventListener("pointermove",c),l(()=>{var s;return(s=e.value)==null?void 0:s.ownerDocument.removeEventListener("pointermove",c)})}}),{isPointerInTransit:n,onPointerExit:r.on}}function va(e,t){const n=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,o,r,a)){case a:return"left";case r:return"right";case n:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function ma(e,t,n=5){const o=[];switch(t){case"top":o.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":o.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":o.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":o.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return o}function ga(e){const{top:t,right:n,bottom:o,left:r}=e;return[{x:r,y:t},{x:n,y:t},{x:n,y:o},{x:r,y:o}]}function ha(e,t){const{x:n,y:o}=e;let r=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const l=t[a].x,u=t[a].y,c=t[i].x,s=t[i].y;u>o!=s>o&&n<(c-l)*(o-u)/(s-u)+l&&(r=!r)}return r}function ya(e){const t=e.slice();return t.sort((n,o)=>n.x<o.x?-1:n.x>o.x?1:n.y<o.y?-1:n.y>o.y?1:0),ba(t)}function ba(e){if(e.length<=1)return e.slice();const t=[];for(let o=0;o<e.length;o++){const r=e[o];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(r.y-i.y)>=(a.y-i.y)*(r.x-i.x))t.pop();else break}t.push(r)}t.pop();const n=[];for(let o=e.length-1;o>=0;o--){const r=e[o];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(r.y-i.y)>=(a.y-i.y)*(r.x-i.x))n.pop();else break}n.push(r)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var wa=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Re=new WeakMap,Qe=new WeakMap,et={},wt=0,to=function(e){return e&&(e.host||to(e.parentNode))},xa=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=to(n);return o&&e.contains(o)?o:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Ca=function(e,t,n,o){var r=xa(t,Array.isArray(e)?e:[e]);et[n]||(et[n]=new WeakMap);var a=et[n],i=[],l=new Set,u=new Set(r),c=function(d){!d||l.has(d)||(l.add(d),c(d.parentNode))};r.forEach(c);var s=function(d){!d||u.has(d)||Array.prototype.forEach.call(d.children,function(p){if(l.has(p))s(p);else try{var v=p.getAttribute(o),g=v!==null&&v!=="false",h=(Re.get(p)||0)+1,m=(a.get(p)||0)+1;Re.set(p,h),a.set(p,m),i.push(p),h===1&&g&&Qe.set(p,!0),m===1&&p.setAttribute(n,"true"),g||p.setAttribute(o,"true")}catch(y){console.error("aria-hidden: cannot operate on ",p,y)}})};return s(t),l.clear(),wt++,function(){i.forEach(function(d){var p=Re.get(d)-1,v=a.get(d)-1;Re.set(d,p),a.set(d,v),p||(Qe.has(d)||d.removeAttribute(o),Qe.delete(d)),v||d.removeAttribute(n)}),wt--,wt||(Re=new WeakMap,Re=new WeakMap,Qe=new WeakMap,et={})}},Oa=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=wa(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live]"))),Ca(o,r,n,"aria-hidden")):function(){return null}};function no(e){let t;j(()=>ce(e),n=>{n?t=Oa(n):t&&t()}),ut(()=>{t&&t()})}let Ea=0;function Be(e,t="radix"){if(e)return e;const n=Gt({useId:void 0});return nn?`${t}-${nn()}`:n.useId?`${t}-${n.useId()}`:`${t}-${++Ea}`}function _a(e){const t=O(),n=D(()=>{var r;return((r=t.value)==null?void 0:r.width)??0}),o=D(()=>{var r;return((r=t.value)==null?void 0:r.height)??0});return le(()=>{const r=ce(e);if(r){t.value={width:r.offsetWidth,height:r.offsetHeight};const a=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const l=i[0];let u,c;if("borderBoxSize"in l){const s=l.borderBoxSize,d=Array.isArray(s)?s[0]:s;u=d.inlineSize,c=d.blockSize}else u=r.offsetWidth,c=r.offsetHeight;t.value={width:u,height:c}});return a.observe(r,{box:"border-box"}),()=>a.unobserve(r)}else t.value=void 0}),{width:n,height:o}}function Aa(e,t){const n=O(e);function o(r){return t[n.value][r]??n.value}return{state:n,dispatch:r=>{n.value=o(r)}}}const Pa="data-item-text";function Ba(e){const t=Kt("",1e3);return{search:t,handleTypeaheadSearch:(n,o)=>{if(!(e!=null&&e.value)&&!o)return;t.value=t.value+n;const r=(e==null?void 0:e.value)??o,a=G(),i=r.map(d=>{var p;return{ref:d,textValue:((p=(d.querySelector(`[${Pa}]`)??d).textContent)==null?void 0:p.trim())??""}}),l=i.find(d=>d.ref===a),u=i.map(d=>d.textValue),c=Ta(u,t.value,l==null?void 0:l.textValue),s=i.find(d=>d.textValue===c);return s&&s.ref.focus(),s==null?void 0:s.ref},resetTypeahead:()=>{t.value=""}}}function ka(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function Ta(e,t,n){const o=t.length>1&&Array.from(t).every(l=>l===t[0])?t[0]:t,r=n?e.indexOf(n):-1;let a=ka(e,Math.max(r,0));o.length===1&&(a=a.filter(l=>l!==n));const i=a.find(l=>l.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}const qt=E({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:n}){return()=>{var o,r;if(!n.default)return null;const a=Ht(n.default()),i=a.findIndex(s=>s.type!==kn);if(i===-1)return a;const l=a[i];(o=l.props)==null||delete o.ref;const u=l.props?I(t,l.props):t;t.class&&(r=l.props)!=null&&r.class&&delete l.props.class;const c=Mo(l,u);for(const s in u)s.startsWith("on")&&(c.props||(c.props={}),c.props[s]=u[s]);return a.length===1?c:(a[i]=c,a)}}}),L=E({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:n}){const o=e.asChild?"template":e.as;return typeof o=="string"&&["area","img","input"].includes(o)?()=>ye(o,t):o!=="template"?()=>ye(e.as,t,{default:n.default}):()=>ye(qt,t,{default:n.default})}});function oo(){const e=O(),t=D(()=>{var n,o;return["#text","#comment"].includes((n=e.value)==null?void 0:n.$el.nodeName)?(o=e.value)==null?void 0:o.$el.nextElementSibling:ce(e)});return{primitiveElement:e,currentElement:t}}function Da(e,t){var n;const o=O({}),r=O("none"),a=O(e),i=e.value?"mounted":"unmounted";let l;const u=((n=t.value)==null?void 0:n.ownerDocument.defaultView)??pt,{state:c,dispatch:s}=Aa(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),d=m=>{var y;if(xe){const w=new CustomEvent(m,{bubbles:!1,cancelable:!1});(y=t.value)==null||y.dispatchEvent(w)}};j(e,async(m,y)=>{var w;const b=y!==m;if(await Q(),b){const P=r.value,T=tt(t.value);m?(s("MOUNT"),d("enter"),T==="none"&&d("after-enter")):T==="none"||((w=o.value)==null?void 0:w.display)==="none"?(s("UNMOUNT"),d("leave"),d("after-leave")):y&&P!==T?(s("ANIMATION_OUT"),d("leave")):(s("UNMOUNT"),d("after-leave"))}},{immediate:!0});const p=m=>{const y=tt(t.value),w=y.includes(m.animationName),b=c.value==="mounted"?"enter":"leave";if(m.target===t.value&&w&&(d(`after-${b}`),s("ANIMATION_END"),!a.value)){const P=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",l=u==null?void 0:u.setTimeout(()=>{var T;((T=t.value)==null?void 0:T.style.animationFillMode)==="forwards"&&(t.value.style.animationFillMode=P)})}m.target===t.value&&y==="none"&&s("ANIMATION_END")},v=m=>{m.target===t.value&&(r.value=tt(t.value))},g=j(t,(m,y)=>{m?(o.value=getComputedStyle(m),m.addEventListener("animationstart",v),m.addEventListener("animationcancel",p),m.addEventListener("animationend",p)):(s("ANIMATION_END"),l!==void 0&&(u==null||u.clearTimeout(l)),y==null||y.removeEventListener("animationstart",v),y==null||y.removeEventListener("animationcancel",p),y==null||y.removeEventListener("animationend",p))},{immediate:!0}),h=j(c,()=>{const m=tt(t.value);r.value=c.value==="mounted"?m:"none"});return ut(()=>{g(),h()}),{isPresent:D(()=>["mounted","unmountSuspended"].includes(c.value))}}function tt(e){return e&&getComputedStyle(e).animationName||"none"}const We=E({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){var o;const{present:r,forceMount:a}=fe(e),i=O(),{isPresent:l}=Da(r,i);n({present:l});let u=t.default({present:l});u=Ht(u||[]);const c=Te();if(u&&(u==null?void 0:u.length)>1){const s=(o=c==null?void 0:c.parent)!=null&&o.type.name?`<${c.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${s}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(d=>`  - ${d}`).join(`
`)].join(`
`))}return()=>a.value||r.value||l.value?ye(t.default({present:l})[0],{ref:s=>{const d=ce(s);return typeof(d==null?void 0:d.hasAttribute)>"u"||(d!=null&&d.hasAttribute("data-radix-popper-content-wrapper")?i.value=d.firstElementChild:i.value=d),d}}):null}}),[ve,Ma]=q("DialogRoot"),$l=E({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,o=Me(n,"open",t,{defaultValue:n.defaultOpen,passive:n.open===void 0}),r=O(),a=O(),{modal:i}=fe(n);return Ma({open:o,modal:i,openModal:()=>{o.value=!0},onOpenChange:l=>{o.value=l},onOpenToggle:()=>{o.value=!o.value},contentId:"",titleId:"",descriptionId:"",triggerElement:r,contentElement:a}),(l,u)=>B(l.$slots,"default",{open:f(o)})}}),Il=E({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=ve(),{forwardRef:o,currentElement:r}=R();return n.contentId||(n.contentId=Be(void 0,"radix-vue-dialog-content")),le(()=>{n.triggerElement.value=r.value}),(a,i)=>(A(),k(f(L),I(t,{ref:f(o),type:a.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":f(n).open.value||!1,"aria-controls":f(n).open.value?f(n).contentId:void 0,"data-state":f(n).open.value?"open":"closed",onClick:f(n).onOpenToggle}),{default:C(()=>[B(a.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),Xt=E({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=Xn();return(n,o)=>f(t)||n.forceMount?(A(),k(Ro,{key:0,to:n.to,disabled:n.disabled},[B(n.$slots,"default")],8,["to","disabled"])):st("",!0)}}),Nl=E({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(n,o)=>(A(),k(f(Xt),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),Sa="dismissableLayer.pointerDownOutside",Ra="dismissableLayer.focusOutside";function ro(e,t){const n=t.closest("[data-dismissable-layer]"),o=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),r=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&o===n||r.indexOf(o)<r.indexOf(n))}function $a(e,t){var n;const o=((n=t==null?void 0:t.value)==null?void 0:n.ownerDocument)??(globalThis==null?void 0:globalThis.document),r=O(!1),a=O(()=>{});return ne(i=>{if(!xe)return;const l=async c=>{const s=c.target;if(t!=null&&t.value){if(ro(t.value,s)){r.value=!1;return}if(c.target&&!r.value){let d=function(){Gn(Sa,e,p)};const p={originalEvent:c};c.pointerType==="touch"?(o.removeEventListener("click",a.value),a.value=d,o.addEventListener("click",a.value,{once:!0})):d()}else o.removeEventListener("click",a.value);r.value=!1}},u=window.setTimeout(()=>{o.addEventListener("pointerdown",l)},0);i(()=>{window.clearTimeout(u),o.removeEventListener("pointerdown",l),o.removeEventListener("click",a.value)})}),{onPointerDownCapture:()=>r.value=!0}}function Ia(e,t){var n;const o=((n=t==null?void 0:t.value)==null?void 0:n.ownerDocument)??(globalThis==null?void 0:globalThis.document),r=O(!1);return ne(a=>{if(!xe)return;const i=async l=>{t!=null&&t.value&&(await Q(),!(!t.value||ro(t.value,l.target))&&l.target&&!r.value&&Gn(Ra,e,{originalEvent:l}))};o.addEventListener("focusin",i),a(()=>o.removeEventListener("focusin",i))}),{onFocusCapture:()=>r.value=!0,onBlurCapture:()=>r.value=!1}}const ge=Mn({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Yt=E({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:t}){const n=e,o=t,{forwardRef:r,currentElement:a}=R(),i=D(()=>{var g;return((g=a.value)==null?void 0:g.ownerDocument)??globalThis.document}),l=D(()=>ge.layersRoot),u=D(()=>a.value?Array.from(l.value).indexOf(a.value):-1),c=D(()=>ge.layersWithOutsidePointerEventsDisabled.size>0),s=D(()=>{const g=Array.from(l.value),[h]=[...ge.layersWithOutsidePointerEventsDisabled].slice(-1),m=g.indexOf(h);return u.value>=m}),d=$a(async g=>{const h=[...ge.branches].some(m=>m==null?void 0:m.contains(g.target));!s.value||h||(o("pointerDownOutside",g),o("interactOutside",g),await Q(),g.defaultPrevented||o("dismiss"))},a),p=Ia(g=>{[...ge.branches].some(h=>h==null?void 0:h.contains(g.target))||(o("focusOutside",g),o("interactOutside",g),g.defaultPrevented||o("dismiss"))},a);Qr("Escape",g=>{u.value===l.value.size-1&&(o("escapeKeyDown",g),g.defaultPrevented||o("dismiss"))});let v;return ne(g=>{a.value&&(n.disableOutsidePointerEvents&&(ge.layersWithOutsidePointerEventsDisabled.size===0&&(v=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),ge.layersWithOutsidePointerEventsDisabled.add(a.value)),l.value.add(a.value),g(()=>{n.disableOutsidePointerEvents&&ge.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=v)}))}),ne(g=>{g(()=>{a.value&&(l.value.delete(a.value),ge.layersWithOutsidePointerEventsDisabled.delete(a.value))})}),(g,h)=>(A(),k(f(L),{ref:f(r),"as-child":g.asChild,as:g.as,"data-dismissable-layer":"",style:Sn({pointerEvents:c.value?s.value?"auto":"none":void 0}),onFocusCapture:f(p).onFocusCapture,onBlurCapture:f(p).onBlurCapture,onPointerdownCapture:f(d).onPointerDownCapture},{default:C(()=>[B(g.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),xt="focusScope.autoFocusOnMount",Ct="focusScope.autoFocusOnUnmount",gn={bubbles:!1,cancelable:!0};function Na(e,{select:t=!1}={}){const n=G();for(const o of e)if(Ee(o,{select:t}),G()!==n)return!0}function La(e){const t=ao(e),n=hn(t,e),o=hn(t.reverse(),e);return[n,o]}function ao(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function hn(e,t){for(const n of e)if(!Fa(n,{upTo:t}))return n}function Fa(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function za(e){return e instanceof HTMLInputElement&&"select"in e}function Ee(e,{select:t=!1}={}){if(e&&e.focus){const n=G();e.focus({preventScroll:!0}),e!==n&&za(e)&&t&&e.select()}}const Va=Wr(()=>O([]));function Wa(){const e=Va();return{add(t){const n=e.value[0];t!==n&&(n==null||n.pause()),e.value=yn(e.value,t),e.value.unshift(t)},remove(t){var n;e.value=yn(e.value,t),(n=e.value[0])==null||n.resume()}}}function yn(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function ja(e){return e.filter(t=>t.tagName!=="A")}const io=E({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:t}){const n=e,o=t,{currentRef:r,currentElement:a}=R(),i=O(null),l=Wa(),u=Mn({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});ne(s=>{if(!xe)return;const d=a.value;if(!n.trapped)return;function p(m){if(u.paused||!d)return;const y=m.target;d.contains(y)?i.value=y:Ee(i.value,{select:!0})}function v(m){if(u.paused||!d)return;const y=m.relatedTarget;y!==null&&(d.contains(y)||Ee(i.value,{select:!0}))}function g(m){d.contains(i.value)||Ee(d)}document.addEventListener("focusin",p),document.addEventListener("focusout",v);const h=new MutationObserver(g);d&&h.observe(d,{childList:!0,subtree:!0}),s(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",v),h.disconnect()})}),ne(async s=>{const d=a.value;if(await Q(),!d)return;l.add(u);const p=G();if(!d.contains(p)){const v=new CustomEvent(xt,gn);d.addEventListener(xt,g=>o("mountAutoFocus",g)),d.dispatchEvent(v),v.defaultPrevented||(Na(ja(ao(d)),{select:!0}),G()===p&&Ee(d))}s(()=>{d.removeEventListener(xt,h=>o("mountAutoFocus",h));const v=new CustomEvent(Ct,gn),g=h=>{o("unmountAutoFocus",h)};d.addEventListener(Ct,g),d.dispatchEvent(v),setTimeout(()=>{v.defaultPrevented||Ee(p??document.body,{select:!0}),d.removeEventListener(Ct,g),l.remove(u)},0)})});function c(s){if(!n.loop&&!n.trapped||u.paused)return;const d=s.key==="Tab"&&!s.altKey&&!s.ctrlKey&&!s.metaKey,p=G();if(d&&p){const v=s.currentTarget,[g,h]=La(v);g&&h?!s.shiftKey&&p===h?(s.preventDefault(),n.loop&&Ee(g,{select:!0})):s.shiftKey&&p===g&&(s.preventDefault(),n.loop&&Ee(h,{select:!0})):p===v&&s.preventDefault()}}return(s,d)=>(A(),k(f(L),{ref_key:"currentRef",ref:r,tabindex:"-1","as-child":s.asChild,as:s.as,onKeydown:c},{default:C(()=>[B(s.$slots,"default")]),_:3},8,["as-child","as"]))}}),Ka="menu.itemSelect",Tt=["Enter"," "],Ha=["ArrowDown","PageUp","Home"],lo=["ArrowUp","PageDown","End"],Ga=[...Ha,...lo];[...Tt],[...Tt];function so(e){return e?"open":"closed"}function Ua(e){const t=G();for(const n of e)if(n===t||(n.focus(),G()!==t))return}function qa(e,t){const{x:n,y:o}=e;let r=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const l=t[a].x,u=t[a].y,c=t[i].x,s=t[i].y;u>o!=s>o&&n<(c-l)*(o-u)/(s-u)+l&&(r=!r)}return r}function Xa(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return qa(n,t)}function Dt(e){return e.pointerType==="mouse"}const uo=E({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=t,r=ve(),{forwardRef:a,currentElement:i}=R();return r.titleId||(r.titleId=Be(void 0,"radix-vue-dialog-title")),r.descriptionId||(r.descriptionId=Be(void 0,"radix-vue-dialog-description")),le(()=>{r.contentElement=i,G()!==document.body&&(r.triggerElement.value=G())}),(l,u)=>(A(),k(f(io),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:u[5]||(u[5]=c=>o("openAutoFocus",c)),onUnmountAutoFocus:u[6]||(u[6]=c=>o("closeAutoFocus",c))},{default:C(()=>[U(f(Yt),I({id:f(r).contentId,ref:f(a),as:l.as,"as-child":l.asChild,"disable-outside-pointer-events":l.disableOutsidePointerEvents,role:"dialog","aria-describedby":f(r).descriptionId,"aria-labelledby":f(r).titleId,"data-state":f(so)(f(r).open.value)},l.$attrs,{onDismiss:u[0]||(u[0]=c=>f(r).onOpenChange(!1)),onEscapeKeyDown:u[1]||(u[1]=c=>o("escapeKeyDown",c)),onFocusOutside:u[2]||(u[2]=c=>o("focusOutside",c)),onInteractOutside:u[3]||(u[3]=c=>o("interactOutside",c)),onPointerDownOutside:u[4]||(u[4]=c=>o("pointerDownOutside",c))}),{default:C(()=>[B(l.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),Ya=E({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=t,r=ve(),a=Xe(o),{forwardRef:i,currentElement:l}=R();return no(l),(u,c)=>(A(),k(uo,I({...n,...f(a)},{ref:f(i),"trap-focus":f(r).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:c[0]||(c[0]=s=>{var d;s.defaultPrevented||(s.preventDefault(),(d=f(r).triggerElement.value)==null||d.focus())}),onPointerDownOutside:c[1]||(c[1]=s=>{const d=s.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0;(d.button===2||p)&&s.preventDefault()}),onFocusOutside:c[2]||(c[2]=s=>{s.preventDefault()})}),{default:C(()=>[B(u.$slots,"default")]),_:3},16,["trap-focus"]))}}),Za=E({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=Xe(t);R();const r=ve(),a=O(!1),i=O(!1);return(l,u)=>(A(),k(uo,I({...n,...f(o)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:u[0]||(u[0]=c=>{var s;c.defaultPrevented||(a.value||(s=f(r).triggerElement.value)==null||s.focus(),c.preventDefault()),a.value=!1,i.value=!1}),onInteractOutside:u[1]||(u[1]=c=>{var s;c.defaultPrevented||(a.value=!0,c.detail.originalEvent.type==="pointerdown"&&(i.value=!0));const d=c.target;(s=f(r).triggerElement.value)!=null&&s.contains(d)&&c.preventDefault(),c.detail.originalEvent.type==="focusin"&&i.value&&c.preventDefault()})}),{default:C(()=>[B(l.$slots,"default")]),_:3},16))}}),Ll=E({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=t,r=ve(),a=Xe(o),{forwardRef:i}=R();return(l,u)=>(A(),k(f(We),{present:l.forceMount||f(r).open.value},{default:C(()=>[f(r).modal.value?(A(),k(Ya,I({key:0,ref:f(i)},{...n,...f(a),...l.$attrs}),{default:C(()=>[B(l.$slots,"default")]),_:3},16)):(A(),k(Za,I({key:1,ref:f(i)},{...n,...f(a),...l.$attrs}),{default:C(()=>[B(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ja=E({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(e){const t=ve();return Jn(!0),R(),(n,o)=>(A(),k(f(L),{as:n.as,"as-child":n.asChild,"data-state":f(t).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:C(()=>[B(n.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Fl=E({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=ve(),{forwardRef:n}=R();return(o,r)=>{var a;return(a=f(t))!=null&&a.modal.value?(A(),k(f(We),{key:0,present:o.forceMount||f(t).open.value},{default:C(()=>[U(Ja,I(o.$attrs,{ref:f(n),as:o.as,"as-child":o.asChild}),{default:C(()=>[B(o.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):st("",!0)}}}),zl=E({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;R();const n=ve();return(o,r)=>(A(),k(f(L),I(t,{type:o.as==="button"?"button":void 0,onClick:r[0]||(r[0]=a=>f(n).onOpenChange(!1))}),{default:C(()=>[B(o.$slots,"default")]),_:3},16,["type"]))}}),Vl=E({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(e){const t=e,n=ve();return R(),(o,r)=>(A(),k(f(L),I(t,{id:f(n).titleId}),{default:C(()=>[B(o.$slots,"default")]),_:3},16,["id"]))}}),Wl=E({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(e){const t=e;R();const n=ve();return(o,r)=>(A(),k(f(L),I(t,{id:f(n).descriptionId}),{default:C(()=>[B(o.$slots,"default")]),_:3},16,["id"]))}}),[co,Qa]=q("AvatarRoot"),jl=E({__name:"AvatarRoot",props:{asChild:{type:Boolean},as:{default:"span"}},setup(e){return R(),Qa({imageLoadingStatus:O("loading")}),(t,n)=>(A(),k(f(L),{"as-child":t.asChild,as:t.as},{default:C(()=>[B(t.$slots,"default")]),_:3},8,["as-child","as"]))}});function ei(e,t){const n=O("idle"),o=O(!1),r=a=>()=>{o.value&&(n.value=a)};return le(()=>{o.value=!0,j([()=>e.value,()=>t==null?void 0:t.value],([a,i])=>{if(!a)n.value="error";else{const l=new window.Image;n.value="loading",l.onload=r("loaded"),l.onerror=r("error"),l.src=a,i&&(l.referrerPolicy=i)}},{immediate:!0})}),ut(()=>{o.value=!1}),n}const Kl=E({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{default:"img"}},emits:["loadingStatusChange"],setup(e,{emit:t}){const n=e,o=t,{src:r,referrerPolicy:a}=fe(n);R();const i=co(),l=ei(r,a);return j(l,u=>{o("loadingStatusChange",u),u!=="idle"&&(i.imageLoadingStatus.value=u)},{immediate:!0}),(u,c)=>ko((A(),k(f(L),{role:"img","as-child":u.asChild,as:u.as,src:f(r),"referrer-policy":f(a)},{default:C(()=>[B(u.$slots,"default")]),_:3},8,["as-child","as","src","referrer-policy"])),[[To,f(l)==="loaded"]])}}),Hl=E({__name:"AvatarFallback",props:{delayMs:{default:0},asChild:{type:Boolean},as:{default:"span"}},setup(e){const t=e,n=co();R();const o=O(!1);let r;return j(n.imageLoadingStatus,a=>{a==="loading"&&(o.value=!1,t.delayMs?r=setTimeout(()=>{o.value=!0,clearTimeout(r)},t.delayMs):o.value=!0)},{immediate:!0}),(a,i)=>o.value&&f(n).imageLoadingStatus.value!=="loaded"?(A(),k(f(L),{key:0,"as-child":a.asChild,as:a.as},{default:C(()=>[B(a.$slots,"default")]),_:3},8,["as-child","as"])):st("",!0)}});function lt(e){return e==="indeterminate"}function fo(e){return lt(e)?"indeterminate":e?"checked":"unchecked"}const ti=["value","checked","name","disabled","required"],[ni,oi]=q("CheckboxRoot"),Gl=E({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultChecked:{type:Boolean},checked:{type:[Boolean,String],default:void 0},disabled:{type:Boolean},required:{type:Boolean},name:{},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"}},emits:["update:checked"],setup(e,{emit:t}){const n=e,o=t,{disabled:r}=fe(n),a=Me(n,"checked",o,{defaultValue:n.defaultChecked,passive:n.checked===void 0}),{forwardRef:i,currentElement:l}=R(),u=fa(l),c=D(()=>{var s;return n.id&&l.value?(s=document.querySelector(`[for="${n.id}"]`))==null?void 0:s.innerText:void 0});return oi({disabled:r,state:a}),(s,d)=>(A(),ot(Bn,null,[U(f(L),I(s.$attrs,{id:s.id,ref:f(i),role:"checkbox","as-child":n.asChild,as:s.as,type:s.as==="button"?"button":void 0,"aria-checked":f(lt)(f(a))?"mixed":f(a),"aria-required":n.required,"aria-label":s.$attrs["aria-label"]||c.value,"data-state":f(fo)(f(a)),"data-disabled":f(r)?"":void 0,disabled:f(r),onKeydown:Rt($t(()=>{},["prevent"]),["enter"]),onClick:d[0]||(d[0]=p=>a.value=f(lt)(f(a))?!0:!f(a))}),{default:C(()=>[B(s.$slots,"default",{checked:f(a)})]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","onKeydown"]),f(u)?(A(),ot("input",{key:0,type:"checkbox",tabindex:"-1","aria-hidden":"true",value:s.value,checked:!!f(a),name:n.name,disabled:n.disabled,required:n.required,style:{transform:"translateX(-100%)",position:"absolute",pointerEvents:"none",opacity:0,margin:0}},null,8,ti)):st("",!0)],64))}}),Ul=E({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const{forwardRef:t}=R(),n=ni();return(o,r)=>(A(),k(f(We),{present:o.forceMount||f(lt)(f(n).state.value)||f(n).state.value===!0},{default:C(()=>[U(f(L),I({ref:f(t),"data-state":f(fo)(f(n).state.value),"data-disabled":f(n).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":o.asChild,as:o.as},o.$attrs),{default:C(()=>[B(o.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}}),[po,ri]=q("PopperRoot"),vo=E({inheritAttrs:!1,__name:"PopperRoot",setup(e){const t=O();return ri({anchor:t,onAnchorChange:n=>t.value=n}),(n,o)=>B(n.$slots,"default")}}),mo=E({__name:"PopperAnchor",props:{element:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,{forwardRef:n,currentElement:o}=R(),r=po();return ne(()=>{r.onAnchorChange(t.element??o.value)}),(a,i)=>(A(),k(f(L),{ref:f(n),as:a.as,"as-child":a.asChild},{default:C(()=>[B(a.$slots,"default")]),_:3},8,["as","as-child"]))}});function ai(e){return e!==null}function ii(e){return{name:"transformOrigin",options:e,fn(t){var n,o,r;const{placement:a,rects:i,middlewareData:l}=t,u=((n=l.arrow)==null?void 0:n.centerOffset)!==0,c=u?0:e.arrowWidth,s=u?0:e.arrowHeight,[d,p]=Mt(a),v={start:"0%",center:"50%",end:"100%"}[p],g=(((o=l.arrow)==null?void 0:o.x)??0)+c/2,h=(((r=l.arrow)==null?void 0:r.y)??0)+s/2;let m="",y="";return d==="bottom"?(m=u?v:`${g}px`,y=`${-s}px`):d==="top"?(m=u?v:`${g}px`,y=`${i.floating.height+s}px`):d==="right"?(m=`${-s}px`,y=u?v:`${h}px`):d==="left"&&(m=`${i.floating.width+s}px`,y=u?v:`${h}px`),{data:{x:m,y}}}}}function Mt(e){const[t,n="center"]=e.split("-");return[t,n]}const go={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,updatePositionStrategy:"optimized",prioritizePosition:!1},[ql,li]=q("PopperContent"),ho=E({inheritAttrs:!1,__name:"PopperContent",props:Rn({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},{...go}),emits:["placed"],setup(e,{emit:t}){const n=e,o=t,r=po(),{forwardRef:a,currentElement:i}=R(),l=O(),u=O(),{width:c,height:s}=_a(u),d=D(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),p=D(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),v=D(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),g=D(()=>({padding:p.value,boundary:v.value.filter(ai),altBoundary:v.value.length>0})),h=zr(()=>[Tr({mainAxis:n.sideOffset+s.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&fn({...g.value}),n.avoidCollisions&&Dr({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?$r():void 0,...g.value}),!n.prioritizePosition&&n.avoidCollisions&&fn({...g.value}),Mr({...g.value,apply:({elements:x,rects:M,availableWidth:_,availableHeight:K})=>{const{width:N,height:X}=M.reference,H=x.floating.style;H.setProperty("--radix-popper-available-width",`${_}px`),H.setProperty("--radix-popper-available-height",`${K}px`),H.setProperty("--radix-popper-anchor-width",`${N}px`),H.setProperty("--radix-popper-anchor-height",`${X}px`)}}),u.value&&Lr({element:u.value,padding:n.arrowPadding}),ii({arrowWidth:c.value,arrowHeight:s.value}),n.hideWhenDetached&&Sr({strategy:"referenceHidden",...g.value})]),{floatingStyles:m,placement:y,isPositioned:w,middlewareData:b}=Fr(r.anchor,l,{strategy:"fixed",placement:d,whileElementsMounted:(...x)=>kr(...x,{animationFrame:n.updatePositionStrategy==="always"}),middleware:h}),P=D(()=>Mt(y.value)[0]),T=D(()=>Mt(y.value)[1]);Ko(()=>{w.value&&o("placed")});const F=D(()=>{var x;return((x=b.value.arrow)==null?void 0:x.centerOffset)!==0}),S=O("");ne(()=>{i.value&&(S.value=window.getComputedStyle(i.value).zIndex)});const z=D(()=>{var x;return((x=b.value.arrow)==null?void 0:x.x)??0}),W=D(()=>{var x;return((x=b.value.arrow)==null?void 0:x.y)??0});return li({placedSide:P,onArrowChange:x=>u.value=x,arrowX:z,arrowY:W,shouldHideArrow:F}),(x,M)=>{var _,K,N;return A(),ot("div",{ref_key:"floatingRef",ref:l,"data-radix-popper-content-wrapper":"",style:Sn({...f(m),transform:f(w)?f(m).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:S.value,"--radix-popper-transform-origin":[(_=f(b).transformOrigin)==null?void 0:_.x,(K=f(b).transformOrigin)==null?void 0:K.y].join(" "),...((N=f(b).hide)==null?void 0:N.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[U(f(L),I({ref:f(a)},x.$attrs,{"as-child":n.asChild,as:x.as,"data-side":P.value,"data-align":T.value,style:{animation:f(w)?void 0:"none"}}),{default:C(()=>[B(x.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}}),si=E({__name:"VisuallyHidden",props:{asChild:{type:Boolean},as:{default:"span"}},setup(e){return R(),(t,n)=>(A(),k(f(L),{as:t.as,"as-child":t.asChild,style:{position:"absolute",border:0,width:"1px",display:"inline-block",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:C(()=>[B(t.$slots,"default")]),_:3},8,["as","as-child"]))}}),ui="data-radix-vue-collection-item",[Zt,di]=q("CollectionProvider");function ci(e=ui){const t=O(new Map),n=O(),o=di({collectionRef:n,itemMap:t,attrName:e}),{getItems:r}=vi(o),a=D(()=>Array.from(o.itemMap.value.values())),i=D(()=>o.itemMap.value.size);return{getItems:r,reactiveItems:a,itemMapSize:i}}const fi=E({name:"CollectionSlot",setup(e,{slots:t}){const n=Zt(),{primitiveElement:o,currentElement:r}=oo();return j(r,()=>{n.collectionRef.value=r.value}),()=>ye(qt,{ref:o},t)}}),pi=E({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(e,{slots:t,attrs:n}){const o=Zt(),{primitiveElement:r,currentElement:a}=oo();return ne(i=>{if(a.value){const l=Ho(a.value);o.itemMap.value.set(l,{ref:a.value,value:e.value}),i(()=>o.itemMap.value.delete(l))}}),()=>ye(qt,{...n,[o.attrName]:"",ref:r},t)}});function vi(e){const t=e??Zt();return{getItems:()=>{const n=t.collectionRef.value;if(!n)return[];const o=Array.from(n.querySelectorAll(`[${t.attrName}]`));return Array.from(t.itemMap.value.values()).sort((r,a)=>o.indexOf(r.ref)-o.indexOf(a.ref))}}}const mi=E({__name:"MenuAnchor",props:{element:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,o)=>(A(),k(f(mo),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}});function gi(){const e=O(!1);return le(()=>{Fe("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),Fe(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const hi=Un(gi),[mt,yi]=q(["MenuRoot","MenuSub"],"MenuContext"),[Jt,bi]=q("MenuRoot"),wi=E({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,o=t,{modal:r,dir:a}=fe(n),i=vt(a),l=Me(n,"open",o),u=O(),c=hi();return yi({open:l,onOpenChange:s=>{l.value=s},content:u,onContentChange:s=>{u.value=s}}),bi({onClose:()=>{l.value=!1},isUsingKeyboardRef:c,dir:i,modal:r}),(s,d)=>(A(),k(f(vo),null,{default:C(()=>[B(s.$slots,"default")]),_:3}))}}),xi="rovingFocusGroup.onEntryFocus",Ci={bubbles:!1,cancelable:!0};function Oi(e,t=!1){const n=G();for(const o of e)if(o===n||(o.focus({preventScroll:t}),G()!==n))return}const[Xl,Ei]=q("RovingFocusGroup"),_i=E({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(e,{expose:t,emit:n}){const o=e,r=n,{loop:a,orientation:i,dir:l}=fe(o),u=vt(l),c=Me(o,"currentTabStopId",r,{defaultValue:o.defaultCurrentTabStopId,passive:o.currentTabStopId===void 0}),s=O(!1),d=O(!1),p=O(0),{getItems:v}=ci();function g(m){const y=!d.value;if(m.currentTarget&&m.target===m.currentTarget&&y&&!s.value){const w=new CustomEvent(xi,Ci);if(m.currentTarget.dispatchEvent(w),r("entryFocus",w),!w.defaultPrevented){const b=v().map(S=>S.ref).filter(S=>S.dataset.disabled!==""),P=b.find(S=>S.getAttribute("data-active")==="true"),T=b.find(S=>S.id===c.value),F=[P,T,...b].filter(Boolean);Oi(F,o.preventScrollOnEntryFocus)}}d.value=!1}function h(){setTimeout(()=>{d.value=!1},1)}return t({getItems:v}),Ei({loop:a,dir:u,orientation:i,currentTabStopId:c,onItemFocus:m=>{c.value=m},onItemShiftTab:()=>{s.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(m,y)=>(A(),k(f(fi),null,{default:C(()=>[U(f(L),{tabindex:s.value||p.value===0?-1:0,"data-orientation":f(i),as:m.as,"as-child":m.asChild,dir:f(u),style:{outline:"none"},onMousedown:y[0]||(y[0]=w=>d.value=!0),onMouseup:h,onFocus:g,onBlur:y[1]||(y[1]=w=>s.value=!1)},{default:C(()=>[B(m.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}}),[yo,Ai]=q("MenuContent"),bo=E({__name:"MenuContentImpl",props:Rn({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},{...go}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:t}){const n=e,o=t,r=mt(),a=Jt(),{trapFocus:i,disableOutsidePointerEvents:l,loop:u}=fe(n);ca(),Jn(l.value);const c=O(""),s=O(0),d=O(0),p=O(null),v=O("right"),g=O(0),h=O(null),{createCollection:m}=Ut(),{forwardRef:y,currentElement:w}=R(),b=m(w);j(w,x=>{r.onContentChange(x)});const{handleTypeaheadSearch:P}=Ba(b);ut(()=>{window.clearTimeout(s.value)});function T(x){var M,_;return v.value===((M=p.value)==null?void 0:M.side)&&Xa(x,(_=p.value)==null?void 0:_.area)}async function F(x){var M;o("openAutoFocus",x),!x.defaultPrevented&&(x.preventDefault(),(M=w.value)==null||M.focus({preventScroll:!0}))}function S(x){if(x.defaultPrevented)return;const M=x.target.closest("[data-radix-menu-content]")===x.currentTarget,_=x.ctrlKey||x.altKey||x.metaKey,K=x.key.length===1,N=Yn(x,G(),w.value,{loop:u.value,arrowKeyOptions:"vertical",dir:a==null?void 0:a.dir.value,focus:!0,attributeName:"[data-radix-vue-collection-item]:not([data-disabled])"});if(N)return N==null?void 0:N.focus();if(x.code==="Space"||(M&&(x.key==="Tab"&&x.preventDefault(),!_&&K&&P(x.key)),x.target!==w.value)||!Ga.includes(x.key))return;x.preventDefault();const X=b.value;lo.includes(x.key)&&X.reverse(),Ua(X)}function z(x){var M,_;(_=(M=x==null?void 0:x.currentTarget)==null?void 0:M.contains)!=null&&_.call(M,x.target)||(window.clearTimeout(s.value),c.value="")}function W(x){var M;if(!Dt(x))return;const _=x.target,K=g.value!==x.clientX;if((M=x==null?void 0:x.currentTarget)!=null&&M.contains(_)&&K){const N=x.clientX>g.value?"right":"left";v.value=N,g.value=x.clientX}}return Ai({onItemEnter:x=>!!T(x),onItemLeave:x=>{var M;T(x)||((M=w.value)==null||M.focus(),h.value=null)},onTriggerLeave:x=>!!T(x),searchRef:c,pointerGraceTimerRef:d,onPointerGraceIntentChange:x=>{p.value=x}}),(x,M)=>(A(),k(f(io),{"as-child":"",trapped:f(i),onMountAutoFocus:F,onUnmountAutoFocus:M[7]||(M[7]=_=>o("closeAutoFocus",_))},{default:C(()=>[U(f(Yt),{"as-child":"","disable-outside-pointer-events":f(l),onEscapeKeyDown:M[2]||(M[2]=_=>o("escapeKeyDown",_)),onPointerDownOutside:M[3]||(M[3]=_=>o("pointerDownOutside",_)),onFocusOutside:M[4]||(M[4]=_=>o("focusOutside",_)),onInteractOutside:M[5]||(M[5]=_=>o("interactOutside",_)),onDismiss:M[6]||(M[6]=_=>o("dismiss"))},{default:C(()=>[U(f(_i),{"current-tab-stop-id":h.value,"onUpdate:currentTabStopId":M[0]||(M[0]=_=>h.value=_),"as-child":"",orientation:"vertical",dir:f(a).dir.value,loop:f(u),onEntryFocus:M[1]||(M[1]=_=>{o("entryFocus",_),f(a).isUsingKeyboardRef.value||_.preventDefault()})},{default:C(()=>[U(f(ho),{ref:f(y),role:"menu",as:x.as,"as-child":x.asChild,"aria-orientation":"vertical","data-radix-menu-content":"","data-state":f(so)(f(r).open.value),dir:f(a).dir.value,side:x.side,"side-offset":x.sideOffset,align:x.align,"align-offset":x.alignOffset,"avoid-collisions":x.avoidCollisions,"collision-boundary":x.collisionBoundary,"collision-padding":x.collisionPadding,"arrow-padding":x.arrowPadding,"prioritize-position":x.prioritizePosition,sticky:x.sticky,"hide-when-detached":x.hideWhenDetached,onKeydown:S,onBlur:z,onPointermove:W},{default:C(()=>[B(x.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","sticky","hide-when-detached"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Pi=E({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=yo(),{forwardRef:o}=R(),r=O(!1);async function a(l){if(!l.defaultPrevented&&Dt(l)){if(t.disabled)n.onItemLeave(l);else if(!n.onItemEnter(l)){const u=l.currentTarget;u==null||u.focus({preventScroll:!0})}}}async function i(l){await Q(),!l.defaultPrevented&&Dt(l)&&n.onItemLeave(l)}return(l,u)=>(A(),k(f(pi),{value:{textValue:l.textValue}},{default:C(()=>[U(f(L),I({ref:f(o),role:"menuitem",tabindex:"-1"},l.$attrs,{as:l.as,"as-child":l.asChild,"data-radix-vue-collection-item":"","aria-disabled":l.disabled||void 0,"data-disabled":l.disabled?"":void 0,"data-highlighted":r.value?"":void 0,onPointermove:a,onPointerleave:i,onFocus:u[0]||(u[0]=async c=>{await Q(),!(c.defaultPrevented||l.disabled)&&(r.value=!0)}),onBlur:u[1]||(u[1]=async c=>{await Q(),!c.defaultPrevented&&(r.value=!1)})}),{default:C(()=>[B(l.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),Bi=E({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:t}){const n=e,o=t,{forwardRef:r,currentElement:a}=R(),i=Jt(),l=yo(),u=O(!1);async function c(){const s=a.value;if(!n.disabled&&s){const d=new CustomEvent(Ka,{bubbles:!0,cancelable:!0});o("select",d),await Q(),d.defaultPrevented?u.value=!1:i.onClose()}}return(s,d)=>(A(),k(Pi,I(n,{ref:f(r),onClick:c,onPointerdown:d[0]||(d[0]=()=>{u.value=!0}),onPointerup:d[1]||(d[1]=async p=>{var v;await Q(),!p.defaultPrevented&&(u.value||(v=p.currentTarget)==null||v.click())}),onKeydown:d[2]||(d[2]=async p=>{const v=f(l).searchRef.value!=="";s.disabled||v&&p.key===" "||f(Tt).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:C(()=>[B(s.$slots,"default")]),_:3},16))}}),ki=E({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=t,r=Ye(n,o),a=mt(),{forwardRef:i,currentElement:l}=R();return no(l),(u,c)=>(A(),k(bo,I(f(r),{ref:f(i),"trap-focus":f(a).open.value,"disable-outside-pointer-events":f(a).open.value,"disable-outside-scroll":!0,onDismiss:c[0]||(c[0]=s=>f(a).onOpenChange(!1)),onFocusOutside:c[1]||(c[1]=$t(s=>o("focusOutside",s),["prevent"]))}),{default:C(()=>[B(u.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),Ti=E({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=Ye(e,t),o=mt();return(r,a)=>(A(),k(bo,I(f(n),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:a[0]||(a[0]=i=>f(o).onOpenChange(!1))}),{default:C(()=>[B(r.$slots,"default")]),_:3},16))}}),Di=E({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=Ye(e,t),o=mt(),r=Jt();return(a,i)=>(A(),k(f(We),{present:a.forceMount||f(o).open.value},{default:C(()=>[f(r).modal.value?(A(),k(ki,te(I({key:0},{...a.$attrs,...f(n)})),{default:C(()=>[B(a.$slots,"default")]),_:3},16)):(A(),k(Ti,te(I({key:1},{...a.$attrs,...f(n)})),{default:C(()=>[B(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Mi=E({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,o)=>(A(),k(f(L),I({role:"group"},t),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),Si=E({__name:"MenuLabel",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e;return(n,o)=>(A(),k(f(L),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),Ri=E({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(n,o)=>(A(),k(f(Xt),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),$i=E({__name:"MenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,o)=>(A(),k(f(L),I(t,{role:"separator","aria-orientation":"horizontal"}),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),[wo,Ii]=q("DropdownMenuRoot"),Yl=E({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,o=t;R();const r=Me(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0}),a=O(),{modal:i,dir:l}=fe(n),u=vt(l);return Ii({open:r,onOpenChange:c=>{r.value=c},onOpenToggle:()=>{r.value=!r.value},triggerId:"",triggerElement:a,contentId:"",modal:i,dir:u}),(c,s)=>(A(),k(f(wi),{open:f(r),"onUpdate:open":s[0]||(s[0]=d=>Do(r)?r.value=d:null),dir:f(u),modal:f(i)},{default:C(()=>[B(c.$slots,"default",{open:f(r)})]),_:3},8,["open","dir","modal"]))}}),Zl=E({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=wo(),{forwardRef:o,currentElement:r}=R();return le(()=>{n.triggerElement=r}),n.triggerId||(n.triggerId=Be(void 0,"radix-vue-dropdown-menu-trigger")),(a,i)=>(A(),k(f(mi),{"as-child":""},{default:C(()=>[U(f(L),{id:f(n).triggerId,ref:f(o),type:a.as==="button"?"button":void 0,"as-child":t.asChild,as:a.as,"aria-haspopup":"menu","aria-expanded":f(n).open.value,"aria-controls":f(n).open.value?f(n).contentId:void 0,"data-disabled":a.disabled?"":void 0,disabled:a.disabled,"data-state":f(n).open.value?"open":"closed",onClick:i[0]||(i[0]=async l=>{var u;!a.disabled&&l.button===0&&l.ctrlKey===!1&&((u=f(n))==null||u.onOpenToggle(),await Q(),f(n).open.value&&l.preventDefault())}),onKeydown:i[1]||(i[1]=Rt(l=>{a.disabled||(["Enter"," "].includes(l.key)&&f(n).onOpenToggle(),l.key==="ArrowDown"&&f(n).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())},["enter","space","arrow-down"]))},{default:C(()=>[B(a.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}}),Jl=E({__name:"DropdownMenuPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(n,o)=>(A(),k(f(Ri),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),Ql=E({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:t}){const n=Ye(e,t);R();const o=wo(),r=O(!1);function a(i){i.defaultPrevented||(r.value||setTimeout(()=>{var l;(l=o.triggerElement.value)==null||l.focus()},0),r.value=!1,i.preventDefault())}return o.contentId||(o.contentId=Be(void 0,"radix-vue-dropdown-menu-content")),(i,l)=>{var u;return A(),k(f(Di),I(f(n),{id:f(o).contentId,"aria-labelledby":(u=f(o))==null?void 0:u.triggerId,style:{"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"},onCloseAutoFocus:a,onInteractOutside:l[0]||(l[0]=c=>{var s;if(c.defaultPrevented)return;const d=c.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0,v=d.button===2||p;(!f(o).modal.value||v)&&(r.value=!0),(s=f(o).triggerElement.value)!=null&&s.contains(c.target)&&c.preventDefault()})}),{default:C(()=>[B(i.$slots,"default")]),_:3},16,["id","aria-labelledby"])}}}),es=E({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:t}){const n=e,o=Xe(t);return R(),(r,a)=>(A(),k(f(Bi),te(ie({...n,...f(o)})),{default:C(()=>[B(r.$slots,"default")]),_:3},16))}}),ts=E({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return R(),(n,o)=>(A(),k(f(Mi),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),ns=E({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return R(),(n,o)=>(A(),k(f($i),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),os=E({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return R(),(n,o)=>(A(),k(f(Si),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),rs=E({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(e){const t=e;return R(),(n,o)=>(A(),k(f(L),I(t,{onMousedown:o[0]||(o[0]=r=>{!r.defaultPrevented&&r.detail>1&&r.preventDefault()})}),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),[Qt,Ni]=q(["NavigationMenuRoot","NavigationMenuSub"],"NavigationMenuContext"),as=E({__name:"NavigationMenuRoot",props:{modelValue:{default:void 0},defaultValue:{},dir:{},orientation:{default:"horizontal"},delayDuration:{default:200},skipDelayDuration:{default:300},disableClickTrigger:{type:Boolean,default:!1},disableHoverTrigger:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"nav"}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,o=Me(n,"modelValue",t,{defaultValue:n.defaultValue??"",passive:n.modelValue===void 0}),r=O(""),{forwardRef:a,currentElement:i}=R(),l=O(),u=O(),{createCollection:c}=Ut("nav");c(l);const{delayDuration:s,skipDelayDuration:d,dir:p,disableClickTrigger:v,disableHoverTrigger:g}=fe(n),h=vt(p),m=Kt(!1,d),y=D(()=>o.value!==""||m.value?150:s.value),w=Yr(b=>{typeof b=="string"&&(r.value=o.value,o.value=b)},y);return Ni({isRootMenu:!0,modelValue:o,previousValue:r,baseId:Be(void 0,"radix-navigation-menu"),disableClickTrigger:v,disableHoverTrigger:g,dir:h,orientation:n.orientation,rootNavigationMenu:i,indicatorTrack:l,onIndicatorTrackChange:b=>{l.value=b},viewport:u,onViewportChange:b=>{u.value=b},onTriggerEnter:b=>{w(b)},onTriggerLeave:()=>{m.value=!0,w("")},onContentEnter:()=>{w()},onContentLeave:()=>{w("")},onItemSelect:b=>{r.value=o.value,o.value=b},onItemDismiss:()=>{r.value=o.value,o.value=""}}),(b,P)=>(A(),k(f(L),{ref:f(a),"aria-label":"Main",as:b.as,"as-child":b.asChild,"data-orientation":b.orientation,dir:f(h),"data-radix-navigation-menu":""},{default:C(()=>[B(b.$slots,"default",{modelValue:f(o)})]),_:3},8,["as","as-child","data-orientation","dir"]))}});function Li(e){return e?"open":"closed"}function Fi(e,t){return`${e}-content-${t}`}const zi="navigationMenu.linkSelect",Vi="navigationMenu.rootContentDismiss";function bn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Wi(e){const t=G();return e.some(n=>n===t?!0:(n.focus(),G()!==t))}function ji(e){return e.forEach(t=>{t.dataset.tabindex=t.getAttribute("tabindex")||"",t.setAttribute("tabindex","-1")}),()=>{e.forEach(t=>{const n=t.dataset.tabindex;t.setAttribute("tabindex",n)})}}function Ki(e){return t=>t.pointerType==="mouse"?e(t):void 0}const[is,Hi]=q("NavigationMenuItem"),ls=E({__name:"NavigationMenuItem",props:{value:{},asChild:{type:Boolean},as:{default:"li"}},setup(e){const t=e;R();const{injectCollection:n}=Ut("nav"),o=n(),r=Qt(),a=Be(t.value),i=O(),l=O(),u=Fi(r.baseId,a);let c=()=>({});const s=O(!1);async function d(h="start"){const m=document.getElementById(u);if(m){c();const y=bn(m);y.length&&Wi(h==="start"?y:y.reverse())}}function p(){const h=document.getElementById(u);if(h){const m=bn(h);m.length&&(c=ji(m))}}Hi({value:a,contentId:u,triggerRef:i,focusProxyRef:l,wasEscapeCloseRef:s,onEntryKeyDown:d,onFocusProxyEnter:d,onContentFocusOutside:p,onRootContentClose:p});function v(){var h;r.onItemDismiss(),(h=i.value)==null||h.focus()}function g(h){const m=G();if(h.keyCode===32||h.key==="Enter")if(r.modelValue.value===a){v(),h.preventDefault();return}else{h.target.click(),h.preventDefault();return}const y=o.value.filter(b=>{var P;return(P=b.parentElement)==null?void 0:P.hasAttribute("data-menu-item")});if(!y.includes(m))return;const w=Yn(h,m,void 0,{itemsArray:y,loop:!1});w&&(w==null||w.focus()),h.preventDefault(),h.stopPropagation()}return(h,m)=>(A(),k(f(L),{"as-child":h.asChild,as:h.as,"data-menu-item":"",onKeydown:Rt(g,["up","down","left","right","home","end","space"])},{default:C(()=>[B(h.$slots,"default")]),_:3},8,["as-child","as"]))}}),ss=E({__name:"NavigationMenuLink",props:{active:{type:Boolean},asChild:{type:Boolean},as:{default:"a"}},emits:["select"],setup(e,{emit:t}){const n=e,o=t;R();async function r(a){var i;const l=new CustomEvent(zi,{bubbles:!0,cancelable:!0,detail:{originalEvent:a}});if(o("select",l),!l.defaultPrevented&&!a.metaKey){const u=new CustomEvent(Vi,{bubbles:!0,cancelable:!0});(i=a.target)==null||i.dispatchEvent(u)}}return(a,i)=>(A(),k(f(L),{as:a.as,"data-active":a.active?"":void 0,"aria-current":a.active?"page":void 0,"as-child":n.asChild,"data-radix-vue-collection-item":"",onClick:r},{default:C(()=>[B(a.$slots,"default")]),_:3},8,["as","data-active","aria-current","as-child"]))}}),us=E({inheritAttrs:!1,__name:"NavigationMenuList",props:{asChild:{type:Boolean},as:{default:"ul"}},setup(e){const t=e,n=Qt(),{forwardRef:o,currentElement:r}=R();return le(()=>{n.onIndicatorTrackChange(r.value)}),(a,i)=>(A(),k(f(L),{ref:f(o),style:{position:"relative"}},{default:C(()=>[U(f(L),I(a.$attrs,{"as-child":t.asChild,as:a.as,"data-orientation":f(n).orientation}),{default:C(()=>[B(a.$slots,"default")]),_:3},16,["as-child","as","data-orientation"])]),_:3},512))}}),ds=E({inheritAttrs:!1,__name:"NavigationMenuViewport",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const{forwardRef:t,currentElement:n}=R(),o=Qt(),r=O(),a=D(()=>!!o.modelValue.value),i=D(()=>o.modelValue.value);j(n,()=>{n.value&&o.onViewportChange(n.value)});const l=O();return j([i,a],async()=>{var u,c;if(await Q(),!n.value)return;const s=(c=(u=n.value.querySelector("[data-state=open]"))==null?void 0:u.children)==null?void 0:c[0];l.value=s},{immediate:!0}),na(l,()=>{l.value&&(r.value={width:l.value.offsetWidth,height:l.value.offsetHeight})}),(u,c)=>(A(),k(f(We),{present:u.forceMount||a.value},{default:C(()=>{var s,d;return[U(f(L),I(u.$attrs,{ref:f(t),as:u.as,"as-child":u.asChild,"data-state":f(Li)(a.value),"data-orientation":f(o).orientation,style:{pointerEvents:!a.value&&f(o).isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":r.value?`${(s=r.value)==null?void 0:s.width}px`:void 0,"--radix-navigation-menu-viewport-height":r.value?`${(d=r.value)==null?void 0:d.height}px`:void 0},onPointerenter:c[0]||(c[0]=p=>f(o).onContentEnter(f(o).modelValue.value)),onPointerleave:c[1]||(c[1]=p=>f(Ki)(()=>f(o).onContentLeave())(p))}),{default:C(()=>[B(u.$slots,"default")]),_:3},16,["as","as-child","data-state","data-orientation","style"])]}),_:3},8,["present"]))}}),Gi=E({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=["horizontal","vertical"];function o(l){return n.includes(l)}const r=D(()=>o(t.orientation)?t.orientation:"horizontal"),a=D(()=>r.value==="vertical"?t.orientation:void 0),i=D(()=>t.decorative?{role:"none"}:{"aria-orientation":a.value,role:"separator"});return(l,u)=>(A(),k(f(L),I({as:l.as,"as-child":l.asChild,"data-orientation":r.value},i.value),{default:C(()=>[B(l.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),cs=E({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,o)=>(A(),k(Gi,te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}});function Ui(){if(typeof matchMedia=="function")return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}Ui();const xo="tooltip.open",[en,qi]=q("TooltipProvider"),fs=E({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{default:700},skipDelayDuration:{default:300},disableHoverableContent:{type:Boolean,default:!1},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean,default:!1}},setup(e){const t=e,{delayDuration:n,skipDelayDuration:o,disableHoverableContent:r,disableClosingTrigger:a,ignoreNonKeyboardFocus:i,disabled:l}=fe(t);R();const u=O(!0),c=O(!1),{start:s,stop:d}=qn(()=>{u.value=!0},o,{immediate:!1});return qi({isOpenDelayed:u,delayDuration:n,onOpen(){d(),u.value=!1},onClose(){s()},isPointerInTransitRef:c,disableHoverableContent:r,disableClosingTrigger:a,disabled:l,ignoreNonKeyboardFocus:i}),(p,v)=>B(p.$slots,"default")}}),[gt,Xi]=q("TooltipRoot"),ps=E({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},delayDuration:{default:void 0},disableHoverableContent:{type:Boolean,default:void 0},disableClosingTrigger:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,default:void 0}},emits:["update:open"],setup(e,{emit:t}){const n=e,o=t;R();const r=en(),a=D(()=>n.disableHoverableContent??r.disableHoverableContent.value),i=D(()=>n.disableClosingTrigger??r.disableClosingTrigger.value),l=D(()=>n.disabled??r.disabled.value),u=D(()=>n.delayDuration??r.delayDuration.value),c=D(()=>n.ignoreNonKeyboardFocus??r.ignoreNonKeyboardFocus.value),s=Me(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0});j(s,b=>{r.onClose&&(b?(r.onOpen(),document.dispatchEvent(new CustomEvent(xo))):r.onClose())});const d=O(!1),p=O(),v=D(()=>s.value?d.value?"delayed-open":"instant-open":"closed"),{start:g,stop:h}=qn(()=>{d.value=!0,s.value=!0},u,{immediate:!1});function m(){h(),d.value=!1,s.value=!0}function y(){h(),s.value=!1}function w(){g()}return Xi({contentId:"",open:s,stateAttribute:v,trigger:p,onTriggerChange(b){p.value=b},onTriggerEnter(){r.isOpenDelayed.value?w():m()},onTriggerLeave(){a.value?y():h()},onOpen:m,onClose:y,disableHoverableContent:a,disableClosingTrigger:i,disabled:l,ignoreNonKeyboardFocus:c}),(b,P)=>(A(),k(f(vo),null,{default:C(()=>[B(b.$slots,"default",{open:f(s)})]),_:3}))}}),vs=E({__name:"TooltipTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=gt(),o=en();n.contentId||(n.contentId=Be(void 0,"radix-vue-tooltip-content"));const{forwardRef:r,currentElement:a}=R(),i=O(!1),l=O(!1),u=D(()=>n.disabled.value?{}:{click:h,focus:v,pointermove:d,pointerleave:p,pointerdown:s,blur:g});le(()=>{n.onTriggerChange(a.value)});function c(){setTimeout(()=>{i.value=!1},1)}function s(){i.value=!0,document.addEventListener("pointerup",c,{once:!0})}function d(m){m.pointerType!=="touch"&&!l.value&&!o.isPointerInTransitRef.value&&(n.onTriggerEnter(),l.value=!0)}function p(){n.onTriggerLeave(),l.value=!1}function v(m){var y,w;i.value||n.ignoreNonKeyboardFocus.value&&!((w=(y=m.target).matches)!=null&&w.call(y,":focus-visible"))||n.onOpen()}function g(){n.onClose()}function h(){n.disableClosingTrigger.value||n.onClose()}return(m,y)=>(A(),k(f(mo),{"as-child":""},{default:C(()=>[U(f(L),I({ref:f(r),"aria-describedby":f(n).open.value?f(n).contentId:void 0,"data-state":f(n).stateAttribute.value,as:m.as,"as-child":t.asChild,"data-grace-area-trigger":""},$o(u.value)),{default:C(()=>[B(m.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3}))}}),Co=E({__name:"TooltipContentImpl",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{default:0},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean,default:!0},collisionBoundary:{default:()=>[]},collisionPadding:{default:0},arrowPadding:{default:0},sticky:{default:"partial"},hideWhenDetached:{type:Boolean,default:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,o=t,r=gt(),{forwardRef:a}=R(),i=zo(),l=D(()=>{var s;return(s=i.default)==null?void 0:s.call(i)}),u=D(()=>{var s;if(n.ariaLabel)return n.ariaLabel;let d="";function p(v){typeof v.children=="string"&&v.type!==kn?d+=v.children:Array.isArray(v.children)&&v.children.forEach(g=>p(g))}return(s=l.value)==null||s.forEach(v=>p(v)),d}),c=D(()=>{const{ariaLabel:s,...d}=n;return d});return le(()=>{Fe(window,"scroll",s=>{const d=s.target;d!=null&&d.contains(r.trigger.value)&&r.onClose()}),Fe(window,xo,r.onClose)}),(s,d)=>(A(),k(f(Yt),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:d[0]||(d[0]=p=>o("escapeKeyDown",p)),onPointerDownOutside:d[1]||(d[1]=p=>{var v;f(r).disableClosingTrigger.value&&(v=f(r).trigger.value)!=null&&v.contains(p.target)&&p.preventDefault(),o("pointerDownOutside",p)}),onFocusOutside:d[2]||(d[2]=$t(()=>{},["prevent"])),onDismiss:d[3]||(d[3]=p=>f(r).onClose())},{default:C(()=>[U(f(ho),I({ref:f(a),"data-state":f(r).stateAttribute.value},{...s.$attrs,...c.value},{style:{"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}}),{default:C(()=>[B(s.$slots,"default"),U(f(si),{id:f(r).contentId,role:"tooltip"},{default:C(()=>[Vo(Wo(u.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),Yi=E({__name:"TooltipContentHoverable",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean}},setup(e){const t=eo(e),{forwardRef:n,currentElement:o}=R(),{trigger:r,onClose:a}=gt(),i=en(),{isPointerInTransit:l,onPointerExit:u}=pa(r,o);return i.isPointerInTransitRef=l,u(()=>{a()}),(c,s)=>(A(),k(Co,I({ref:f(n)},f(t)),{default:C(()=>[B(c.$slots,"default")]),_:3},16))}}),ms=E({__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,o=t,r=gt(),a=Ye(n,o),{forwardRef:i}=R();return(l,u)=>(A(),k(f(We),{present:l.forceMount||f(r).open.value},{default:C(()=>[(A(),k(Io(f(r).disableHoverableContent.value?Co:Yi),I({ref:f(i)},f(a)),{default:C(()=>[B(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),gs=E({__name:"TooltipPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(n,o)=>(A(),k(f(Xt),te(ie(t)),{default:C(()=>[B(n.$slots,"default")]),_:3},16))}}),tn="-",Zi=e=>{const t=Qi(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const l=i.split(tn);return l[0]===""&&l.length!==1&&l.shift(),Oo(l,t)||Ji(i)},getConflictingClassGroupIds:(i,l)=>{const u=n[i]||[];return l&&o[i]?[...u,...o[i]]:u}}},Oo=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],o=t.nextPart.get(n),r=o?Oo(e.slice(1),o):void 0;if(r)return r;if(t.validators.length===0)return;const a=e.join(tn);return(i=t.validators.find(({validator:l})=>l(a)))==null?void 0:i.classGroupId},wn=/^\[(.+)\]$/,Ji=e=>{if(wn.test(e)){const t=wn.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Qi=e=>{const{theme:t,prefix:n}=e,o={nextPart:new Map,validators:[]};return tl(Object.entries(e.classGroups),n).forEach(([a,i])=>{St(i,o,a,t)}),o},St=(e,t,n,o)=>{e.forEach(r=>{if(typeof r=="string"){const a=r===""?t:xn(t,r);a.classGroupId=n;return}if(typeof r=="function"){if(el(r)){St(r(o),t,n,o);return}t.validators.push({validator:r,classGroupId:n});return}Object.entries(r).forEach(([a,i])=>{St(i,xn(t,a),n,o)})})},xn=(e,t)=>{let n=e;return t.split(tn).forEach(o=>{n.nextPart.has(o)||n.nextPart.set(o,{nextPart:new Map,validators:[]}),n=n.nextPart.get(o)}),n},el=e=>e.isThemeGetter,tl=(e,t)=>t?e.map(([n,o])=>{const r=o.map(a=>typeof a=="string"?t+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([i,l])=>[t+i,l])):a);return[n,r]}):e,nl=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,o=new Map;const r=(a,i)=>{n.set(a,i),t++,t>e&&(t=0,o=n,n=new Map)};return{get(a){let i=n.get(a);if(i!==void 0)return i;if((i=o.get(a))!==void 0)return r(a,i),i},set(a,i){n.has(a)?n.set(a,i):r(a,i)}}},Eo="!",ol=e=>{const{separator:t,experimentalParseClassName:n}=e,o=t.length===1,r=t[0],a=t.length,i=l=>{const u=[];let c=0,s=0,d;for(let m=0;m<l.length;m++){let y=l[m];if(c===0){if(y===r&&(o||l.slice(m,m+a)===t)){u.push(l.slice(s,m)),s=m+a;continue}if(y==="/"){d=m;continue}}y==="["?c++:y==="]"&&c--}const p=u.length===0?l:l.substring(s),v=p.startsWith(Eo),g=v?p.substring(1):p,h=d&&d>s?d-s:void 0;return{modifiers:u,hasImportantModifier:v,baseClassName:g,maybePostfixModifierPosition:h}};return n?l=>n({className:l,parseClassName:i}):i},rl=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(o=>{o[0]==="["?(t.push(...n.sort(),o),n=[]):n.push(o)}),t.push(...n.sort()),t},al=e=>({cache:nl(e.cacheSize),parseClassName:ol(e),...Zi(e)}),il=/\s+/,ll=(e,t)=>{const{parseClassName:n,getClassGroupId:o,getConflictingClassGroupIds:r}=t,a=[],i=e.trim().split(il);let l="";for(let u=i.length-1;u>=0;u-=1){const c=i[u],{modifiers:s,hasImportantModifier:d,baseClassName:p,maybePostfixModifierPosition:v}=n(c);let g=!!v,h=o(g?p.substring(0,v):p);if(!h){if(!g){l=c+(l.length>0?" "+l:l);continue}if(h=o(p),!h){l=c+(l.length>0?" "+l:l);continue}g=!1}const m=rl(s).join(":"),y=d?m+Eo:m,w=y+h;if(a.includes(w))continue;a.push(w);const b=r(h,g);for(let P=0;P<b.length;++P){const T=b[P];a.push(y+T)}l=c+(l.length>0?" "+l:l)}return l};function sl(){let e=0,t,n,o="";for(;e<arguments.length;)(t=arguments[e++])&&(n=_o(t))&&(o&&(o+=" "),o+=n);return o}const _o=e=>{if(typeof e=="string")return e;let t,n="";for(let o=0;o<e.length;o++)e[o]&&(t=_o(e[o]))&&(n&&(n+=" "),n+=t);return n};function ul(e,...t){let n,o,r,a=i;function i(u){const c=t.reduce((s,d)=>d(s),e());return n=al(c),o=n.cache.get,r=n.cache.set,a=l,l(u)}function l(u){const c=o(u);if(c)return c;const s=ll(u,n);return r(u,s),s}return function(){return a(sl.apply(null,arguments))}}const V=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ao=/^\[(?:([a-z-]+):)?(.+)\]$/i,dl=/^\d+\/\d+$/,cl=new Set(["px","full","screen"]),fl=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,pl=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,vl=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ml=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gl=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,he=e=>Ne(e)||cl.has(e)||dl.test(e),Ce=e=>je(e,"length",El),Ne=e=>!!e&&!Number.isNaN(Number(e)),Ot=e=>je(e,"number",Ne),Ke=e=>!!e&&Number.isInteger(Number(e)),hl=e=>e.endsWith("%")&&Ne(e.slice(0,-1)),$=e=>Ao.test(e),Oe=e=>fl.test(e),yl=new Set(["length","size","percentage"]),bl=e=>je(e,yl,Po),wl=e=>je(e,"position",Po),xl=new Set(["image","url"]),Cl=e=>je(e,xl,Al),Ol=e=>je(e,"",_l),He=()=>!0,je=(e,t,n)=>{const o=Ao.exec(e);return o?o[1]?typeof t=="string"?o[1]===t:t.has(o[1]):n(o[2]):!1},El=e=>pl.test(e)&&!vl.test(e),Po=()=>!1,_l=e=>ml.test(e),Al=e=>gl.test(e),Pl=()=>{const e=V("colors"),t=V("spacing"),n=V("blur"),o=V("brightness"),r=V("borderColor"),a=V("borderRadius"),i=V("borderSpacing"),l=V("borderWidth"),u=V("contrast"),c=V("grayscale"),s=V("hueRotate"),d=V("invert"),p=V("gap"),v=V("gradientColorStops"),g=V("gradientColorStopPositions"),h=V("inset"),m=V("margin"),y=V("opacity"),w=V("padding"),b=V("saturate"),P=V("scale"),T=V("sepia"),F=V("skew"),S=V("space"),z=V("translate"),W=()=>["auto","contain","none"],x=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",$,t],_=()=>[$,t],K=()=>["",he,Ce],N=()=>["auto",Ne,$],X=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],H=()=>["solid","dashed","dotted","double","none"],Z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],oe=()=>["start","end","center","between","around","evenly","stretch"],Y=()=>["","0",$],Ze=()=>["auto","avoid","all","avoid-page","page","left","right","column"],me=()=>[Ne,$];return{cacheSize:500,separator:":",theme:{colors:[He],spacing:[he,Ce],blur:["none","",Oe,$],brightness:me(),borderColor:[e],borderRadius:["none","","full",Oe,$],borderSpacing:_(),borderWidth:K(),contrast:me(),grayscale:Y(),hueRotate:me(),invert:Y(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[hl,Ce],inset:M(),margin:M(),opacity:me(),padding:_(),saturate:me(),scale:me(),sepia:Y(),skew:me(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",$]}],container:["container"],columns:[{columns:[Oe]}],"break-after":[{"break-after":Ze()}],"break-before":[{"break-before":Ze()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...X(),$]}],overflow:[{overflow:x()}],"overflow-x":[{"overflow-x":x()}],"overflow-y":[{"overflow-y":x()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ke,$]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",$]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",Ke,$]}],"grid-cols":[{"grid-cols":[He]}],"col-start-end":[{col:["auto",{span:["full",Ke,$]},$]}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":[He]}],"row-start-end":[{row:["auto",{span:[Ke,$]},$]}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",$]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",$]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...oe()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...oe(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...oe(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[m]}],mx:[{mx:[m]}],my:[{my:[m]}],ms:[{ms:[m]}],me:[{me:[m]}],mt:[{mt:[m]}],mr:[{mr:[m]}],mb:[{mb:[m]}],ml:[{ml:[m]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",$,t]}],"min-w":[{"min-w":[$,t,"min","max","fit"]}],"max-w":[{"max-w":[$,t,"none","full","min","max","fit","prose",{screen:[Oe]},Oe]}],h:[{h:[$,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[$,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Oe,Ce]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ot]}],"font-family":[{font:[He]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",$]}],"line-clamp":[{"line-clamp":["none",Ne,Ot]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",he,$]}],"list-image":[{"list-image":["none",$]}],"list-style-type":[{list:["none","disc","decimal",$]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",he,Ce]}],"underline-offset":[{"underline-offset":["auto",he,$]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...X(),wl]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",bl]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Cl]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...H(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:H()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...H()]}],"outline-offset":[{"outline-offset":[he,$]}],"outline-w":[{outline:[he,Ce]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:K()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[he,Ce]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Oe,Ol]}],"shadow-color":[{shadow:[He]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...Z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Z()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[o]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",Oe,$]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[s]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[s]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",$]}],duration:[{duration:me()}],ease:[{ease:["linear","in","out","in-out",$]}],delay:[{delay:me()}],animate:[{animate:["none","spin","ping","pulse","bounce",$]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[P]}],"scale-x":[{"scale-x":[P]}],"scale-y":[{"scale-y":[P]}],rotate:[{rotate:[Ke,$]}],"translate-x":[{"translate-x":[z]}],"translate-y":[{"translate-y":[z]}],"skew-x":[{"skew-x":[F]}],"skew-y":[{"skew-y":[F]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",$]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[he,Ce,Ot]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Bl=ul(Pl);function kl(...e){return Bl(In(e))}/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var nt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=({size:e,strokeWidth:t=2,absoluteStrokeWidth:n,color:o,iconNode:r,name:a,class:i,...l},{slots:u})=>ye("svg",{...nt,width:e||nt.width,height:e||nt.height,stroke:o||nt.stroke,"stroke-width":n?Number(t)*24/Number(e):t,class:["lucide",`lucide-${Tl(a??"icon")}`],...l},[...r.map(c=>ye(...c)),...u.default?[u.default()]:[]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=(e,t)=>(n,{slots:o})=>ye(Dl,{...n,iconNode:t,name:e},o),Ml=qo("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),ys=E({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(n,o)=>(A(),k(f(L),{as:n.as,"as-child":n.asChild,class:Go(f(kl)(f(Ml)({variant:n.variant,size:n.size}),t.class))},{default:C(()=>[B(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),bs=E({inheritAttrs:!1,__name:"AppLogoIcon",props:{className:{}},setup(e){return(t,n)=>(A(),ot("svg",I({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 40 42",class:t.className},t.$attrs),n[0]||(n[0]=[Uo("path",{fill:"currentColor","fill-rule":"evenodd","clip-rule":"evenodd",d:"M17.2 5.633 8.6.855 0 5.633v26.51l16.2 9 16.2-9v-8.442l7.6-4.223V9.856l-8.6-4.777-8.6 4.777V18.3l-5.6 3.111V5.633ZM38 18.301l-5.6 3.11v-6.157l5.6-3.11V18.3Zm-1.06-7.856-5.54 3.078-5.54-3.079 5.54-3.078 5.54 3.079ZM24.8 18.3v-6.157l5.6 3.111v6.158L24.8 18.3Zm-1 1.732 5.54 3.078-13.14 7.302-5.54-3.078 13.14-7.3v-.002Zm-16.2 7.89 7.6 4.222V38.3L2 30.966V7.92l5.6 3.111v16.892ZM8.6 9.3 3.06 6.222 8.6 3.143l5.54 3.08L8.6 9.3Zm21.8 15.51-13.2 7.334V38.3l13.2-7.334v-6.156ZM9.6 11.034l5.6-3.11v14.6l-5.6 3.11v-14.6Z"},null,-1)]),16))}});export{Fl as $,ls as A,Vl as B,ss as C,us as D,fs as H,Nl as I,Kl as K,eo as O,Ll as P,Ye as S,ms as U,rs as V,Gl as W,zl as X,ys as _,kl as a,bs as b,hs as c,Wl as d,cs as e,qo as f,ps as g,vs as h,L as i,Ul as j,Hl as k,Jl as l,Ql as m,$l as n,Il as o,ts as p,gs as q,os as r,ns as s,q as t,Yl as u,es as v,Zl as w,as as x,ds as y,jl as z};
