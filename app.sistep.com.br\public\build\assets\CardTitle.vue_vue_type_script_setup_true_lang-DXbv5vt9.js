import{_ as b}from"./AppLayout.vue_vue_type_script_setup_true_lang-MMQe0pvK.js";import{d as l,A as g,c as i,g as h,o as r,w as $,a as t,r as d,b as _,n as p,u}from"./app-DIEHtcz0.js";import{a as m}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";const v={key:0},x={key:1,class:"p-4"},A=l({__name:"AdminLayout",props:{breadcrumbs:{default:()=>[]}},setup(a){const s=g(),e=i(()=>s.props.auth.user),o=i(()=>{var n,c;return(c=(n=e.value)==null?void 0:n.roles)==null?void 0:c.some(f=>f.name==="admin")});return(n,c)=>(r(),h(b,{breadcrumbs:n.breadcrumbs},{default:$(()=>[o.value?(r(),t("div",v,[d(n.$slots,"default")])):(r(),t("div",x,c[0]||(c[0]=[_("div",{class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative",role:"alert"},[_("strong",{class:"font-bold"},"Acesso negado!"),_("span",{class:"block sm:inline"}," Você não tem permissão para acessar esta área.")],-1)])))]),_:3},8,["breadcrumbs"]))}}),B=l({__name:"Card",props:{class:{}},setup(a){const s=a;return(e,o)=>(r(),t("div",{class:p(u(m)("rounded-lg border bg-card text-card-foreground shadow-sm",s.class))},[d(e.$slots,"default")],2))}}),w=l({__name:"CardContent",props:{class:{}},setup(a){const s=a;return(e,o)=>(r(),t("div",{class:p(u(m)("p-6 pt-0",s.class))},[d(e.$slots,"default")],2))}}),V=l({__name:"CardHeader",props:{class:{}},setup(a){const s=a;return(e,o)=>(r(),t("div",{class:p(u(m)("flex flex-col gap-y-1.5 p-6",s.class))},[d(e.$slots,"default")],2))}}),z=l({__name:"CardTitle",props:{class:{}},setup(a){const s=a;return(e,o)=>(r(),t("h3",{class:p(u(m)("text-2xl font-semibold leading-none tracking-tight",s.class))},[d(e.$slots,"default")],2))}});export{B as _,V as a,z as b,w as c,A as d};
