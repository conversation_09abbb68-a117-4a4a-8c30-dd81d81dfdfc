import{c as u,S as f,W as p,a as m,j as h}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{d as g,c as k,g as b,o as y,u as e,B as _,w as o,e as r,r as B}from"./app-DIEHtcz0.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=u("CheckIcon",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),w=g({__name:"Checkbox",props:{defaultChecked:{type:Boolean},checked:{type:[Boolean,String]},disabled:{type:Boolean},required:{type:Boolean},name:{},value:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(c,{emit:d}){const s=c,n=d,i=k(()=>{const{class:a,...t}=s;return t}),l=f(i,n);return(a,t)=>(y(),b(e(p),_(e(l),{class:e(m)("peer size-5 shrink-0 rounded-sm border border-input ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-accent-foreground",s.class)}),{default:o(()=>[r(e(h),{class:"flex h-full w-full items-center justify-center text-current"},{default:o(()=>[B(a.$slots,"default",{},()=>[r(e(C),{class:"size-3.5 stroke-[3]"})])]),_:3})]),_:3},16,["class"]))}});export{C,w as _};
