import{d as p,C as f,g as i,o as d,w as t,e as a,b as r,u as s,m as c,z as u,h as n,x as _}from"./app-DIEHtcz0.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{_ as C}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{a as g,_ as b}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{L as V,_ as h}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DAnhOjOB.js";import"./index-Cree0lnl.js";const x={class:"space-y-6"},y={class:"grid gap-2"},$={class:"flex items-center"},q=p({__name:"ConfirmPassword",setup(k){const o=f({password:""}),m=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return(v,e)=>(d(),i(h,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(c),{title:"Confirm password"}),r("form",{onSubmit:u(m,["prevent"])},[r("div",x,[r("div",y,[a(s(g),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[n("Password")])),_:1}),a(s(b),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=l=>s(o).password=l),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(w,{message:s(o).errors.password},null,8,["message"])]),r("div",$,[a(s(C),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(d(),i(s(V),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),e[2]||(e[2]=n(" Confirm Password "))]),_:1},8,["disabled"])])])],32)]),_:1}))}});export{q as default};
