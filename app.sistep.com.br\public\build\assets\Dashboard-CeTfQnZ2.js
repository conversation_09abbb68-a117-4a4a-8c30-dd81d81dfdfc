import{_ as d,a as o,b as n,c as u,d as p}from"./CardTitle.vue_vue_type_script_setup_true_lang-DXbv5vt9.js";import{d as c,a as g,o as y,e as t,u as e,m as b,w as l,b as a,h as r,t as m,F as v}from"./app-DIEHtcz0.js";import{c as i}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import"./AppLayout.vue_vue_type_script_setup_true_lang-MMQe0pvK.js";import"./index-Cree0lnl.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=i("ClipboardCheckIcon",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=i("FilesIcon",[["path",{d:"M20 7h-3a2 2 0 0 1-2-2V2",key:"x099mo"}],["path",{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z",key:"18t6ie"}],["path",{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8",key:"1nja0z"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=i("TestTubeIcon",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=i("UsersIcon",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),C={class:"flex h-full flex-1 flex-col gap-4 p-4"},M={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},T={class:"text-2xl font-bold"},V={class:"text-2xl font-bold"},I={class:"mt-4 grid grid-cols-1 gap-4 md:grid-cols-2"},P=c({__name:"Dashboard",props:{usersCount:{},rolesCount:{}},setup(j){const x=[{title:"Dashboard",href:route("admin.dashboard")}];return(f,s)=>(y(),g(v,null,[t(e(b),{title:"Painel administrativo"}),t(p,{breadcrumbs:x},{default:l(()=>[a("div",C,[s[12]||(s[12]=a("h1",{class:"text-2xl font-bold"},"Dashboard",-1)),s[13]||(s[13]=a("p",{class:"text-muted-foreground"},"Bem-vindo ao painel administrativo do SISTEP.",-1)),a("div",M,[t(e(d),null,{default:l(()=>[t(e(o),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:l(()=>[t(e(n),{class:"text-sm font-medium"},{default:l(()=>s[0]||(s[0]=[r("Usuários")])),_:1}),t(e(w),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),t(e(u),null,{default:l(()=>[a("div",T,m(f.usersCount),1),s[1]||(s[1]=a("p",{class:"text-xs text-muted-foreground"}," Total de usuários cadastrados ",-1))]),_:1})]),_:1}),t(e(d),null,{default:l(()=>[t(e(o),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:l(()=>[t(e(n),{class:"text-sm font-medium"},{default:l(()=>s[2]||(s[2]=[r("Perfis")])),_:1}),t(e(k),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),t(e(u),null,{default:l(()=>[a("div",V,m(f.rolesCount),1),s[3]||(s[3]=a("p",{class:"text-xs text-muted-foreground"}," Perfis de acesso disponíveis ",-1))]),_:1})]),_:1}),t(e(d),null,{default:l(()=>[t(e(o),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:l(()=>[t(e(n),{class:"text-sm font-medium"},{default:l(()=>s[4]||(s[4]=[r("Testes")])),_:1}),t(e(h),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),t(e(u),null,{default:l(()=>s[5]||(s[5]=[a("div",{class:"text-2xl font-bold"},"--",-1),a("p",{class:"text-xs text-muted-foreground"}," Total de testes disponíveis ",-1)])),_:1})]),_:1}),t(e(d),null,{default:l(()=>[t(e(o),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:l(()=>[t(e(n),{class:"text-sm font-medium"},{default:l(()=>s[6]||(s[6]=[r("Avaliações")])),_:1}),t(e(_),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),t(e(u),null,{default:l(()=>s[7]||(s[7]=[a("div",{class:"text-2xl font-bold"},"--",-1),a("p",{class:"text-xs text-muted-foreground"}," Total de avaliações realizadas ",-1)])),_:1})]),_:1})]),a("div",I,[t(e(d),null,{default:l(()=>[t(e(o),null,{default:l(()=>[t(e(n),null,{default:l(()=>s[8]||(s[8]=[r("Atividade recente")])),_:1})]),_:1}),t(e(u),null,{default:l(()=>s[9]||(s[9]=[a("p",{class:"text-muted-foreground"},"Não há atividades recentes para exibir.",-1)])),_:1})]),_:1}),t(e(d),null,{default:l(()=>[t(e(o),null,{default:l(()=>[t(e(n),null,{default:l(()=>s[10]||(s[10]=[r("Estatísticas de uso")])),_:1})]),_:1}),t(e(u),null,{default:l(()=>s[11]||(s[11]=[a("p",{class:"text-muted-foreground"},"Não há estatísticas disponíveis no momento.",-1)])),_:1})]),_:1})])])]),_:1})],64))}});export{P as default};
