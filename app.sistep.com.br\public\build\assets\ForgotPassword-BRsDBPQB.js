import{d as f,C as _,g as n,o as m,w as r,e as a,a as c,x as d,b as o,u as s,m as g,t as w,z as x,h as l}from"./app-DIEHtcz0.js";import{_ as y}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{_ as k}from"./TextLink.vue_vue_type_script_setup_true_lang-DFPAzajK.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{a as V,_ as b}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{L as $,_ as C}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DAnhOjOB.js";import"./index-Cree0lnl.js";const B={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E={class:"space-y-6"},N={class:"grid gap-2"},h={class:"my-6 flex items-center justify-start"},F={class:"space-x-1 text-center text-sm text-muted-foreground"},T=f({__name:"ForgotPassword",props:{status:{}},setup(L){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(m(),n(C,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[a(s(g),{title:"Forgot password"}),i.status?(m(),c("div",B,w(i.status),1)):d("",!0),o("div",E,[o("form",{onSubmit:x(p,["prevent"])},[o("div",N,[a(s(V),{for:"email"},{default:r(()=>e[1]||(e[1]=[l("Email address")])),_:1}),a(s(b),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=u=>s(t).email=u),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),a(y,{message:s(t).errors.email},null,8,["message"])]),o("div",h,[a(s(v),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s($),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=l(" Email password reset link "))]),_:1},8,["disabled"])])],32),o("div",F,[e[4]||(e[4]=o("span",null,"Or, return to",-1)),a(k,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[l("log in")])),_:1},8,["href"])])])]),_:1}))}});export{T as default};
