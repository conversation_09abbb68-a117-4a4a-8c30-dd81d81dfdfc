import{d as k,a as r,o as n,n as C,u as e,r as $,b as m,p as se,i as me,c as D,j as B,k as S,l as pe,q as le,s as fe,v as ce,g as G,x,T as ve,e as s,t as f,h as d,C as P,m as _e,w as o,F as z,y as L,z as J}from"./app-DIEHtcz0.js";import{_ as ge,a as xe,b as be,c as ye,d as we}from"./CardTitle.vue_vue_type_script_setup_true_lang-DXbv5vt9.js";import{c as Ve,a as q,_ as U}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{_ as h,a as F,b as j,c as O,d as A,e as I}from"./DialogTitle.vue_vue_type_script_setup_true_lang-BLre35X4.js";import{_ as V,a as g}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{u as ke}from"./index-Cree0lnl.js";import{C as $e,_ as M}from"./Checkbox.vue_vue_type_script_setup_true_lang-t1diozls.js";import"./AppLayout.vue_vue_type_script_setup_true_lang-MMQe0pvK.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=Ve("ChevronDownIcon",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Ce=k({__name:"CardDescription",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("p",{class:C(e(q)("text-sm text-muted-foreground",u.class))},[$(i.$slots,"default")],2))}}),Se={class:"relative w-full overflow-auto"},Ee=k({__name:"Table",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("div",Se,[m("table",{class:C(e(q)("w-full caption-bottom text-sm",u.class))},[$(i.$slots,"default")],2)]))}}),Te=k({__name:"TableBody",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("tbody",{class:C(e(q)("[&_tr:last-child]:border-0",u.class))},[$(i.$slots,"default")],2))}}),T=k({__name:"TableCell",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("td",{class:C(e(q)("p-4 align-middle [&:has([role=checkbox])]:pr-0",u.class))},[$(i.$slots,"default")],2))}}),K=k({__name:"TableRow",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("tr",{class:C(e(q)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",u.class))},[$(i.$slots,"default")],2))}}),R=k({__name:"TableHead",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("th",{class:C(e(q)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",u.class))},[$(i.$slots,"default")],2))}}),Re=k({__name:"TableHeader",props:{class:{}},setup(b){const u=b;return(i,v)=>(n(),r("thead",{class:C(e(q)("[&_tr]:border-b",u.class))},[$(i.$slots,"default")],2))}}),qe={class:"relative"},W=k({__name:"Select",props:{modelValue:{},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(b,{expose:u,emit:i}){const v=b,_=ke(v,"modelValue",i);return se("select",{value:_,disabled:me(v,"disabled")}),u({value:D(()=>_.value)}),(w,p)=>(n(),r("div",qe,[$(w.$slots,"default")]))}}),Z=k({__name:"SelectContent",setup(b){const u=B("selectTrigger",{}),i=S(null),v=D(()=>{var p;return((p=u==null?void 0:u.open)==null?void 0:p.value)||!1}),y=D(()=>{var p;return((p=u==null?void 0:u.triggerElement)==null?void 0:p.value)||null});function _(){if(!i.value||!y.value)return;const p=y.value.getBoundingClientRect();i.value.getBoundingClientRect(),i.value.style.width=`${p.width}px`,i.value.style.top=`${p.bottom+window.scrollY+5}px`,i.value.style.left=`${p.left+window.scrollX}px`}function w(p){i.value&&!i.value.contains(p.target)&&y.value&&!y.value.contains(p.target)&&(u.open.value=!1)}return pe(()=>{window.addEventListener("resize",_),document.addEventListener("mousedown",w),le(v,p=>{p&&fe(()=>{_()})},{immediate:!0})}),ce(()=>{window.removeEventListener("resize",_),document.removeEventListener("mousedown",w)}),(p,l)=>(n(),G(ve,{to:"body"},[v.value?(n(),r("div",{key:0,ref_key:"contentElement",ref:i,class:"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"},[$(p.$slots,"default")],512)):x("",!0)]))}}),Ne=["aria-selected","data-disabled","data-selected"],ze={class:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center"},N=k({__name:"SelectItem",props:{value:{},disabled:{type:Boolean,default:!1}},setup(b){const u=b,i=B("select",{}),v=B("selectTrigger",{}),y=D(()=>{var w;return((w=i==null?void 0:i.value)==null?void 0:w.value)===u.value});function _(){u.disabled||(i.value.value=u.value,v.open.value=!1)}return(w,p)=>(n(),r("div",{role:"option","aria-selected":y.value,"data-disabled":w.disabled,"data-selected":y.value,class:C(["relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",{"opacity-50 pointer-events-none":w.disabled}]),onClick:_},[m("span",ze,[y.value?(n(),G(e($e),{key:0,class:"h-4 w-4"})):x("",!0)]),$(w.$slots,"default")],10,Ne))}}),De=["disabled"],ee=k({__name:"SelectTrigger",setup(b){const u=B("select",{}),i=S(null),v=S(!1);function y(){var _;(_=u==null?void 0:u.disabled)!=null&&_.value||(v.value=!v.value)}return se("selectTrigger",{open:v,triggerElement:i}),(_,w)=>{var p,l;return n(),r("button",{ref_key:"triggerElement",ref:i,type:"button",onClick:y,disabled:(l=(p=e(u))==null?void 0:p.disabled)==null?void 0:l.value,class:"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"},[$(_.$slots,"default"),s(e(Ue),{class:"ml-2 h-4 w-4 opacity-50"})],8,De)}}}),Be={class:"block truncate"},Le={key:0},Me={key:1,class:"text-muted-foreground"},te=k({__name:"SelectValue",props:{placeholder:{}},setup(b){const u=B("select",{});return(i,v)=>{var y,_;return n(),r("span",Be,[(_=(y=e(u))==null?void 0:y.value)!=null&&_.value?(n(),r("span",Le,[$(i.$slots,"default",{},()=>[d(f(e(u).value.value),1)])])):i.placeholder?(n(),r("span",Me,f(i.placeholder),1)):x("",!0)])}}}),Pe={class:"flex items-center justify-between"},he={class:"mb-4"},Fe={class:"flex flex-wrap gap-1"},je={class:"flex space-x-2"},Oe={class:"grid grid-cols-1 gap-4 md:grid-cols-2"},Ae={class:"space-y-2"},Ie={key:0,class:"text-sm text-red-500"},Ge={class:"space-y-2"},He={key:0,class:"text-sm text-red-500"},Qe={class:"space-y-2"},Xe={key:0,class:"text-sm text-red-500"},Ye={class:"space-y-2"},Je={class:"space-y-2"},Ke={class:"space-y-2"},We={key:0,class:"text-sm text-red-500"},Ze={class:"text-xs text-blue-500"},et={class:"space-y-2"},tt={key:0,class:"text-sm text-red-500"},st={class:"space-y-2"},lt={key:0,class:"text-sm text-red-500"},ot={class:"space-y-2"},at={key:0,class:"text-sm text-red-500"},dt={class:"space-y-2"},nt={key:0,class:"text-sm text-red-500"},rt={class:"space-y-2"},it={key:0,class:"text-sm text-red-500"},ut={class:"flex items-center space-x-2"},mt={key:0,class:"text-sm text-red-500"},pt={class:"grid grid-cols-1 gap-4 md:grid-cols-2"},ft={class:"space-y-2"},ct={key:0,class:"text-sm text-red-500"},vt={class:"space-y-2"},_t={key:0,class:"text-sm text-red-500"},gt={class:"space-y-2"},xt={key:0,class:"text-sm text-red-500"},bt={class:"space-y-2"},yt={class:"space-y-2"},wt={class:"space-y-2"},Vt={key:0,class:"text-sm text-red-500"},kt={class:"text-xs text-blue-500"},$t={class:"space-y-2"},Ut={key:0,class:"text-sm text-red-500"},Ct={class:"space-y-2"},St={key:0,class:"text-sm text-red-500"},Et={class:"space-y-2"},Tt={key:0,class:"text-sm text-red-500"},Rt={class:"space-y-2"},qt={key:0,class:"text-sm text-red-500"},Nt={class:"space-y-2"},zt={key:0,class:"text-sm text-red-500"},Dt={class:"flex items-center space-x-2"},Bt={key:0,class:"text-sm text-red-500"},It=k({__name:"Index",props:{users:{},roles:{}},setup(b){const u=b,i=S(""),v=S(!1),y=S(!1),_=S(null),w=S(!1),p=S([]),l=P({id:null,name:"",email:"",password:"",password_confirmation:"",crp:"",institution:"",birthdate:"",gender:"",phone:"",active:!0,roles:[]});le(p,c=>{l.roles=[...c],console.log("Roles atualizadas:",l.roles)});const H=P({}),oe=D(()=>{if(!i.value)return u.users;const c=i.value.toLowerCase();return u.users.filter(t=>t.name.toLowerCase().includes(c)||t.email.toLowerCase().includes(c)||t.crp&&t.crp.toLowerCase().includes(c)||t.institution&&t.institution.toLowerCase().includes(c))}),ae=()=>{l.reset(),l.clearErrors(),p.value=[],v.value=!0},de=c=>{var t;_.value=c,l.id=c.id,l.name=c.name,l.email=c.email,l.password="",l.password_confirmation="",l.crp=c.crp||"",l.institution=c.institution||"",l.birthdate=c.birthdate||"",l.gender=c.gender||"",l.phone=c.phone||"",l.active=c.active,p.value=((t=c.roles)==null?void 0:t.map(a=>a.id.toString()))||[],l.roles=[...p.value],console.log("Roles carregadas na edição:",l.roles),y.value=!0},ne=c=>{_.value=c,w.value=!0},Q=c=>{const t=p.value.indexOf(c);t===-1?p.value.push(c):p.value.splice(t,1),console.log("Toggle role:",c,"Current roles:",p.value)},X=c=>p.value.includes(c),Y=()=>{if(console.log("Salvando usuário com roles:",l.roles),p.value.length===0){l.setError("roles","The roles field is required.");return}l.roles=[...p.value],l.id?l.put(route("admin.users.update",l.id),{onSuccess:()=>{y.value=!1,_.value=null}}):l.post(route("admin.users.store"),{onSuccess:()=>{v.value=!1}})},re=()=>{_.value&&H.delete(route("admin.users.destroy",_.value.id),{onSuccess:()=>{w.value=!1,_.value=null}})},ie=c=>{P({}).patch(route("admin.users.toggle-status",c.id))},ue=[{title:"Dashboard",href:route("admin.dashboard")},{title:"Usuários",href:route("admin.users.index")}];return(c,t)=>(n(),r(z,null,[s(e(_e),{title:"Gerenciamento de Usuários"}),s(we,{breadcrumbs:ue},{default:o(()=>[s(e(ge),{class:"min-h-screen"},{default:o(()=>[s(e(xe),null,{default:o(()=>[m("div",Pe,[s(e(be),null,{default:o(()=>t[27]||(t[27]=[d("Gerenciamento de Usuários")])),_:1}),s(e(U),{onClick:ae},{default:o(()=>t[28]||(t[28]=[d("Novo Usuário")])),_:1})]),s(e(Ce),null,{default:o(()=>t[29]||(t[29]=[d(" Gerencie todos os usuários do sistema, incluindo psicólogos, estudantes e pacientes. ")])),_:1})]),_:1}),s(e(ye),null,{default:o(()=>[m("div",he,[s(e(V),{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=a=>i.value=a),placeholder:"Buscar usuários por nome, email, CRP ou instituição...",class:"max-w-md"},null,8,["modelValue"])]),s(e(Ee),null,{default:o(()=>[s(e(Re),null,{default:o(()=>[s(e(K),null,{default:o(()=>[s(e(R),null,{default:o(()=>t[30]||(t[30]=[d("Nome")])),_:1}),s(e(R),null,{default:o(()=>t[31]||(t[31]=[d("Email")])),_:1}),s(e(R),null,{default:o(()=>t[32]||(t[32]=[d("Perfis")])),_:1}),s(e(R),null,{default:o(()=>t[33]||(t[33]=[d("CRP")])),_:1}),s(e(R),null,{default:o(()=>t[34]||(t[34]=[d("Instituição")])),_:1}),s(e(R),null,{default:o(()=>t[35]||(t[35]=[d("Status")])),_:1}),s(e(R),null,{default:o(()=>t[36]||(t[36]=[d("Ações")])),_:1})]),_:1})]),_:1}),s(e(Te),null,{default:o(()=>[(n(!0),r(z,null,L(oe.value,a=>(n(),G(e(K),{key:a.id},{default:o(()=>[s(e(T),null,{default:o(()=>[d(f(a.name),1)]),_:2},1024),s(e(T),null,{default:o(()=>[d(f(a.email),1)]),_:2},1024),s(e(T),null,{default:o(()=>[m("div",Fe,[(n(!0),r(z,null,L(a.roles,E=>(n(),r("span",{key:E.id,class:"inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"},f(E.name),1))),128))])]),_:2},1024),s(e(T),null,{default:o(()=>[d(f(a.crp||"-"),1)]),_:2},1024),s(e(T),null,{default:o(()=>[d(f(a.institution||"-"),1)]),_:2},1024),s(e(T),null,{default:o(()=>[m("span",{class:C(["inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",a.active?"bg-green-100 text-green-700":"bg-red-100 text-red-700"])},f(a.active?"Ativo":"Inativo"),3)]),_:2},1024),s(e(T),null,{default:o(()=>[m("div",je,[s(e(U),{variant:"outline",size:"sm",onClick:E=>de(a)},{default:o(()=>t[37]||(t[37]=[d(" Editar ")])),_:2},1032,["onClick"]),s(e(U),{variant:"outline",size:"sm",onClick:E=>ie(a),class:C(a.active?"text-yellow-600":"text-green-600")},{default:o(()=>[d(f(a.active?"Desativar":"Ativar"),1)]),_:2},1032,["onClick","class"]),s(e(U),{variant:"destructive",size:"sm",onClick:E=>ne(a)},{default:o(()=>t[38]||(t[38]=[d(" Excluir ")])),_:2},1032,["onClick"])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1}),s(e(h),{open:v.value,"onUpdate:open":t[12]||(t[12]=a=>v.value=a)},{default:o(()=>[s(e(F),{class:"sm:max-w-md md:max-w-xl"},{default:o(()=>[s(e(j),null,{default:o(()=>[s(e(O),null,{default:o(()=>t[39]||(t[39]=[d("Criar Novo Usuário")])),_:1}),s(e(A),null,{default:o(()=>t[40]||(t[40]=[d(" Preencha os dados para criar um novo usuário no sistema. ")])),_:1})]),_:1}),m("form",{onSubmit:J(Y,["prevent"]),class:"grid gap-4 py-4"},[m("div",Oe,[m("div",Ae,[s(e(g),{for:"name"},{default:o(()=>t[41]||(t[41]=[d("Nome")])),_:1}),s(e(V),{id:"name",modelValue:e(l).name,"onUpdate:modelValue":t[1]||(t[1]=a=>e(l).name=a),required:""},null,8,["modelValue"]),e(l).errors.name?(n(),r("div",Ie,f(e(l).errors.name),1)):x("",!0)]),m("div",Ge,[s(e(g),{for:"email"},{default:o(()=>t[42]||(t[42]=[d("Email")])),_:1}),s(e(V),{id:"email",type:"email",modelValue:e(l).email,"onUpdate:modelValue":t[2]||(t[2]=a=>e(l).email=a),required:""},null,8,["modelValue"]),e(l).errors.email?(n(),r("div",He,f(e(l).errors.email),1)):x("",!0)]),m("div",Qe,[s(e(g),{for:"password"},{default:o(()=>t[43]||(t[43]=[d("Senha")])),_:1}),s(e(V),{id:"password",type:"password",modelValue:e(l).password,"onUpdate:modelValue":t[3]||(t[3]=a=>e(l).password=a),required:""},null,8,["modelValue"]),e(l).errors.password?(n(),r("div",Xe,f(e(l).errors.password),1)):x("",!0)]),m("div",Ye,[s(e(g),{for:"password_confirmation"},{default:o(()=>t[44]||(t[44]=[d("Confirmar Senha")])),_:1}),s(e(V),{id:"password_confirmation",type:"password",modelValue:e(l).password_confirmation,"onUpdate:modelValue":t[4]||(t[4]=a=>e(l).password_confirmation=a),required:""},null,8,["modelValue"])]),m("div",Je,[s(e(g),{for:"roles"},{default:o(()=>t[45]||(t[45]=[d("Perfis")])),_:1}),m("div",Ke,[(n(!0),r(z,null,L(c.roles,a=>(n(),r("div",{key:a.id,class:"flex items-center space-x-2"},[s(e(M),{id:`role-${a.id}`,checked:X(a.id.toString()),"onUpdate:checked":E=>Q(a.id.toString())},null,8,["id","checked","onUpdate:checked"]),s(e(g),{for:`role-${a.id}`},{default:o(()=>[d(f(a.name),1)]),_:2},1032,["for"])]))),128))]),e(l).errors.roles?(n(),r("div",We,f(e(l).errors.roles),1)):x("",!0),m("div",Ze,"Perfis selecionados: "+f(p.value.length),1)]),m("div",et,[s(e(g),{for:"gender"},{default:o(()=>t[46]||(t[46]=[d("Gênero")])),_:1}),s(e(W),{modelValue:e(l).gender,"onUpdate:modelValue":t[5]||(t[5]=a=>e(l).gender=a)},{default:o(()=>[s(e(ee),null,{default:o(()=>[s(e(te),{placeholder:"Selecione o gênero"})]),_:1}),s(e(Z),null,{default:o(()=>[s(e(N),{value:"M"},{default:o(()=>t[47]||(t[47]=[d("Masculino")])),_:1}),s(e(N),{value:"F"},{default:o(()=>t[48]||(t[48]=[d("Feminino")])),_:1}),s(e(N),{value:"O"},{default:o(()=>t[49]||(t[49]=[d("Outro")])),_:1})]),_:1})]),_:1},8,["modelValue"]),e(l).errors.gender?(n(),r("div",tt,f(e(l).errors.gender),1)):x("",!0)]),m("div",st,[s(e(g),{for:"crp"},{default:o(()=>t[50]||(t[50]=[d("CRP (para psicólogos)")])),_:1}),s(e(V),{id:"crp",modelValue:e(l).crp,"onUpdate:modelValue":t[6]||(t[6]=a=>e(l).crp=a)},null,8,["modelValue"]),e(l).errors.crp?(n(),r("div",lt,f(e(l).errors.crp),1)):x("",!0)]),m("div",ot,[s(e(g),{for:"institution"},{default:o(()=>t[51]||(t[51]=[d("Instituição (para estudantes)")])),_:1}),s(e(V),{id:"institution",modelValue:e(l).institution,"onUpdate:modelValue":t[7]||(t[7]=a=>e(l).institution=a)},null,8,["modelValue"]),e(l).errors.institution?(n(),r("div",at,f(e(l).errors.institution),1)):x("",!0)]),m("div",dt,[s(e(g),{for:"birthdate"},{default:o(()=>t[52]||(t[52]=[d("Data de Nascimento")])),_:1}),s(e(V),{id:"birthdate",type:"date",modelValue:e(l).birthdate,"onUpdate:modelValue":t[8]||(t[8]=a=>e(l).birthdate=a)},null,8,["modelValue"]),e(l).errors.birthdate?(n(),r("div",nt,f(e(l).errors.birthdate),1)):x("",!0)]),m("div",rt,[s(e(g),{for:"phone"},{default:o(()=>t[53]||(t[53]=[d("Telefone")])),_:1}),s(e(V),{id:"phone",modelValue:e(l).phone,"onUpdate:modelValue":t[9]||(t[9]=a=>e(l).phone=a)},null,8,["modelValue"]),e(l).errors.phone?(n(),r("div",it,f(e(l).errors.phone),1)):x("",!0)]),m("div",ut,[s(e(M),{id:"active",modelValue:e(l).active,"onUpdate:modelValue":t[10]||(t[10]=a=>e(l).active=a)},null,8,["modelValue"]),s(e(g),{for:"active"},{default:o(()=>t[54]||(t[54]=[d("Usuário Ativo")])),_:1}),e(l).errors.active?(n(),r("div",mt,f(e(l).errors.active),1)):x("",!0)])]),s(e(I),null,{default:o(()=>[s(e(U),{type:"button",variant:"outline",onClick:t[11]||(t[11]=a=>v.value=!1)},{default:o(()=>t[55]||(t[55]=[d("Cancelar")])),_:1}),s(e(U),{type:"submit",disabled:e(l).processing},{default:o(()=>t[56]||(t[56]=[d("Criar Usuário")])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1},8,["open"]),s(e(h),{open:y.value,"onUpdate:open":t[24]||(t[24]=a=>y.value=a)},{default:o(()=>[s(e(F),{class:"sm:max-w-md md:max-w-xl"},{default:o(()=>[s(e(j),null,{default:o(()=>[s(e(O),null,{default:o(()=>t[57]||(t[57]=[d("Editar Usuário")])),_:1}),s(e(A),null,{default:o(()=>{var a;return[d(" Modifique os dados do usuário "+f((a=_.value)==null?void 0:a.name)+". ",1)]}),_:1})]),_:1}),m("form",{onSubmit:J(Y,["prevent"]),class:"grid gap-4 py-4"},[m("div",pt,[m("div",ft,[s(e(g),{for:"edit-name"},{default:o(()=>t[58]||(t[58]=[d("Nome")])),_:1}),s(e(V),{id:"edit-name",modelValue:e(l).name,"onUpdate:modelValue":t[13]||(t[13]=a=>e(l).name=a),required:""},null,8,["modelValue"]),e(l).errors.name?(n(),r("div",ct,f(e(l).errors.name),1)):x("",!0)]),m("div",vt,[s(e(g),{for:"edit-email"},{default:o(()=>t[59]||(t[59]=[d("Email")])),_:1}),s(e(V),{id:"edit-email",type:"email",modelValue:e(l).email,"onUpdate:modelValue":t[14]||(t[14]=a=>e(l).email=a),required:""},null,8,["modelValue"]),e(l).errors.email?(n(),r("div",_t,f(e(l).errors.email),1)):x("",!0)]),m("div",gt,[s(e(g),{for:"edit-password"},{default:o(()=>t[60]||(t[60]=[d("Nova Senha (opcional)")])),_:1}),s(e(V),{id:"edit-password",type:"password",modelValue:e(l).password,"onUpdate:modelValue":t[15]||(t[15]=a=>e(l).password=a)},null,8,["modelValue"]),e(l).errors.password?(n(),r("div",xt,f(e(l).errors.password),1)):x("",!0)]),m("div",bt,[s(e(g),{for:"edit-password_confirmation"},{default:o(()=>t[61]||(t[61]=[d("Confirmar Nova Senha")])),_:1}),s(e(V),{id:"edit-password_confirmation",type:"password",modelValue:e(l).password_confirmation,"onUpdate:modelValue":t[16]||(t[16]=a=>e(l).password_confirmation=a)},null,8,["modelValue"])]),m("div",yt,[s(e(g),{for:"edit-roles"},{default:o(()=>t[62]||(t[62]=[d("Perfis")])),_:1}),m("div",wt,[(n(!0),r(z,null,L(c.roles,a=>(n(),r("div",{key:a.id,class:"flex items-center space-x-2"},[s(e(M),{id:`edit-role-${a.id}`,checked:X(a.id.toString()),"onUpdate:checked":E=>Q(a.id.toString())},null,8,["id","checked","onUpdate:checked"]),s(e(g),{for:`edit-role-${a.id}`},{default:o(()=>[d(f(a.name),1)]),_:2},1032,["for"])]))),128))]),e(l).errors.roles?(n(),r("div",Vt,f(e(l).errors.roles),1)):x("",!0),m("div",kt,"Perfis selecionados: "+f(p.value.length),1)]),m("div",$t,[s(e(g),{for:"edit-gender"},{default:o(()=>t[63]||(t[63]=[d("Gênero")])),_:1}),s(e(W),{modelValue:e(l).gender,"onUpdate:modelValue":t[17]||(t[17]=a=>e(l).gender=a)},{default:o(()=>[s(e(ee),null,{default:o(()=>[s(e(te),{placeholder:"Selecione o gênero"})]),_:1}),s(e(Z),null,{default:o(()=>[s(e(N),{value:"M"},{default:o(()=>t[64]||(t[64]=[d("Masculino")])),_:1}),s(e(N),{value:"F"},{default:o(()=>t[65]||(t[65]=[d("Feminino")])),_:1}),s(e(N),{value:"O"},{default:o(()=>t[66]||(t[66]=[d("Outro")])),_:1})]),_:1})]),_:1},8,["modelValue"]),e(l).errors.gender?(n(),r("div",Ut,f(e(l).errors.gender),1)):x("",!0)]),m("div",Ct,[s(e(g),{for:"edit-crp"},{default:o(()=>t[67]||(t[67]=[d("CRP (para psicólogos)")])),_:1}),s(e(V),{id:"edit-crp",modelValue:e(l).crp,"onUpdate:modelValue":t[18]||(t[18]=a=>e(l).crp=a)},null,8,["modelValue"]),e(l).errors.crp?(n(),r("div",St,f(e(l).errors.crp),1)):x("",!0)]),m("div",Et,[s(e(g),{for:"edit-institution"},{default:o(()=>t[68]||(t[68]=[d("Instituição (para estudantes)")])),_:1}),s(e(V),{id:"edit-institution",modelValue:e(l).institution,"onUpdate:modelValue":t[19]||(t[19]=a=>e(l).institution=a)},null,8,["modelValue"]),e(l).errors.institution?(n(),r("div",Tt,f(e(l).errors.institution),1)):x("",!0)]),m("div",Rt,[s(e(g),{for:"edit-birthdate"},{default:o(()=>t[69]||(t[69]=[d("Data de Nascimento")])),_:1}),s(e(V),{id:"edit-birthdate",type:"date",modelValue:e(l).birthdate,"onUpdate:modelValue":t[20]||(t[20]=a=>e(l).birthdate=a)},null,8,["modelValue"]),e(l).errors.birthdate?(n(),r("div",qt,f(e(l).errors.birthdate),1)):x("",!0)]),m("div",Nt,[s(e(g),{for:"edit-phone"},{default:o(()=>t[70]||(t[70]=[d("Telefone")])),_:1}),s(e(V),{id:"edit-phone",modelValue:e(l).phone,"onUpdate:modelValue":t[21]||(t[21]=a=>e(l).phone=a)},null,8,["modelValue"]),e(l).errors.phone?(n(),r("div",zt,f(e(l).errors.phone),1)):x("",!0)]),m("div",Dt,[s(e(M),{id:"edit-active",modelValue:e(l).active,"onUpdate:modelValue":t[22]||(t[22]=a=>e(l).active=a)},null,8,["modelValue"]),s(e(g),{for:"edit-active"},{default:o(()=>t[71]||(t[71]=[d("Usuário Ativo")])),_:1}),e(l).errors.active?(n(),r("div",Bt,f(e(l).errors.active),1)):x("",!0)])]),s(e(I),null,{default:o(()=>[s(e(U),{type:"button",variant:"outline",onClick:t[23]||(t[23]=a=>y.value=!1)},{default:o(()=>t[72]||(t[72]=[d("Cancelar")])),_:1}),s(e(U),{type:"submit",disabled:e(l).processing},{default:o(()=>t[73]||(t[73]=[d("Salvar Alterações")])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1},8,["open"]),s(e(h),{open:w.value,"onUpdate:open":t[26]||(t[26]=a=>w.value=a)},{default:o(()=>[s(e(F),null,{default:o(()=>[s(e(j),null,{default:o(()=>[s(e(O),null,{default:o(()=>t[74]||(t[74]=[d("Confirmar Exclusão")])),_:1}),s(e(A),null,{default:o(()=>{var a;return[d(" Tem certeza que deseja excluir o usuário "+f((a=_.value)==null?void 0:a.name)+"?",1),t[75]||(t[75]=m("br",null,null,-1)),t[76]||(t[76]=d(" Esta ação não pode ser desfeita. "))]}),_:1})]),_:1}),s(e(I),null,{default:o(()=>[s(e(U),{type:"button",variant:"outline",onClick:t[25]||(t[25]=a=>w.value=!1)},{default:o(()=>t[77]||(t[77]=[d("Cancelar")])),_:1}),s(e(U),{type:"button",variant:"destructive",onClick:re,disabled:e(H).processing},{default:o(()=>t[78]||(t[78]=[d(" Excluir Usuário ")])),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1},8,["open"])]),_:1})],64))}});export{It as default};
