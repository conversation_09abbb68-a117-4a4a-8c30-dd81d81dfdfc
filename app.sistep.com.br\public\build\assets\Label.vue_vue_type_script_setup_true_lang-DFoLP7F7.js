import{d as n,D as c,M as m,u as t,o as u,a as p,n as f,N as b,c as g,g as x,B as _,w as V,r as v}from"./app-DIEHtcz0.js";import{a as d,V as h}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{u as w}from"./index-Cree0lnl.js";const M=n({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(o,{emit:a}){const s=o,e=w(s,"modelValue",a,{passive:!0,defaultValue:s.defaultValue});return(k,r)=>c((u(),p("input",{"onUpdate:modelValue":r[0]||(r[0]=i=>b(e)?e.value=i:null),class:f(t(d)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s.class))},null,2)),[[m,t(e)]])}}),D=n({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(o){const a=o,s=g(()=>{const{class:l,...e}=a;return e});return(l,e)=>(u(),x(t(h),_(s.value,{class:t(d)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a.class)}),{default:V(()=>[v(l.$slots,"default")]),_:3},16,["class"]))}});export{M as _,D as a};
