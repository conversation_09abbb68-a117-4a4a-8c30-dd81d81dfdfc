import{d as _,C as b,g as n,o as m,w as r,e as o,a as x,x as u,b as a,u as s,m as y,t as V,z as k,h as i}from"./app-DIEHtcz0.js";import{_ as f}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{_ as c}from"./TextLink.vue_vue_type_script_setup_true_lang-DFPAzajK.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{_ as $}from"./Checkbox.vue_vue_type_script_setup_true_lang-t1diozls.js";import{a as p,_ as g}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{L,_ as B}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DAnhOjOB.js";import"./index-Cree0lnl.js";const C={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},N={class:"grid gap-6"},P={class:"grid gap-2"},h={class:"grid gap-2"},q={class:"flex items-center justify-between"},E={class:"flex items-center justify-between",tabindex:3},R={class:"text-center text-sm text-muted-foreground"},G=_({__name:"Login",props:{status:{},canResetPassword:{type:Boolean}},setup(S){const t=b({email:"",password:"",remember:!1}),w=()=>{t.post(route("login"),{onFinish:()=>t.reset("password")})};return(d,e)=>(m(),n(B,{title:"Log in to your account",description:"Enter your email and password below to log in"},{default:r(()=>[o(s(y),{title:"Log in"}),d.status?(m(),x("div",C,V(d.status),1)):u("",!0),a("form",{onSubmit:k(w,["prevent"]),class:"flex flex-col gap-6"},[a("div",N,[a("div",P,[o(s(p),{for:"email"},{default:r(()=>e[3]||(e[3]=[i("Email address")])),_:1}),o(s(g),{id:"email",type:"email",required:"",autofocus:"",tabindex:1,autocomplete:"email",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=l=>s(t).email=l),placeholder:"<EMAIL>"},null,8,["modelValue"]),o(f,{message:s(t).errors.email},null,8,["message"])]),a("div",h,[a("div",q,[o(s(p),{for:"password"},{default:r(()=>e[4]||(e[4]=[i("Password")])),_:1}),d.canResetPassword?(m(),n(c,{key:0,href:d.route("password.request"),class:"text-sm",tabindex:5},{default:r(()=>e[5]||(e[5]=[i(" Forgot password? ")])),_:1},8,["href"])):u("",!0)]),o(s(g),{id:"password",type:"password",required:"",tabindex:2,autocomplete:"current-password",modelValue:s(t).password,"onUpdate:modelValue":e[1]||(e[1]=l=>s(t).password=l),placeholder:"Password"},null,8,["modelValue"]),o(f,{message:s(t).errors.password},null,8,["message"])]),a("div",E,[o(s(p),{for:"remember",class:"flex items-center space-x-3"},{default:r(()=>[o(s($),{id:"remember",checked:s(t).remember,"onUpdate:checked":e[2]||(e[2]=l=>s(t).remember=l),tabindex:4},null,8,["checked"]),e[6]||(e[6]=a("span",null,"Remember me",-1))]),_:1})]),o(s(v),{type:"submit",class:"mt-4 w-full",tabindex:4,disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s(L),{key:0,class:"h-4 w-4 animate-spin"})):u("",!0),e[7]||(e[7]=i(" Log in "))]),_:1},8,["disabled"])]),a("div",R,[e[9]||(e[9]=i(" Don't have an account? ")),o(c,{href:d.route("register"),tabindex:5},{default:r(()=>e[8]||(e[8]=[i("Sign up")])),_:1},8,["href"])])],32)]),_:1}))}});export{G as default};
