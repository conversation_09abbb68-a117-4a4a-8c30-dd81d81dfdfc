import{d as g,k as w,C as v,g as y,o as V,w as r,e as a,u as s,m as b,b as t,z as k,h as d,I,D as x,J as C}from"./app-DIEHtcz0.js";import{_ as u}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-MMQe0pvK.js";import{_ as $,a as P}from"./Layout.vue_vue_type_script_setup_true_lang-CQrs9Z1l.js";import{_ as N}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{a as c,_ as m}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import"./index-Cree0lnl.js";const E={class:"space-y-6"},T={class:"grid gap-2"},U={class:"grid gap-2"},B={class:"grid gap-2"},M={class:"flex items-center gap-4"},h={class:"text-sm text-neutral-600"},F=g({__name:"Password",setup(D){const f=[{title:"Password settings",href:"/settings/password"}],l=w(null),p=w(null),e=v({current_password:"",password:"",password_confirmation:""}),_=()=>{e.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>e.reset(),onError:i=>{i.password&&(e.reset("password","password_confirmation"),l.value instanceof HTMLInputElement&&l.value.focus()),i.current_password&&(e.reset("current_password"),p.value instanceof HTMLInputElement&&p.value.focus())}})};return(i,o)=>(V(),y(S,{breadcrumbs:f},{default:r(()=>[a(s(b),{title:"Password settings"}),a($,null,{default:r(()=>[t("div",E,[a(P,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),t("form",{onSubmit:k(_,["prevent"]),class:"space-y-6"},[t("div",T,[a(s(c),{for:"current_password"},{default:r(()=>o[3]||(o[3]=[d("Current password")])),_:1}),a(s(m),{id:"current_password",ref_key:"currentPasswordInput",ref:p,modelValue:s(e).current_password,"onUpdate:modelValue":o[0]||(o[0]=n=>s(e).current_password=n),type:"password",class:"mt-1 block w-full",autocomplete:"current-password",placeholder:"Current password"},null,8,["modelValue"]),a(u,{message:s(e).errors.current_password},null,8,["message"])]),t("div",U,[a(s(c),{for:"password"},{default:r(()=>o[4]||(o[4]=[d("New password")])),_:1}),a(s(m),{id:"password",ref_key:"passwordInput",ref:l,modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=n=>s(e).password=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"New password"},null,8,["modelValue"]),a(u,{message:s(e).errors.password},null,8,["message"])]),t("div",B,[a(s(c),{for:"password_confirmation"},{default:r(()=>o[5]||(o[5]=[d("Confirm password")])),_:1}),a(s(m),{id:"password_confirmation",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=n=>s(e).password_confirmation=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"Confirm password"},null,8,["modelValue"]),a(u,{message:s(e).errors.password_confirmation},null,8,["message"])]),t("div",M,[a(s(N),{disabled:s(e).processing},{default:r(()=>o[6]||(o[6]=[d("Save password")])),_:1},8,["disabled"]),a(I,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:r(()=>[x(t("p",h,"Saved.",512),[[C,s(e).recentlySuccessful]])]),_:1})])],32)])]),_:1})]),_:1}))}});export{F as default};
