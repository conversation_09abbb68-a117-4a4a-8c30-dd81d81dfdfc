import{d as w,g as x,o as _,u as e,K as S,L as U,w as o,r as D,k as P,C as E,a as V,e as s,b as l,h as i,A as B,m as I,z as R,x as k,I as h,D as z,J as A}from"./app-DIEHtcz0.js";import{a as N,_ as q}from"./Layout.vue_vue_type_script_setup_true_lang-CQrs9Z1l.js";import{_ as f}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{X as M,o as X,_ as b}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{a as F,b as J,c as K,d as L,e as O,_ as W}from"./DialogTitle.vue_vue_type_script_setup_true_lang-BLre35X4.js";import{a as c,_ as v}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{_ as j,a as C}from"./RadioGroupItem.vue_vue_type_script_setup_true_lang-CqEb8i6m.js";import{_ as G}from"./AppLayout.vue_vue_type_script_setup_true_lang-MMQe0pvK.js";import"./index-Cree0lnl.js";const H=w({__name:"DialogClose",props:{asChild:{type:Boolean},as:{}},setup(g){const u=g;return(r,n)=>(_(),x(e(M),S(U(u)),{default:o(()=>[D(r.$slots,"default")]),_:3},16))}}),Q=w({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{}},setup(g){const u=g;return(r,n)=>(_(),x(e(X),S(U(u)),{default:o(()=>[D(r.$slots,"default")]),_:3},16))}}),Y={class:"space-y-6"},Z={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},ee={class:"grid gap-2"},se=w({__name:"DeleteUser",setup(g){const u=P(null),r=E({password:""}),n=p=>{p.preventDefault(),r.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>y(),onError:()=>{var t;return(t=u.value)==null?void 0:t.focus()},onFinish:()=>r.reset()})},y=()=>{r.clearErrors(),r.reset()};return(p,t)=>(_(),V("div",Y,[s(N,{title:"Delete account",description:"Delete your account and all of its resources"}),l("div",Z,[t[7]||(t[7]=l("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[l("p",{class:"font-medium"},"Warning"),l("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),s(e(W),null,{default:o(()=>[s(e(Q),{"as-child":""},{default:o(()=>[s(e(b),{variant:"destructive"},{default:o(()=>t[1]||(t[1]=[i("Delete account")])),_:1})]),_:1}),s(e(F),null,{default:o(()=>[l("form",{class:"space-y-6",onSubmit:n},[s(e(J),{class:"space-y-3"},{default:o(()=>[s(e(K),null,{default:o(()=>t[2]||(t[2]=[i("Are you sure you want to delete your account?")])),_:1}),s(e(L),null,{default:o(()=>t[3]||(t[3]=[i(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1})]),_:1}),l("div",ee,[s(e(c),{for:"password",class:"sr-only"},{default:o(()=>t[4]||(t[4]=[i("Password")])),_:1}),s(e(v),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:u,modelValue:e(r).password,"onUpdate:modelValue":t[0]||(t[0]=$=>e(r).password=$),placeholder:"Password"},null,8,["modelValue"]),s(f,{message:e(r).errors.password},null,8,["message"])]),s(e(O),{class:"gap-2"},{default:o(()=>[s(e(H),{"as-child":""},{default:o(()=>[s(e(b),{variant:"secondary",onClick:y},{default:o(()=>t[5]||(t[5]=[i(" Cancel ")])),_:1})]),_:1}),s(e(b),{variant:"destructive",disabled:e(r).processing},{default:o(()=>t[6]||(t[6]=[l("button",{type:"submit"},"Delete account",-1)])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),te={class:"flex flex-col space-y-6"},ae={class:"grid gap-2"},oe={class:"grid gap-2"},le={class:"grid gap-2"},re={class:"flex items-center space-x-2"},ne={class:"flex items-center space-x-2"},ie={key:0,class:"grid gap-2"},de={key:1,class:"grid gap-2"},ue={class:"flex items-center gap-4"},pe={class:"text-sm text-neutral-600"},$e=w({__name:"Profile",props:{status:{}},setup(g){const u=[{title:"Configurações de perfil",href:"/settings/profile"}],n=B().props.auth.user,y=()=>n.roles&&n.roles.some(m=>m.name==="psychologist")?"psychologist":n.roles&&n.roles.some(m=>m.name==="student")?"student":"patient",p=P(y()),t=E({name:n.name,email:n.email,user_type:y(),crp:n.crp||"",institution:n.institution||""}),$=m=>{p.value=m,t.user_type=m},T=()=>{t.patch(route("profile.update"),{preserveScroll:!0})};return(m,a)=>(_(),x(G,{breadcrumbs:u},{default:o(()=>[s(e(I),{title:"Configurações de perfil"}),s(q,null,{default:o(()=>[l("div",te,[s(N,{title:"Informações de perfil",description:"Atualize seus dados pessoais"}),l("form",{onSubmit:R(T,["prevent"]),class:"space-y-6"},[l("div",ae,[s(e(c),{for:"name"},{default:o(()=>a[5]||(a[5]=[i("Nome")])),_:1}),s(e(v),{id:"name",class:"mt-1 block w-full",modelValue:e(t).name,"onUpdate:modelValue":a[0]||(a[0]=d=>e(t).name=d),required:"",autocomplete:"name",placeholder:"Nome completo"},null,8,["modelValue"]),s(f,{class:"mt-2",message:e(t).errors.name},null,8,["message"])]),l("div",oe,[s(e(c),{for:"email"},{default:o(()=>a[6]||(a[6]=[i("Email")])),_:1}),s(e(v),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e(t).email,"onUpdate:modelValue":a[1]||(a[1]=d=>e(t).email=d),required:"",autocomplete:"username",placeholder:"Email"},null,8,["modelValue"]),s(f,{class:"mt-2",message:e(t).errors.email},null,8,["message"])]),l("div",le,[a[9]||(a[9]=l("div",{class:"mb-2"},"Tipo de Usuário",-1)),s(e(j),{modelValue:p.value,"onUpdate:modelValue":[a[2]||(a[2]=d=>p.value=d),$],class:"flex flex-col space-y-2"},{default:o(()=>[l("div",re,[s(e(C),{value:"psychologist",id:"user_type_psychologist"}),s(e(c),{for:"user_type_psychologist",class:"cursor-pointer"},{default:o(()=>a[7]||(a[7]=[i("Sou Psicólogo")])),_:1})]),l("div",ne,[s(e(C),{value:"student",id:"user_type_student"}),s(e(c),{for:"user_type_student",class:"cursor-pointer"},{default:o(()=>a[8]||(a[8]=[i("Sou Estudante")])),_:1})])]),_:1},8,["modelValue"]),s(f,{class:"mt-2",message:e(t).errors.user_type},null,8,["message"])]),p.value==="psychologist"?(_(),V("div",ie,[s(e(c),{for:"crp"},{default:o(()=>a[10]||(a[10]=[i("CRP (Registro no Conselho Regional de Psicologia)")])),_:1}),s(e(v),{id:"crp",class:"mt-1 block w-full",modelValue:e(t).crp,"onUpdate:modelValue":a[3]||(a[3]=d=>e(t).crp=d),placeholder:"Ex: 123456-7"},null,8,["modelValue"]),s(f,{class:"mt-2",message:e(t).errors.crp},null,8,["message"])])):k("",!0),p.value==="student"?(_(),V("div",de,[s(e(c),{for:"institution"},{default:o(()=>a[11]||(a[11]=[i("Instituição de Ensino")])),_:1}),s(e(v),{id:"institution",class:"mt-1 block w-full",modelValue:e(t).institution,"onUpdate:modelValue":a[4]||(a[4]=d=>e(t).institution=d),placeholder:"Nome da instituição"},null,8,["modelValue"]),s(f,{class:"mt-2",message:e(t).errors.institution},null,8,["message"])])):k("",!0),l("div",ue,[s(e(b),{disabled:e(t).processing},{default:o(()=>a[12]||(a[12]=[i("Salvar")])),_:1},8,["disabled"]),s(h,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:o(()=>[z(l("p",pe,"Salvo.",512),[[A,e(t).recentlySuccessful]])]),_:1})])],32)]),s(se)]),_:1})]),_:1}))}});export{$e as default};
