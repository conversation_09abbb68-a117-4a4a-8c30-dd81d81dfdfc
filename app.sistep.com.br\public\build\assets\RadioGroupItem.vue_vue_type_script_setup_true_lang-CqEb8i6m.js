import{u as f}from"./index-Cree0lnl.js";import{d as u,p as v,a as c,o as p,r as m,j as b,c as l,b as i,u as _}from"./app-DIEHtcz0.js";const h={class:"grid gap-2",role:"radiogroup"},$=u({__name:"RadioGroup",props:{modelValue:{},disabled:{type:Boolean,default:!1},name:{default:""}},emits:["update:modelValue"],setup(d,{emit:a}){const e=d,o=f(e,"modelValue",a);return v("radioGroup",{value:o,disabled:e.disabled,name:e.name}),(s,r)=>(p(),c("div",h,[m(s.$slots,"default")]))}}),g={class:"flex items-center space-x-2"},y=["id","value","name","checked","disabled"],k=["for"],w=u({__name:"RadioGroupItem",props:{value:{},disabled:{type:Boolean,default:!1},id:{default:void 0}},setup(d){const a=d,e=b("radioGroup",{value:{value:null},disabled:!1,name:""}),t=l(()=>e.value.value===a.value),o=l(()=>a.disabled||e.disabled),s=l(()=>a.id||`radio-${a.value}`);function r(){o.value||(e.value.value=a.value)}return(n,B)=>(p(),c("div",g,[i("input",{type:"radio",id:s.value,value:n.value,name:_(e).name,checked:t.value,disabled:o.value,onChange:r,class:"h-4 w-4 cursor-pointer rounded-full border border-primary text-primary shadow focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},null,40,y),i("label",{for:s.value,class:"text-sm font-medium leading-none cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"},[m(n.$slots,"default")],8,k)]))}});export{$ as _,w as a};
