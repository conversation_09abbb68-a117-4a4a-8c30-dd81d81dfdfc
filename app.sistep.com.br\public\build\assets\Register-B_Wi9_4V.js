import{d as q,k as E,C as z,c as _,q as O,g as x,o as i,w as d,e as r,b as o,u as e,m as B,a as u,x as f,n as v,z as H,h as n,D as R,E as L}from"./app-DIEHtcz0.js";import{_ as g}from"./InputError.vue_vue_type_script_setup_true_lang-D8h8hfl2.js";import{_ as M}from"./TextLink.vue_vue_type_script_setup_true_lang-DFPAzajK.js";import{c as h,_ as S}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-gahKXgGh.js";import{a as c,_ as y}from"./Label.vue_vue_type_script_setup_true_lang-DFoLP7F7.js";import{_ as D,a as N}from"./RadioGroupItem.vue_vue_type_script_setup_true_lang-CqEb8i6m.js";import{L as F,_ as J}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DAnhOjOB.js";import"./index-Cree0lnl.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=h("ArrowLeftIcon",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=h("ArrowRightIcon",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=h("CircleAlertIcon",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=h("CircleCheckIcon",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),Q={class:"flex flex-col items-center justify-center mb-6"},W={key:0,class:"w-full mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm"},X={class:"flex items-center gap-2 font-medium"},Y={class:"flex items-center justify-center w-full mt-6"},Z={class:"flex items-center w-full max-w-xs"},ee={key:0},se={key:1},te={key:2},re={key:0},oe={key:1},ae={key:2},le={key:0},ie={key:1},ne={key:0,class:"space-y-4"},de={class:"grid gap-2"},ue={class:"grid gap-2"},me={key:1,class:"space-y-4"},ge={class:"grid gap-2"},ce={class:"flex items-center space-x-2"},pe={class:"flex items-center space-x-2"},fe={key:0,class:"grid gap-2"},ye={key:1,class:"grid gap-2"},xe={key:2,class:"space-y-4"},ve={class:"grid gap-2"},ke={class:"grid gap-2"},be={class:"grid gap-3"},_e={class:"flex items-start"},we={class:"flex items-start"},he={class:"flex justify-between mt-6"},Ve={key:1},Ce={class:"flex justify-center gap-1 pt-12 text-sm text-gray-600 dark:text-gray-400"},j=3,Ae=q({__name:"Register",setup(Ee){const p=E("psychologist"),a=E(1),V=E(!1),t=z({name:"",email:"",password:"",password_confirmation:"",terms:!1,tcle:!1,user_type:"psychologist",crp:"",institution:""}),k=_(()=>Object.keys(t.errors).some(m=>["name","email"].includes(m))),b=_(()=>Object.keys(t.errors).some(m=>["user_type","crp","institution"].includes(m))),C=_(()=>Object.keys(t.errors).some(m=>["password","password_confirmation","terms","tcle"].includes(m))),A=()=>{k.value?a.value=1:b.value?a.value=2:C.value&&(a.value=3)};O(()=>Object.keys(t.errors).length,m=>{m>0&&V.value&&(A(),V.value=!1)});const $=_(()=>{if(a.value===1)return t.name&&t.email;if(a.value===2){if(p.value==="psychologist")return t.crp;if(p.value==="student")return t.institution}else if(a.value===3)return t.password&&t.password_confirmation&&t.terms&&t.tcle;return!1}),U=()=>{a.value<j&&a.value++},I=()=>{a.value>1&&a.value--},P=()=>{V.value=!0,t.user_type=p.value,t.post(route("register"),{onFinish:()=>{t.reset("password","password_confirmation")}})};return(m,s)=>(i(),x(J,null,{default:d(()=>[r(e(B),{title:"Registrar"}),o("div",Q,[s[14]||(s[14]=o("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Criar conta",-1)),s[15]||(s[15]=o("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-400"}," Cadastre-se para acessar todos os recursos. ",-1)),Object.keys(e(t).errors).length>0?(i(),u("div",W,[o("div",X,[r(e(w),{class:"h-4 w-4"}),s[13]||(s[13]=o("span",null,"Existem erros no formulário que precisam ser corrigidos",-1))])])):f("",!0),o("div",Y,[o("div",Z,[o("div",{class:v(["flex items-center justify-center w-8 h-8 rounded-full cursor-pointer",[a.value>=1?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600",k.value?"ring-2 ring-red-500 ring-offset-2":""]]),onClick:s[0]||(s[0]=l=>a.value=1)},[a.value>1&&!k.value?(i(),u("span",ee,[r(e(T),{class:"w-5 h-5"})])):k.value?(i(),u("span",se,[r(e(w),{class:"w-5 h-5 text-red-500"})])):(i(),u("span",te,"1"))],2),o("div",{class:v(["flex-1 h-1",a.value>1?"bg-indigo-600":"bg-gray-300"])},null,2),o("div",{class:v(["flex items-center justify-center w-8 h-8 rounded-full cursor-pointer",[a.value>=2?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600",b.value?"ring-2 ring-red-500 ring-offset-2":""]]),onClick:s[1]||(s[1]=l=>a.value=2)},[a.value>2&&!b.value?(i(),u("span",re,[r(e(T),{class:"w-5 h-5"})])):b.value?(i(),u("span",oe,[r(e(w),{class:"w-5 h-5 text-red-500"})])):(i(),u("span",ae,"2"))],2),o("div",{class:v(["flex-1 h-1",a.value>2?"bg-indigo-600":"bg-gray-300"])},null,2),o("div",{class:v(["flex items-center justify-center w-8 h-8 rounded-full cursor-pointer",[a.value>=3?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600",C.value?"ring-2 ring-red-500 ring-offset-2":""]]),onClick:s[2]||(s[2]=l=>a.value=3)},[C.value?(i(),u("span",le,[r(e(w),{class:"w-5 h-5 text-red-500"})])):(i(),u("span",ie,"3"))],2)])])]),o("form",{onSubmit:s[12]||(s[12]=H(l=>a.value===j?P():U(),["prevent"])),class:"space-y-4"},[a.value===1?(i(),u("div",ne,[o("div",de,[r(e(c),{for:"name"},{default:d(()=>s[16]||(s[16]=[n("Nome")])),_:1}),r(e(y),{id:"name",autocomplete:"name",modelValue:e(t).name,"onUpdate:modelValue":s[3]||(s[3]=l=>e(t).name=l),placeholder:"Nome completo",disabled:e(t).processing,tabindex:1},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.name},null,8,["message"])]),o("div",ue,[r(e(c),{for:"email"},{default:d(()=>s[17]||(s[17]=[n("Email")])),_:1}),r(e(y),{id:"email",type:"email",autocomplete:"email",modelValue:e(t).email,"onUpdate:modelValue":s[4]||(s[4]=l=>e(t).email=l),placeholder:"Email",disabled:e(t).processing,tabindex:2},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.email},null,8,["message"])])])):f("",!0),a.value===2?(i(),u("div",me,[o("div",ge,[r(e(c),null,{default:d(()=>s[18]||(s[18]=[n("Tipo de Usuário")])),_:1}),r(e(D),{modelValue:p.value,"onUpdate:modelValue":s[5]||(s[5]=l=>p.value=l),class:"flex flex-col space-y-2",tabindex:3},{default:d(()=>[o("div",ce,[r(e(N),{value:"psychologist",id:"user_type_psychologist"}),r(e(c),{for:"user_type_psychologist",class:"cursor-pointer"},{default:d(()=>s[19]||(s[19]=[n("Sou Psicólogo(a)")])),_:1})]),o("div",pe,[r(e(N),{value:"student",id:"user_type_student"}),r(e(c),{for:"user_type_student",class:"cursor-pointer"},{default:d(()=>s[20]||(s[20]=[n("Sou Estudante")])),_:1})])]),_:1},8,["modelValue"]),r(g,{message:e(t).errors.user_type},null,8,["message"])]),p.value==="psychologist"?(i(),u("div",fe,[r(e(c),{for:"crp"},{default:d(()=>s[21]||(s[21]=[n("CRP (Registro no Conselho Regional de Psicologia)")])),_:1}),r(e(y),{id:"crp",modelValue:e(t).crp,"onUpdate:modelValue":s[6]||(s[6]=l=>e(t).crp=l),placeholder:"Ex: 123456-7",disabled:e(t).processing,tabindex:4},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.crp},null,8,["message"])])):f("",!0),p.value==="student"?(i(),u("div",ye,[r(e(c),{for:"institution"},{default:d(()=>s[22]||(s[22]=[n("Instituição de Ensino")])),_:1}),r(e(y),{id:"institution",modelValue:e(t).institution,"onUpdate:modelValue":s[7]||(s[7]=l=>e(t).institution=l),placeholder:"Nome da instituição",disabled:e(t).processing,tabindex:4},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.institution},null,8,["message"])])):f("",!0)])):f("",!0),a.value===3?(i(),u("div",xe,[o("div",ve,[r(e(c),{for:"password"},{default:d(()=>s[23]||(s[23]=[n("Crie uma Senha")])),_:1}),r(e(y),{id:"password",type:"password",autocomplete:"new-password",modelValue:e(t).password,"onUpdate:modelValue":s[8]||(s[8]=l=>e(t).password=l),placeholder:"Senha",disabled:e(t).processing,tabindex:5},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.password},null,8,["message"])]),o("div",ke,[r(e(c),{for:"password_confirmation"},{default:d(()=>s[24]||(s[24]=[n("Repita a Senha")])),_:1}),r(e(y),{id:"password_confirmation",type:"password",autocomplete:"new-password",modelValue:e(t).password_confirmation,"onUpdate:modelValue":s[9]||(s[9]=l=>e(t).password_confirmation=l),placeholder:"Confirmar senha",disabled:e(t).processing,tabindex:6},null,8,["modelValue","disabled"]),r(g,{message:e(t).errors.password_confirmation},null,8,["message"])]),o("div",be,[o("label",_e,[R(o("input",{type:"checkbox",name:"terms",id:"terms","onUpdate:modelValue":s[10]||(s[10]=l=>e(t).terms=l),class:"mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-900 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800",tabindex:7},null,512),[[L,e(t).terms]]),s[25]||(s[25]=o("span",{class:"ml-2 text-xs text-gray-600 dark:text-gray-400"},[n(" Eu concordo com os "),o("a",{class:"underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800",href:"/terms",target:"_blank"}," Termos de Serviço "),n(" e "),o("a",{class:"underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800",href:"/privacy-policy",target:"_blank"}," Política de Privacidade ")],-1))]),o("label",we,[R(o("input",{type:"checkbox",name:"tcle",id:"tcle","onUpdate:modelValue":s[11]||(s[11]=l=>e(t).tcle=l),class:"mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-900 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800",tabindex:8},null,512),[[L,e(t).tcle]]),s[26]||(s[26]=o("span",{class:"ml-2 text-xs text-gray-600 dark:text-gray-400"},[n(" Eu li e concordo com o "),o("a",{class:"underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800",href:"/tcle",target:"_blank"}," Termo de Consentimento Livre e Esclarecido (TCLE) ")],-1))]),r(g,{class:"mt-2",message:e(t).errors.terms},null,8,["message"]),r(g,{class:"mt-2",message:e(t).errors.tcle},null,8,["message"])])])):f("",!0),o("div",he,[a.value>1?(i(),x(e(S),{key:0,type:"button",variant:"outline",onClick:I,disabled:e(t).processing},{default:d(()=>[r(e(G),{class:"mr-2 h-4 w-4"}),s[27]||(s[27]=n(" Voltar "))]),_:1},8,["disabled"])):(i(),u("div",Ve)),a.value<j?(i(),x(e(S),{key:2,type:"button",onClick:U,disabled:!$.value||e(t).processing},{default:d(()=>[s[28]||(s[28]=n(" Próximo ")),r(e(K),{class:"ml-2 h-4 w-4"})]),_:1},8,["disabled"])):(i(),x(e(S),{key:3,type:"submit",disabled:!$.value||e(t).processing},{default:d(()=>[e(t).processing?(i(),x(e(F),{key:0,class:"mr-2 h-4 w-4 animate-spin"})):f("",!0),s[29]||(s[29]=n(" Registrar "))]),_:1},8,["disabled"]))]),o("div",Ce,[s[31]||(s[31]=o("span",null,"Já tem uma conta?",-1)),r(M,{href:m.route("login"),tabindex:9},{default:d(()=>s[30]||(s[30]=[n("Entre aqui")])),_:1},8,["href"])])],32)]),_:1}))}});export{Ae as default};
