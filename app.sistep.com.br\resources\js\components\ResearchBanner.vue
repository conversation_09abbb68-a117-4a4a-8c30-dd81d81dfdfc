<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { AlertTriangle } from 'lucide-vue-next'

const isAlertOpen = ref(false)

// Detectar contexto de pesquisa
const isResearchContext = computed(() => {
    return typeof localStorage !== 'undefined' && localStorage.getItem('researchParticipantId')
})

const currentStep = computed(() => {
    if (typeof localStorage !== 'undefined') {
        return parseInt(localStorage.getItem('researchCurrentStep') || '1', 10)
    }
    return 1
})

const canProceedToNext = computed(() => {
    return currentStep.value < 5
})

// Ícone precisa de atenção quando pode prosseguir para próxima etapa
const needsAttention = computed(() => {
    return canProceedToNext.value && currentStep.value > 1
})

// Mostrar ícone apenas quando não estiver na página /pesquisa
const shouldShowIcon = computed(() => {
    return isResearchContext.value && window.location.pathname !== '/pesquisa'
})

// Processar parâmetros da URL quando o componente for montado
onMounted(() => {
    if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search)
        const queryParticipantId = urlParams.get('participant_id')
        const queryCurrentStep = urlParams.get('current_step')
        const stepCompleted = urlParams.get('stepCompleted')

        // Se já existe um participant_id no localStorage, use ele
        let participantId = localStorage.getItem('researchParticipantId')
        let currentStepValue = parseInt(localStorage.getItem('researchCurrentStep') || '1', 10)

        // Override com parâmetros da URL se existirem
        if (queryParticipantId) {
            participantId = queryParticipantId
            localStorage.setItem('researchParticipantId', participantId)
        }

        // Se current_step vier na URL, usar ele (navegação entre Laravel/Nuxt)
        if (queryCurrentStep && participantId) {
            currentStepValue = parseInt(queryCurrentStep, 10)
            localStorage.setItem('researchCurrentStep', currentStepValue.toString())
        }

        // Processar etapa completada (retorno de etapa externa)
        if (stepCompleted && participantId) {
            if (stepCompleted === 'dass') {
                currentStepValue = 3 // Move para etapa do Painel
            } else if (stepCompleted === 'panel') {
                currentStepValue = 4 // Move para etapa dos Forms
            }
            localStorage.setItem('researchCurrentStep', currentStepValue.toString())
        }
        
        // Limpar a URL removendo os parâmetros se eles existirem
        if (queryParticipantId || queryCurrentStep || stepCompleted) {
            const newUrl = window.location.pathname + window.location.hash
            window.history.replaceState({}, document.title, newUrl)
        }
    }
})

// Funções
function toggleAlert() {
    isAlertOpen.value = !isAlertOpen.value
}

function closeAlert() {
    isAlertOpen.value = false
}

function getCurrentStepDescription() {
    const descriptions: Record<number, string> = {
        1: 'Aceite do TCLE.',
        2: 'Teste DASS-21.',
        3: 'Painel do psicólogo.',
        4: 'Questionário no Google Forms.'
    }
    return descriptions[currentStep.value] || 'Continue sua participação.'
}

function getNextStepButtonText() {
    const texts: Record<number, string> = {
        1: 'Aceitar TCLE.',
        2: 'Executar DASS-21.',
        3: 'Acessar painel.',
        4: 'Responder Google Forms.'
    }
    return texts[currentStep.value] || 'Continuar'
}

function goToResearchPage() {
    isAlertOpen.value = false
    // Para o Laravel, vamos redirecionar para o Nuxt passando a etapa atual
    const participantId = localStorage.getItem('researchParticipantId')
    const currentStepParam = currentStep.value
    window.location.href = `http://localhost:3000/pesquisa?participant_id=${participantId}&current_step=${currentStepParam}`
}

function proceedToNextStep() {
    isAlertOpen.value = false
    
    // Emitir evento para que a página pesquisa.vue possa reagir
    if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('research-proceed-next', { 
            detail: { currentStep: currentStep.value } 
        }))
    }
    
    // Se não estiver na página de pesquisa, navegar para lá passando a etapa atual
    if (window.location.pathname !== '/pesquisa') {
        const participantId = localStorage.getItem('researchParticipantId')
        const currentStepParam = currentStep.value
        window.location.href = `http://localhost:3000/pesquisa?participant_id=${participantId}&current_step=${currentStepParam}`
    }
}
</script>

<template>
    <!-- Banner da Pesquisa (aparece no topo quando aberto) -->
    <div v-if="isResearchContext && isAlertOpen"
        class="fixed mx-auto top-0 left-0 right-0 max-w-7xl w-full bg-amber-50 border-b border-amber-200 p-3 z-50 shadow-sm rounded-lg mt-4">
        <div class="mx-auto">
            <!-- Desktop Layout -->
            <div class="hidden md:flex items-center justify-between text-sm">
                <div class="flex flex-1 flex-row items-center justify-between gap-4">
                    <span class="text-amber-800">
                        Você está participando da pesquisa de usabilidade da plataforma SISTEP e está na etapa {{
                        currentStep }} de 4. {{ getCurrentStepDescription() }}
                    </span>
                    <div class="ml-auto flex items-center gap-3 text-amber-700">
                        <a @click="goToResearchPage"
                            class="underline hover:no-underline cursor-pointer whitespace-nowrap">
                            ← Voltar para a pesquisa
                        </a>
                        <span v-if="canProceedToNext" class="text-amber-400">•</span>
                        <a v-if="canProceedToNext" @click="proceedToNextStep"
                            class="underline hover:no-underline cursor-pointer font-medium whitespace-nowrap">
                            {{ getNextStepButtonText() }} →
                        </a>
                    </div>
                </div>
                <button @click="closeAlert" class="text-amber-600 hover:text-amber-800 text-lg leading-none ml-4">
                    ×
                </button>
            </div>

            <!-- Mobile Layout -->
            <div class="md:hidden text-sm">
                <div class="flex items-start justify-between mb-2">
                    <span class="text-amber-800 text-xs leading-relaxed pr-4">
                        Você está participando da pesquisa de usabilidade da plataforma SISTEP e está na etapa {{
                        currentStep }} de 4.
                    </span>
                    <button @click="closeAlert"
                        class="text-amber-600 hover:text-amber-800 text-lg leading-none flex-shrink-0">
                        ×
                    </button>
                </div>
                <div class="text-amber-800 text-xs mb-3">
                    {{ getCurrentStepDescription() }}
                </div>
                <div class="flex items-center gap-3 text-amber-700 text-xs">
                    <a @click="goToResearchPage" class="underline hover:no-underline cursor-pointer">
                        ← Voltar para a pesquisa
                    </a>
                    <span v-if="canProceedToNext" class="text-amber-400">•</span>
                    <a v-if="canProceedToNext" @click="proceedToNextStep"
                        class="underline hover:no-underline cursor-pointer font-medium">
                        {{ getNextStepButtonText() }} →
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Ícone indicador da Pesquisa -->
    <div v-if="shouldShowIcon" class="flex items-center gap-2">
        <button @click="toggleAlert" class="relative p-1.5 rounded-full transition-all duration-200" :class="needsAttention ? 
                'bg-amber-100 hover:bg-amber-200 text-amber-600' : 
                'bg-muted hover:bg-accent'"
            :title="needsAttention ? 'Ação necessária na pesquisa' : 'Status da pesquisa'">
            <AlertTriangle class="w-5 h-5" />

            <!-- Indicador de ação necessária -->
            <div v-if="needsAttention"
                class="absolute top-0 right-0 w-2 h-2 bg-amber-500 rounded-full animate-pulse">
            </div>
        </button>
    </div>

    <!-- Overlay quando banner está aberto -->
    <div v-if="isResearchContext && isAlertOpen" class="fixed inset-0 bg-black/25 z-40 pointer-events-none">
    </div>
</template>

<style scoped>
/* Animação suave para o banner */
.bg-amber-50 {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style> 