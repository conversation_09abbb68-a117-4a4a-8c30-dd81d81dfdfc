<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { provide } from 'vue'

interface Props {
  modelValue: string | null
  disabled?: boolean
  name?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  name: '',
})

const emit = defineEmits(['update:modelValue'])

const model = useVModel(props, 'modelValue', emit)

provide('radioGroup', {
  value: model,
  disabled: props.disabled,
  name: props.name,
})
</script>

<template>
  <div
    class="grid gap-2"
    role="radiogroup"
  >
    <slot />
  </div>
</template> 