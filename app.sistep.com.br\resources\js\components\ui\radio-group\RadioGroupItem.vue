<script setup lang="ts">
import { computed, inject } from 'vue'

interface Props {
  value: string
  disabled?: boolean
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  id: undefined
})

const radioGroup: any = inject('radioGroup', { value: { value: null }, disabled: false, name: '' })

const isChecked = computed(() => radioGroup.value.value === props.value)
const isDisabled = computed(() => props.disabled || radioGroup.disabled)
const inputId = computed(() => props.id || `radio-${props.value}`)

function handleChange() {
  if (isDisabled.value) return
  radioGroup.value.value = props.value
}
</script>

<template>
  <div class="flex items-center space-x-2">
    <input
      type="radio"
      :id="inputId"
      :value="value"
      :name="radioGroup.name"
      :checked="isChecked"
      :disabled="isDisabled"
      @change="handleChange"
      class="h-4 w-4 cursor-pointer rounded-full border border-primary text-primary shadow focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
    />
    <label
      :for="inputId"
      class="text-sm font-medium leading-none cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
    >
      <slot />
    </label>
  </div>
</template> 