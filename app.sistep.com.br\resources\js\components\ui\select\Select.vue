<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { computed, provide, toRef } from 'vue'

interface Props {
  modelValue: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits(['update:modelValue'])

const model = useVModel(props, 'modelValue', emit)

provide('select', {
  value: model,
  disabled: toRef(props, 'disabled'),
})

defineExpose({
  value: computed(() => model.value),
})
</script>

<template>
  <div class="relative">
    <slot />
  </div>
</template> 