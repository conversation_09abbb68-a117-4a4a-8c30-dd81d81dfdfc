<script setup lang="ts">
import { computed, inject, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

const selectTrigger: any = inject('selectTrigger', {})
const contentElement = ref<HTMLDivElement | null>(null)

const isOpen = computed(() => selectTrigger?.open?.value || false)
const triggerEl = computed(() => selectTrigger?.triggerElement?.value || null)

function positionDropdown() {
  if (!contentElement.value || !triggerEl.value) return
  
  const triggerRect = triggerEl.value.getBoundingClientRect()
  const contentRect = contentElement.value.getBoundingClientRect()
  
  contentElement.value.style.width = `${triggerRect.width}px`
  contentElement.value.style.top = `${triggerRect.bottom + window.scrollY + 5}px`
  contentElement.value.style.left = `${triggerRect.left + window.scrollX}px`
}

function handleClickOutside(event: MouseEvent) {
  if (
    contentElement.value && 
    !contentElement.value.contains(event.target as Node) &&
    triggerEl.value && 
    !triggerEl.value.contains(event.target as Node)
  ) {
    selectTrigger.open.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', positionDropdown)
  document.addEventListener('mousedown', handleClickOutside)
  
  watch(isOpen, (newVal) => {
    if (newVal) {
      nextTick(() => {
        positionDropdown()
      })
    }
  }, { immediate: true })
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', positionDropdown)
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<template>
  <teleport to="body">
    <div
      v-if="isOpen"
      ref="contentElement"
      class="absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
    >
      <slot />
    </div>
  </teleport>
</template> 