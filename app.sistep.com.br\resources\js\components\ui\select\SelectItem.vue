<script setup lang="ts">
import { Check } from 'lucide-vue-next'
import { computed, inject } from 'vue'

interface Props {
  value: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const select: any = inject('select', {})
const selectTrigger: any = inject('selectTrigger', {})

const isSelected = computed(() => select?.value?.value === props.value)

function handleSelect() {
  if (props.disabled) return
  
  select.value.value = props.value
  selectTrigger.open.value = false
}
</script>

<template>
  <div
    role="option"
    :aria-selected="isSelected"
    :data-disabled="disabled"
    :data-selected="isSelected"
    class="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
    :class="{ 'opacity-50 pointer-events-none': disabled }"
    @click="handleSelect"
  >
    <span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <Check v-if="isSelected" class="h-4 w-4" />
    </span>
    <slot />
  </div>
</template> 