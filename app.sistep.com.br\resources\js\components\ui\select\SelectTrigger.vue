<script setup lang="ts">
import { ChevronDown } from 'lucide-vue-next'
import { inject, provide, ref } from 'vue'

const select: any = inject('select', {})
const triggerElement = ref<HTMLButtonElement | null>(null)

const open = ref(false)

function toggleDropdown() {
  if (select?.disabled?.value) {
    return
  }
  open.value = !open.value
}

// Fornecer o estado e referência para os componentes filhos
provide('selectTrigger', {
  open,
  triggerElement,
})
</script>

<template>
  <button
    ref="triggerElement"
    type="button"
    @click="toggleDropdown"
    :disabled="select?.disabled?.value"
    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
  >
    <slot />
    <ChevronDown class="ml-2 h-4 w-4 opacity-50" />
  </button>
</template> 