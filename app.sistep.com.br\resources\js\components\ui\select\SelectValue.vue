<script setup lang="ts">
import { inject } from 'vue'

interface Props {
  placeholder?: string
}

const props = defineProps<Props>()
const select: any = inject('select', {})
</script>

<template>
  <span class="block truncate">
    <span v-if="select?.value?.value">
      <slot>{{ select.value.value }}</slot>
    </span>
    <span v-else-if="placeholder" class="text-muted-foreground">
      {{ placeholder }}
    </span>
  </span>
</template> 