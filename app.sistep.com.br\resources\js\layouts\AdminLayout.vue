<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import type { BreadcrumbItem, SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Props {
    breadcrumbs?: BreadcrumbItem[];
}

interface Role {
    id: number;
    name: string;
}

interface UserWithRoles {
    id: number;
    name: string;
    email: string;
    roles?: Role[];
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});

const page = usePage<SharedData>();
const user = computed(() => page.props.auth.user as unknown as UserWithRoles);
const hasAdminRole = computed(() => {
    return user.value?.roles?.some((role: Role) => role.name === 'admin');
});
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <div v-if="hasAdminRole">
            <slot />
        </div>
        <div v-else class="p-4">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Acesso negado!</strong>
                <span class="block sm:inline"> Você não tem permissão para acessar esta área.</span>
            </div>
        </div>
    </AppLayout>
</template>
