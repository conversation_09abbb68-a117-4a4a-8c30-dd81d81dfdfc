<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import PlaceholderPattern from '../components/PlaceholderPattern.vue';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Backend Requirements for Research Integration:
// 1. Add route to handle research participants: GET /research/participant/{participant_id}
// 2. Display DASS-21 results for research participants
// 3. Show professional analysis view vs patient view
// 4. Implement redirect back to research page after analysis
// 5. Add research context indicator when participant_id and research_return_url are present
// 6. Store research participant test results in database for retrieval

</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <!-- Research Context Notice (to be implemented) -->
            <!-- Show this when accessing via research context -->
            <!-- Example: v-if="$page.props.research_context" -->
            
            <div class="grid auto-rows-min gap-4 md:grid-cols-3">
                <div class="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                    <PlaceholderPattern />
                    <!-- Future: Research participant results card -->
                </div>
                <div class="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                    <PlaceholderPattern />
                    <!-- Future: Professional analysis tools -->
                </div>
                <div class="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                    <PlaceholderPattern />
                    <!-- Future: Research navigation -->
                </div>
            </div>
            <div class="relative min-h-[100vh] flex-1 rounded-xl border border-sidebar-border/70 dark:border-sidebar-border md:min-h-min">
                <PlaceholderPattern />
                <!-- Future: DASS-21 Results Display for Research Context -->
                <!-- This area will show the professional view of DASS-21 results -->
                <!-- when accessed via research participant flow -->
            </div>
        </div>
    </AppLayout>
</template>
