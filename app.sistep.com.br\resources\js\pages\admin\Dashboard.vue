<script setup lang="ts">
import AdminLayout from '@/layouts/AdminLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { UsersIcon, FilesIcon, TestTubeIcon, ClipboardCheckIcon } from 'lucide-vue-next';

interface Props {
  usersCount: number;
  rolesCount: number;
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: route('admin.dashboard'),
  },
];
</script>

<template>
  <Head title="Painel administrativo" />

  <AdminLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 p-4">
      <h1 class="text-2xl font-bold">Dashboard</h1>
      <p class="text-muted-foreground">Bem-vindo ao painel administrativo do SISTEP.</p>
      
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Usuários</CardTitle>
            <UsersIcon class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ usersCount }}</div>
            <p class="text-xs text-muted-foreground">
              Total de usuários cadastrados
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Perfis</CardTitle>
            <FilesIcon class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ rolesCount }}</div>
            <p class="text-xs text-muted-foreground">
              Perfis de acesso disponíveis
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Testes</CardTitle>
            <TestTubeIcon class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">--</div>
            <p class="text-xs text-muted-foreground">
              Total de testes disponíveis
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">Avaliações</CardTitle>
            <ClipboardCheckIcon class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">--</div>
            <p class="text-xs text-muted-foreground">
              Total de avaliações realizadas
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Atividade recente</CardTitle>
          </CardHeader>
          <CardContent>
            <p class="text-muted-foreground">Não há atividades recentes para exibir.</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Estatísticas de uso</CardTitle>
          </CardHeader>
          <CardContent>
            <p class="text-muted-foreground">Não há estatísticas disponíveis no momento.</p>
          </CardContent>
        </Card>
      </div>
    </div>
  </AdminLayout>
</template>
