<script setup lang="ts">
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table/index';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import type { User, Role } from '@/types';

interface Props {
  users: User[];
  roles: Role[];
}

const props = defineProps<Props>();
const searchQuery = ref('');
const isCreatingUser = ref(false);
const isEditingUser = ref(false);
const selectedUser = ref<User | null>(null);
const confirmingUserDeletion = ref(false);

// Estado separado para controlar as roles selecionadas
const selectedRoles = ref<string[]>([]);

// Formulário para criar/editar usuário
const form = useForm({
  id: null as number | null,
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  crp: '',
  institution: '',
  birthdate: '',
  gender: '',
  phone: '',
  active: true as boolean,
  roles: [] as string[] // Alterado para string[] para compatibilidade com Checkbox
});

// Atualiza o form.roles sempre que selectedRoles mudar
watch(selectedRoles, (newRoles) => {
  form.roles = [...newRoles];
  console.log('Roles atualizadas:', form.roles);
});

// Formulário para confirmar exclusão
const deleteForm = useForm({});

// Lista de usuários filtrada pela busca
const filteredUsers = computed(() => {
  if (!searchQuery.value) return props.users;
  
  const query = searchQuery.value.toLowerCase();
  return props.users.filter(user => 
    user.name.toLowerCase().includes(query) || 
    user.email.toLowerCase().includes(query) ||
    (user.crp && user.crp.toLowerCase().includes(query)) ||
    (user.institution && user.institution.toLowerCase().includes(query))
  );
});

// Função para abrir o modal de criação de usuário
const openCreateUserModal = () => {
  form.reset();
  form.clearErrors();
  selectedRoles.value = [];
  isCreatingUser.value = true;
};

// Função para abrir o modal de edição de usuário
const openEditUserModal = (user: User) => {
  selectedUser.value = user;
  form.id = user.id;
  form.name = user.name;
  form.email = user.email;
  form.password = '';
  form.password_confirmation = '';
  form.crp = user.crp || '';
  form.institution = user.institution || '';
  form.birthdate = user.birthdate || '';
  form.gender = user.gender || '';
  form.phone = user.phone || '';
  form.active = user.active;
  
  // Atualiza selectedRoles com os roles do usuário
  selectedRoles.value = user.roles?.map(role => role.id.toString()) || [];
  form.roles = [...selectedRoles.value]; // Copia para o formulário
  console.log('Roles carregadas na edição:', form.roles);
  
  isEditingUser.value = true;
};

// Função para confirmar exclusão de usuário
const confirmUserDeletion = (user: User) => {
  selectedUser.value = user;
  confirmingUserDeletion.value = true;
};

// Função para alternar uma role na seleção
const toggleRole = (roleId: string) => {
  const index = selectedRoles.value.indexOf(roleId);
  if (index === -1) {
    selectedRoles.value.push(roleId);
  } else {
    selectedRoles.value.splice(index, 1);
  }
  console.log('Toggle role:', roleId, 'Current roles:', selectedRoles.value);
};

// Verificar se uma role está selecionada
const isRoleSelected = (roleId: string) => {
  return selectedRoles.value.includes(roleId);
};

// Função para salvar usuário (criar ou editar)
const saveUser = () => {
  console.log('Salvando usuário com roles:', form.roles);
  
  // Certifique-se de que roles seja um array não vazio
  if (selectedRoles.value.length === 0) {
    form.setError('roles', 'The roles field is required.');
    return;
  }
  
  // Transfere as roles selecionadas para o formulário
  form.roles = [...selectedRoles.value];
  
  if (form.id) {
    // Editando usuário existente
    form.put(route('admin.users.update', form.id), {
      onSuccess: () => {
        isEditingUser.value = false;
        selectedUser.value = null;
      }
    });
  } else {
    // Criando novo usuário
    form.post(route('admin.users.store'), {
      onSuccess: () => {
        isCreatingUser.value = false;
      }
    });
  }
};

// Função para excluir usuário
const deleteUser = () => {
  if (!selectedUser.value) return;
  
  deleteForm.delete(route('admin.users.destroy', selectedUser.value.id), {
    onSuccess: () => {
      confirmingUserDeletion.value = false;
      selectedUser.value = null;
    }
  });
};

// Função para alternar o status de um usuário
const toggleUserStatus = (user: User) => {
  const toggleForm = useForm({});
  toggleForm.patch(route('admin.users.toggle-status', user.id));
};

// Breadcrumbs
const breadcrumbs = [
  { title: 'Dashboard', href: route('admin.dashboard') },
  { title: 'Usuários', href: route('admin.users.index') },
];
</script>

<template>
  <Head title="Gerenciamento de Usuários" />

  <AdminLayout :breadcrumbs="breadcrumbs">
    <Card class="min-h-screen">
      <CardHeader>
        <div class="flex items-center justify-between">
          <CardTitle>Gerenciamento de Usuários</CardTitle>
          <Button @click="openCreateUserModal">Novo Usuário</Button>
        </div>
        <CardDescription>
          Gerencie todos os usuários do sistema, incluindo psicólogos, estudantes e pacientes.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="mb-4">
          <Input 
            v-model="searchQuery"
            placeholder="Buscar usuários por nome, email, CRP ou instituição..." 
            class="max-w-md"
          />
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Perfis</TableHead>
              <TableHead>CRP</TableHead>
              <TableHead>Instituição</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="user in filteredUsers" :key="user.id">
              <TableCell>{{ user.name }}</TableCell>
              <TableCell>{{ user.email }}</TableCell>
              <TableCell>
                <div class="flex flex-wrap gap-1">
                  <span 
                    v-for="role in user.roles" 
                    :key="role.id"
                    class="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                  >
                    {{ role.name }}
                  </span>
                </div>
              </TableCell>
              <TableCell>{{ user.crp || '-' }}</TableCell>
              <TableCell>{{ user.institution || '-' }}</TableCell>
              <TableCell>
                <span 
                  :class="[
                    'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                    user.active 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-red-100 text-red-700'
                  ]"
                >
                  {{ user.active ? 'Ativo' : 'Inativo' }}
                </span>
              </TableCell>
              <TableCell>
                <div class="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    @click="openEditUserModal(user)"
                  >
                    Editar
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    @click="toggleUserStatus(user)"
                    :class="user.active ? 'text-yellow-600' : 'text-green-600'"
                  >
                    {{ user.active ? 'Desativar' : 'Ativar' }}
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    @click="confirmUserDeletion(user)"
                  >
                    Excluir
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    <!-- Modal de Criação de Usuário -->
    <Dialog v-model:open="isCreatingUser">
      <DialogContent class="sm:max-w-md md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Criar Novo Usuário</DialogTitle>
          <DialogDescription>
            Preencha os dados para criar um novo usuário no sistema.
          </DialogDescription>
        </DialogHeader>

        <form @submit.prevent="saveUser" class="grid gap-4 py-4">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="space-y-2">
              <Label for="name">Nome</Label>
              <Input id="name" v-model="form.name" required />
              <div v-if="form.errors.name" class="text-sm text-red-500">{{ form.errors.name }}</div>
            </div>

            <div class="space-y-2">
              <Label for="email">Email</Label>
              <Input id="email" type="email" v-model="form.email" required />
              <div v-if="form.errors.email" class="text-sm text-red-500">{{ form.errors.email }}</div>
            </div>

            <div class="space-y-2">
              <Label for="password">Senha</Label>
              <Input id="password" type="password" v-model="form.password" required />
              <div v-if="form.errors.password" class="text-sm text-red-500">{{ form.errors.password }}</div>
            </div>

            <div class="space-y-2">
              <Label for="password_confirmation">Confirmar Senha</Label>
              <Input id="password_confirmation" type="password" v-model="form.password_confirmation" required />
            </div>

            <div class="space-y-2">
              <Label for="roles">Perfis</Label>
              <div class="space-y-2">
                <div v-for="role in roles" :key="role.id" class="flex items-center space-x-2">
                  <Checkbox 
                    :id="`role-${role.id}`" 
                    :checked="isRoleSelected(role.id.toString())"
                    @update:checked="toggleRole(role.id.toString())"
                  />
                  <Label :for="`role-${role.id}`">{{ role.name }}</Label>
                </div>
              </div>
              <div v-if="form.errors.roles" class="text-sm text-red-500">{{ form.errors.roles }}</div>
              <div class="text-xs text-blue-500">Perfis selecionados: {{ selectedRoles.length }}</div>
            </div>

            <div class="space-y-2">
              <Label for="gender">Gênero</Label>
              <Select v-model="form.gender">
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o gênero" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="M">Masculino</SelectItem>
                  <SelectItem value="F">Feminino</SelectItem>
                  <SelectItem value="O">Outro</SelectItem>
                </SelectContent>
              </Select>
              <div v-if="form.errors.gender" class="text-sm text-red-500">{{ form.errors.gender }}</div>
            </div>

            <div class="space-y-2">
              <Label for="crp">CRP (para psicólogos)</Label>
              <Input id="crp" v-model="form.crp" />
              <div v-if="form.errors.crp" class="text-sm text-red-500">{{ form.errors.crp }}</div>
            </div>

            <div class="space-y-2">
              <Label for="institution">Instituição (para estudantes)</Label>
              <Input id="institution" v-model="form.institution" />
              <div v-if="form.errors.institution" class="text-sm text-red-500">{{ form.errors.institution }}</div>
            </div>

            <div class="space-y-2">
              <Label for="birthdate">Data de Nascimento</Label>
              <Input id="birthdate" type="date" v-model="form.birthdate" />
              <div v-if="form.errors.birthdate" class="text-sm text-red-500">{{ form.errors.birthdate }}</div>
            </div>

            <div class="space-y-2">
              <Label for="phone">Telefone</Label>
              <Input id="phone" v-model="form.phone" />
              <div v-if="form.errors.phone" class="text-sm text-red-500">{{ form.errors.phone }}</div>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox id="active" v-model="form.active" />
              <Label for="active">Usuário Ativo</Label>
              <div v-if="form.errors.active" class="text-sm text-red-500">{{ form.errors.active }}</div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" @click="isCreatingUser = false">Cancelar</Button>
            <Button type="submit" :disabled="form.processing">Criar Usuário</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>

    <!-- Modal de Edição de Usuário -->
    <Dialog v-model:open="isEditingUser">
      <DialogContent class="sm:max-w-md md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Editar Usuário</DialogTitle>
          <DialogDescription>
            Modifique os dados do usuário {{ selectedUser?.name }}.
          </DialogDescription>
        </DialogHeader>

        <form @submit.prevent="saveUser" class="grid gap-4 py-4">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="space-y-2">
              <Label for="edit-name">Nome</Label>
              <Input id="edit-name" v-model="form.name" required />
              <div v-if="form.errors.name" class="text-sm text-red-500">{{ form.errors.name }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-email">Email</Label>
              <Input id="edit-email" type="email" v-model="form.email" required />
              <div v-if="form.errors.email" class="text-sm text-red-500">{{ form.errors.email }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-password">Nova Senha (opcional)</Label>
              <Input id="edit-password" type="password" v-model="form.password" />
              <div v-if="form.errors.password" class="text-sm text-red-500">{{ form.errors.password }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-password_confirmation">Confirmar Nova Senha</Label>
              <Input id="edit-password_confirmation" type="password" v-model="form.password_confirmation" />
            </div>

            <div class="space-y-2">
              <Label for="edit-roles">Perfis</Label>
              <div class="space-y-2">
                <div v-for="role in roles" :key="role.id" class="flex items-center space-x-2">
                  <Checkbox 
                    :id="`edit-role-${role.id}`" 
                    :checked="isRoleSelected(role.id.toString())"
                    @update:checked="toggleRole(role.id.toString())"
                  />
                  <Label :for="`edit-role-${role.id}`">{{ role.name }}</Label>
                </div>
              </div>
              <div v-if="form.errors.roles" class="text-sm text-red-500">{{ form.errors.roles }}</div>
              <div class="text-xs text-blue-500">Perfis selecionados: {{ selectedRoles.length }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-gender">Gênero</Label>
              <Select v-model="form.gender">
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o gênero" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="M">Masculino</SelectItem>
                  <SelectItem value="F">Feminino</SelectItem>
                  <SelectItem value="O">Outro</SelectItem>
                </SelectContent>
              </Select>
              <div v-if="form.errors.gender" class="text-sm text-red-500">{{ form.errors.gender }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-crp">CRP (para psicólogos)</Label>
              <Input id="edit-crp" v-model="form.crp" />
              <div v-if="form.errors.crp" class="text-sm text-red-500">{{ form.errors.crp }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-institution">Instituição (para estudantes)</Label>
              <Input id="edit-institution" v-model="form.institution" />
              <div v-if="form.errors.institution" class="text-sm text-red-500">{{ form.errors.institution }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-birthdate">Data de Nascimento</Label>
              <Input id="edit-birthdate" type="date" v-model="form.birthdate" />
              <div v-if="form.errors.birthdate" class="text-sm text-red-500">{{ form.errors.birthdate }}</div>
            </div>

            <div class="space-y-2">
              <Label for="edit-phone">Telefone</Label>
              <Input id="edit-phone" v-model="form.phone" />
              <div v-if="form.errors.phone" class="text-sm text-red-500">{{ form.errors.phone }}</div>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox id="edit-active" v-model="form.active" />
              <Label for="edit-active">Usuário Ativo</Label>
              <div v-if="form.errors.active" class="text-sm text-red-500">{{ form.errors.active }}</div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" @click="isEditingUser = false">Cancelar</Button>
            <Button type="submit" :disabled="form.processing">Salvar Alterações</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>

    <!-- Modal de Confirmação de Exclusão -->
    <Dialog v-model:open="confirmingUserDeletion">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirmar Exclusão</DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir o usuário {{ selectedUser?.name }}?<br>
            Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button type="button" variant="outline" @click="confirmingUserDeletion = false">Cancelar</Button>
          <Button 
            type="button" 
            variant="destructive" 
            @click="deleteUser"
            :disabled="deleteForm.processing"
          >
            Excluir Usuário
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AdminLayout>
</template> 