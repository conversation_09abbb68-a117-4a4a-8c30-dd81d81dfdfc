<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';

import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group/';
import AuthLayout from '@/layouts/AuthLayout.vue';
import { LoaderCircle } from 'lucide-vue-next';

const userType = ref('psychologist');

// Detect research context
const isResearchContext = computed(() => {
    return typeof localStorage !== 'undefined' && localStorage.getItem('researchParticipantId');
});

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    terms: false as boolean,
    tcle: false as boolean,
    user_type: 'psychologist',
    crp: '',
    institution: '',
});

// Auto-check TCLE in research context
onMounted(() => {
    if (isResearchContext.value) {
        form.tcle = true;
    }
});

const isFormValid = computed(() => {
    const baseValid = form.name &&
        form.email &&
        form.password &&
        form.password_confirmation &&
        form.terms &&
        form.tcle;

    if (userType.value === 'psychologist') {
        return baseValid && form.crp;
    } else if (userType.value === 'student') {
        return baseValid && form.institution;
    }

    return baseValid;
});

const submit = () => {
    form.user_type = userType.value;
    form.post(route('register'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
        },
    });
};
</script>

<template>
    <AuthLayout>

        <Head title="Registrar" />

        <div class="flex flex-col items-center justify-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Criar conta</h1>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Cadastre-se para acessar todos os recursos.
            </p>
        </div>

        <form @submit.prevent="submit" class="flex flex-col gap-6">
            <div class="flex flex-col gap-2">
                <!-- Informações básicas -->
                <div class="grid gap-2">
                    <Label for="name">Nome</Label>
                    <Input id="name" name="name" autocomplete="name" v-model="form.name" placeholder="Nome completo"
                        :disabled="form.processing" :tabindex="1" />
                    <InputError :message="form.errors.name" />
                </div>

                <div class="grid gap-2">
                    <Label for="email">Email</Label>
                    <Input id="email" name="email" type="email" autocomplete="email" v-model="form.email" placeholder="Email"
                        :disabled="form.processing" :tabindex="2" />
                    <InputError :message="form.errors.email" />
                </div>
            </div>

            <div class="flex flex-col gap-6">
                <!-- Tipo de usuário e dados específicos -->
                <div class="grid gap-2">
                    <RadioGroup v-model="userType" class="flex flex-col" :tabindex="3">
                        <div class="flex items-center space-x-2">
                            <RadioGroupItem value="psychologist" id="user_type_psychologist" />
                            <Label for="user_type_psychologist" class="cursor-pointer">Sou Psicólogo(a)</Label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <RadioGroupItem value="student" id="user_type_student" />
                            <Label for="user_type_student" class="cursor-pointer">Sou Estudante</Label>
                        </div>
                    </RadioGroup>
                    <InputError :message="form.errors.user_type" />
                </div>

                <!-- Campos específicos por tipo de usuário -->
                <div v-if="userType === 'psychologist'" class="grid gap-2">
                    <Label for="crp">CRP (Registro no Conselho Regional de Psicologia)</Label>
                    <Input id="crp" name="crp" v-model="form.crp" placeholder="Ex: 123456-7" :disabled="form.processing"
                        :tabindex="4" />
                    <InputError :message="form.errors.crp" />
                </div>

                <div v-if="userType === 'student'" class="grid gap-2">
                    <Label for="institution">Instituição de Ensino</Label>
                    <Input id="institution" name="institution" v-model="form.institution" placeholder="Nome da instituição"
                        :disabled="form.processing" :tabindex="4" />
                    <InputError :message="form.errors.institution" />
                </div>
            </div>

            <div class="grid gap-2">
                <!-- Senha -->
                <div class="grid gap-2">
                    <Label for="password">Crie uma Senha</Label>
                    <Input id="password" name="password" type="password" autocomplete="new-password" v-model="form.password"
                        placeholder="Senha" :disabled="form.processing" :tabindex="5" />
                    <InputError :message="form.errors.password" />
                </div>

                <div class="grid gap-2">
                    <Label for="password_confirmation">Repita a Senha</Label>
                    <Input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password"
                        v-model="form.password_confirmation" placeholder="Confirmar senha" :disabled="form.processing"
                        :tabindex="6" />
                    <InputError :message="form.errors.password_confirmation" />
                </div>
            </div>

            <!-- Termos e TCLE -->
            <div class="grid gap-3">
                <label class="flex items-start">
                    <input type="checkbox" name="terms" id="terms" :checked="form.terms" :disabled="form.processing"
                        @change="form.terms = ($event.target as HTMLInputElement).checked"
                        class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-900 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800"
                        :tabindex="7" />
                    <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">
                        Eu concordo com os
                        <a class="underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                            href="/terms" target="_blank">
                            Termos de Serviço
                        </a>
                        e
                        <a class="underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                            href="/privacy-policy" target="_blank">
                            Política de Privacidade
                        </a>
                    </span>
                </label>

                <label class="flex items-start">
                    <input type="checkbox" name="tcle" id="tcle" :checked="form.tcle"
                        @change="!isResearchContext && (form.tcle = ($event.target as HTMLInputElement).checked)"
                        :disabled="!!isResearchContext || form.processing"
                        class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-900 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        :tabindex="8" />
                    <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">
                        Eu li e concordo com o
                        <a class="underline text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                            href="/tcle" target="_blank">
                            Termo de Consentimento Livre e Esclarecido (TCLE)
                        </a>
                        <span v-if="isResearchContext" class="text-amber-600 font-medium">
                            (já aceito na pesquisa)
                        </span>
                    </span>
                </label>

                <InputError class="mt-2" :message="form.errors.terms" />
                <InputError class="mt-2" :message="form.errors.tcle" />
            </div>

            <!-- Botão de envio -->
            <div class="flex justify-center mt-6">
                <Button type="submit" class="w-full" :disabled="!isFormValid || form.processing">
                    <LoaderCircle v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                    Registrar
                </Button>
            </div>

            <div class="flex justify-center gap-1 pt-6 text-sm text-gray-600 dark:text-gray-400">
                <span>Já tem uma conta?</span>
                <TextLink :href="route('login')" :tabindex="9">Entre aqui</TextLink>
            </div>
        </form>
    </AuthLayout>
</template>