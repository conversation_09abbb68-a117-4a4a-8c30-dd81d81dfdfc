<script setup lang="ts">
import { Head, useForm, usePage } from '@inertiajs/vue3';

import DeleteUser from '@/components/DeleteUser.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type BreadcrumbItem, type SharedData, type User } from '@/types';
import { ref } from 'vue';

interface Props {
    status?: string;
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Configurações de perfil',
        href: '/settings/profile',
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User & {
    roles?: {id: number; name: string}[];
    crp?: string | null;
    institution?: string | null;
};

// Determinar o tipo de usuário com base nos papéis
const getCurrentUserType = (): string => {
    if (user.roles && user.roles.some((role: {name: string}) => role.name === 'psychologist')) {
        return 'psychologist';
    } else if (user.roles && user.roles.some((role: {name: string}) => role.name === 'student')) {
        return 'student';
    } else {
        return 'patient';
    }
};

// Criar uma variável reativa para o tipo de usuário
const userType = ref(getCurrentUserType());

const form = useForm({
    name: user.name,
    email: user.email,
    user_type: getCurrentUserType(),
    crp: user.crp || '',
    institution: user.institution || '',
});

// Atualizar o tipo de usuário no formulário quando o usuário alternar
const updateUserType = (type: string) => {
    userType.value = type;
    form.user_type = type;
};

const submit = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
    });
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Configurações de perfil" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall title="Informações de perfil" description="Atualize seus dados pessoais" />

                <form @submit.prevent="submit" class="space-y-6">
                    <div class="grid gap-2">
                        <Label for="name">Nome</Label>
                        <Input id="name" class="mt-1 block w-full" v-model="form.name" required autocomplete="name" placeholder="Nome completo" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="grid gap-2">
                        <Label for="email">Email</Label>
                        <Input
                            id="email"
                            type="email"
                            class="mt-1 block w-full"
                            v-model="form.email"
                            required
                            autocomplete="username"
                            placeholder="Email"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>
                    
                    <!-- Seletor de tipo de usuário -->
                    <div class="grid gap-2">
                        <div class="mb-2">Tipo de Usuário</div>
                        <RadioGroup v-model="userType" class="flex flex-col space-y-2" @update:modelValue="updateUserType">
                            <div class="flex items-center space-x-2">
                                <RadioGroupItem value="psychologist" id="user_type_psychologist" />
                                <Label for="user_type_psychologist" class="cursor-pointer">Sou Psicólogo</Label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <RadioGroupItem value="student" id="user_type_student" />
                                <Label for="user_type_student" class="cursor-pointer">Sou Estudante</Label>
                            </div>
                        </RadioGroup>
                        <InputError class="mt-2" :message="form.errors.user_type" />
                    </div>
                    
                    <!-- Campos específicos por tipo de usuário -->
                    <div v-if="userType === 'psychologist'" class="grid gap-2">
                        <Label for="crp">CRP (Registro no Conselho Regional de Psicologia)</Label>
                        <Input id="crp" class="mt-1 block w-full" v-model="form.crp" placeholder="Ex: 123456-7" />
                        <InputError class="mt-2" :message="form.errors.crp" />
                    </div>
                    
                    <div v-if="userType === 'student'" class="grid gap-2">
                        <Label for="institution">Instituição de Ensino</Label>
                        <Input id="institution" class="mt-1 block w-full" v-model="form.institution" placeholder="Nome da instituição" />
                        <InputError class="mt-2" :message="form.errors.institution" />
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing">Salvar</Button>

                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">Salvo.</p>
                        </Transition>
                    </div>
                </form>
            </div>

            <DeleteUser />
        </SettingsLayout>
    </AppLayout>
</template>
