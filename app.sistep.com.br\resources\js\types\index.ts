export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at?: string | null;
    created_at: string;
    updated_at: string;
    crp: string | null;
    institution: string | null;
    birthdate?: string | null;
    gender?: string | null;
    phone?: string | null;
    active: boolean;
    is_anonymous?: boolean;
    anonymous_id?: string | null;
    roles?: Role[];
    permissions?: Permission[];
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface SharedData {
    auth: {
        user: User;
    };
}

export interface Role {
    id: number;
    name: string;
    guard_name: string;
    created_at: string;
    updated_at: string;
    pivot: {
        model_id: number;
        role_id: number;
        model_type: string;
    };
}

export interface Permission {
    id: number;
    name: string;
    guard_name: string;
    created_at: string;
    updated_at: string;
    pivot: {
        model_id: number;
        permission_id: number;
        model_type: string;
    };
} 