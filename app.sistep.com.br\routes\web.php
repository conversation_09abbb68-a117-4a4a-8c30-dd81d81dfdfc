<?php

use App\Http\Controllers\AnonymousPatientController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Middleware\AdminMiddleware;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth'])->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth'])->name('dashboard');

// Rotas para pacientes anônimos (sem autenticação)
Route::prefix('anonymous')->name('anonymous.')->group(function () {
    Route::post('/register', [AnonymousPatientController::class, 'createAnonymousUser'])->name('register');
    Route::post('/test-result', [AnonymousPatientController::class, 'saveTestResult'])->name('test-result.store');
    Route::get('/results/{anonymousId}', [AnonymousPatientController::class, 'viewResults'])->name('results');
});

// Research Context Routes (TO BE IMPLEMENTED)
// These routes handle the research participant flow from the pesquisa.vue page
Route::prefix('research')->name('research.')->group(function () {
    // Route to display DASS-21 results in professional context
    // Should accept participant_id and research_return_url as query parameters
    // Example: /research/participant/analysis?participant_id=pid_123&research_return_url=...
    
    // TO IMPLEMENT: Route::get('/participant/analysis', [ResearchController::class, 'showParticipantAnalysis'])->name('participant.analysis');
    
    // API route to save research participant test results
    // Called from DASS-21 apply page when in research context
    // TO IMPLEMENT: Route::post('/test-results', [ResearchController::class, 'saveTestResults'])->name('test-results.store');
    
    // Route to get research participant test results
    // TO IMPLEMENT: Route::get('/participant/{participantId}/results', [ResearchController::class, 'getParticipantResults'])->name('participant.results');
});

// Rotas administrativas
Route::middleware(['auth', AdminMiddleware::class])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    
    // Gerenciamento de usuários
    Route::resource('users', UserController::class);
    Route::patch('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
