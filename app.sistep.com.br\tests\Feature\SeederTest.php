<?php

namespace Tests\Feature;

use App\Models\TestResult;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SeederTest extends TestCase
{
    use RefreshDatabase;

    public function test_roles_and_permissions_seeder(): void
    {
        // Testa o seeder de permissões e papéis
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        // Verifica se temos os 4 papéis esperados
        $this->assertDatabaseCount('roles', 4);
        $this->assertDatabaseHas('roles', ['name' => 'admin']);
        $this->assertDatabaseHas('roles', ['name' => 'psychologist']);
        $this->assertDatabaseHas('roles', ['name' => 'student']);
        $this->assertDatabaseHas('roles', ['name' => 'patient']);
        
        // Verifica se temos as permissões esperadas
        $this->assertDatabaseHas('permissions', ['name' => 'users_manage']);
        $this->assertDatabaseHas('permissions', ['name' => 'roles_manage']);
        $this->assertDatabaseHas('permissions', ['name' => 'tests_manage']);
        $this->assertDatabaseHas('permissions', ['name' => 'results_view']);
        $this->assertDatabaseHas('permissions', ['name' => 'dashboard_access']);
        
        // Verifica se temos os admins fixos
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_user_seeder(): void
    {
        // Testa o seeder de usuários
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        $this->seed(\Database\Seeders\UserSeeder::class);
        
        // Verifica se temos usuários com os papéis corretos
        $this->assertEquals(5, User::role('psychologist')->count());
        $this->assertEquals(20, User::role('patient')->count());
        
        // Além dos 2 admins fixos, devemos ter mais 2
        $this->assertEquals(4, User::role('admin')->count());
        
        // Verifica se os psicólogos têm CRP
        $psychologists = User::role('psychologist')->get();
        foreach ($psychologists as $psychologist) {
            $this->assertNotNull($psychologist->crp);
        }
        
        // Verifica se os pacientes têm dados de paciente
        $patients = User::role('patient')->get();
        foreach ($patients as $patient) {
            $this->assertNotNull($patient->birthdate);
            $this->assertNotNull($patient->gender);
        }
    }

    public function test_test_result_seeder(): void
    {
        // Testa o seeder de resultados de testes
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        $this->seed(\Database\Seeders\UserSeeder::class);
        $this->seed(\Database\Seeders\TestResultSeeder::class);
        
        // Verifica se temos resultados de testes
        $this->assertTrue(TestResult::count() > 0);
        
        // Verifica se temos resultados de testes DASS21
        $this->assertTrue(TestResult::where('test_type', 'DASS21')->count() > 0);
        
        // Verifica se alguns pacientes têm testes SUS
        $this->assertTrue(TestResult::where('test_type', 'SUS')->count() > 0);
        
        // Verifica se todos os resultados pertencem a pacientes
        $patientIds = User::role('patient')->pluck('id')->toArray();
        foreach (TestResult::all() as $result) {
            $this->assertTrue(in_array($result->user_id, $patientIds));
        }
    }

    public function test_all_seeders_together(): void
    {
        // Testa todos os seeders juntos (como seria em produção)
        $this->seed();
        
        // Verifica se temos os papéis e permissões
        $this->assertDatabaseCount('roles', 4);
        $this->assertDatabaseHas('roles', ['name' => 'admin']);
        
        // Verifica se temos os usuários
        $this->assertTrue(User::role('admin')->count() >= 3);
        $this->assertTrue(User::role('psychologist')->count() >= 5);
        $this->assertTrue(User::role('patient')->count() >= 20);
        
        // Verifica se temos resultados de testes
        $this->assertTrue(TestResult::count() > 0);
    }
} 