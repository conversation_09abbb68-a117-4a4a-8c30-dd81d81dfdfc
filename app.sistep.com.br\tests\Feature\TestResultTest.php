<?php

namespace Tests\Feature;

use App\Models\TestResult;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TestResultTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_dass21_result(): void
    {
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $patient = User::factory()->patient()->create();
        
        $testResult = TestResult::factory()
            ->dass21()
            ->create([
                'user_id' => $patient->id,
            ]);
        
        $this->assertEquals('DASS21', $testResult->test_type);
        $this->assertIsArray($testResult->score);
        $this->assertArrayHasKey('depression', $testResult->score);
        $this->assertArrayHasKey('anxiety', $testResult->score);
        $this->assertArrayHasKey('stress', $testResult->score);
        $this->assertArrayHasKey('total', $testResult->score);
        
        // Verifica se os dados do resultado contêm as informações esperadas
        $this->assertIsArray($testResult->result_data);
        $this->assertArrayHasKey('questions', $testResult->result_data);
        $this->assertArrayHasKey('depression_level', $testResult->result_data);
        $this->assertArrayHasKey('anxiety_level', $testResult->result_data);
        $this->assertArrayHasKey('stress_level', $testResult->result_data);
        
        // Verifica relacionamento com usuário
        $this->assertEquals($patient->id, $testResult->user->id);
    }

    public function test_can_create_sus_result(): void
    {
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $patient = User::factory()->patient()->create();
        
        $testResult = TestResult::factory()
            ->sus()
            ->create([
                'user_id' => $patient->id,
            ]);
        
        $this->assertEquals('SUS', $testResult->test_type);
        $this->assertIsArray($testResult->score);
        $this->assertArrayHasKey('sus_score', $testResult->score);
        
        // Verifica se a pontuação SUS está no intervalo correto (0-100)
        $this->assertGreaterThanOrEqual(0, $testResult->score['sus_score']);
        $this->assertLessThanOrEqual(100, $testResult->score['sus_score']);
        
        // Verifica se os dados do resultado contêm as informações esperadas
        $this->assertIsArray($testResult->result_data);
        $this->assertArrayHasKey('questions', $testResult->result_data);
        $this->assertArrayHasKey('usability_level', $testResult->result_data);
        
        // Verifica se temos 10 respostas (o SUS tem 10 perguntas)
        $this->assertCount(10, $testResult->result_data['questions']);
    }

    public function test_user_can_have_multiple_test_results(): void
    {
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $patient = User::factory()->patient()->create();
        
        // Criar 3 resultados para o mesmo paciente
        TestResult::factory()->dass21()->create(['user_id' => $patient->id]);
        TestResult::factory()->dass21()->create(['user_id' => $patient->id]);
        TestResult::factory()->sus()->create(['user_id' => $patient->id]);
        
        $this->assertDatabaseCount('test_results', 3);
        $this->assertCount(3, $patient->refresh()->testResults);
        
        // Verificar se temos 2 resultados DASS21 e 1 SUS
        $this->assertEquals(2, $patient->testResults()->where('test_type', 'DASS21')->count());
        $this->assertEquals(1, $patient->testResults()->where('test_type', 'SUS')->count());
    }
} 