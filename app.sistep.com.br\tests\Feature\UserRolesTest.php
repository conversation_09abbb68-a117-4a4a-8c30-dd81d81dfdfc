<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserRolesTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_admin_user(): void
    {
        // Teste se podemos criar um usuário administrador
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $user = User::factory()->admin()->create();
        
        $this->assertTrue($user->hasRole('admin'));
        $this->assertTrue($user->can('users_manage'));
        $this->assertTrue($user->can('roles_manage'));
        $this->assertTrue($user->can('tests_manage'));
    }

    public function test_can_create_psychologist_user(): void
    {
        // Teste se podemos criar um usuário psicólogo
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $user = User::factory()->psychologist()->create();
        
        $this->assertTrue($user->hasRole('psychologist'));
        $this->assertTrue($user->can('tests_manage'));
        $this->assertTrue($user->can('results_view'));
        $this->assertFalse($user->can('users_manage'));
        
        // Verifica se o CRP foi gerado
        $this->assertNotNull($user->crp);
        $this->assertStringContainsString('-', $user->crp);
    }

    public function test_can_create_patient_user(): void
    {
        // Teste se podemos criar um usuário paciente
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $user = User::factory()->patient()->create();
        
        $this->assertTrue($user->hasRole('patient'));
        $this->assertFalse($user->can('tests_manage'));
        $this->assertFalse($user->can('results_view'));
        
        // Verifica se dados de paciente foram gerados
        $this->assertNotNull($user->birthdate);
        $this->assertNotNull($user->gender);
        $this->assertNotNull($user->phone);
    }

    public function test_admin_users_exist(): void
    {
        // Teste se os usuários administradores fixos foram criados
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
        
        // Verifica se todos têm papel de admin
        $admins = User::whereIn('email', [
            '<EMAIL>',
            '<EMAIL>'
        ])->get();
        
        foreach ($admins as $admin) {
            $this->assertTrue($admin->hasRole('admin'));
        }
    }
} 