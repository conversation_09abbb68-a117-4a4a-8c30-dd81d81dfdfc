# Plano de Implementação do TCLE

## Contexto
O TCLE (Termo de Consentimento Livre e Esclarecido) é um requisito legal e ético que deve ser apresentado e aceito antes de qualquer coleta de dados do usuário. Este documento descreve o plano de implementação do TCLE no SISTEP.

## Estrutura do Banco de Dados

### Nova Migration: `create_tcle_table`
```php
Schema::create('tcle_acceptances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->string('ip_address', 45)->nullable();
    $table->text('user_agent')->nullable();
    $table->timestamp('accepted_at');
    $table->timestamps();
    
    // Índices
    $table->index('user_id');
});
```

## Modelos e Relacionamentos

1. Novo modelo `TcleAcceptance`:
   - Relacionamento com `User`
   - Métodos para verificar aceitação

2. Atualização do modelo `User`:
   - Relacionamento com `TcleAcceptance`
   - Métodos para verificar status do TCLE

## Middleware

1. Criar `EnsureTcleAccepted`:
   - Verifica se o usuário aceitou o TCLE
   - Redireciona para tela de TCLE se necessário
   - Aplicar em rotas que exigem TCLE

## Backend (Laravel)

1. Controller:
   ```
   app/Http/Controllers/
   └── Tcle/
       └── TcleController.php
   ```

2. Request:
   ```
   app/Http/Requests/
   └── Tcle/
       └── StoreTcleAcceptanceRequest.php
   ```

3. Resource:
   ```
   app/Http/Resources/
   └── Tcle/
       └── TcleAcceptanceResource.php
   ```

4. Rotas Web:
   ```php
   Route::middleware(['auth'])->group(function () {
       Route::get('/tcle', [TcleController::class, 'show'])->name('tcle.show');
       Route::post('/tcle/accept', [TcleController::class, 'accept'])->name('tcle.accept');
   });
   ```

## Frontend (Vue + Inertia)

1. Componentes:
   ```
   resources/js/Components/
   └── Tcle/
       ├── TcleModal.vue
       └── TcleAcceptanceForm.vue
   ```

2. Página:
   ```
   resources/js/Pages/
   └── Tcle/
       └── Show.vue
   ```

3. Layout:
   ```
   resources/js/Layouts/
   └── TcleLayout.vue
   ```

4. Tipos TypeScript:
   ```
   resources/js/types/
   └── tcle.d.ts
   ```

## Fluxo de Implementação

1. **Fase 1 - Backend**
   - Criar migration e modelo
   - Implementar controller e rotas
   - Configurar middleware
   - Adicionar testes unitários e de integração

2. **Fase 2 - Frontend**
   - Desenvolver componentes Vue
   - Implementar página Inertia
   - Integrar com controller
   - Adicionar testes de componentes

3. **Fase 3 - Integração**
   - Integrar TCLE no fluxo de registro
   - Adicionar verificação antes dos testes
   - Implementar logs
   - Realizar testes de aceitação

## Requisitos de Segurança

1. **Validação e Sanitização**
   - Sanitizar inputs do usuário
   - Prevenir XSS e CSRF

2. **Auditoria**
   - Registrar IP e User Agent
   - Logs de aceitação

3. **LGPD**
   - Garantir conformidade com LGPD
   - Documentar processamento de dados

## Testes

1. **Unitários**
   - Modelo e relacionamentos
   - Controller e request
   - Middleware

2. **Integração**
   - Fluxo de aceitação do TCLE
   - Rotas e redirecionamentos

3. **Frontend**
   - Testes de componentes Vue
   - Testes de integração Inertia

## Monitoramento

1. **Métricas**
   - Taxa de aceitação
   - Tempo médio de leitura

2. **Logs**
   - Aceitações
   - Erros e exceções

## Documentação

1. **Técnica**
   - Rotas e controller
   - Modelo e relacionamentos
   - Fluxo de dados

2. **Usuário**
   - Guia de implementação
   - Fluxo de aceitação
   - FAQ

## Considerações Finais

1. **Performance**
   - Otimização de queries
   - Cache de status de aceitação

2. **Manutenção**
   - Backup de dados
   - Plano de rollback

3. **Escalabilidade**
   - Design modular
   - Monitoramento de carga 