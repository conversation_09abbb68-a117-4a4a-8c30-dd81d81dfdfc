---
description: 
globs: 
alwaysApply: false
---
# Correção do Bug do Accordion

## Problema Identificado
O componente Accordion não estava abrindo e fechando corretamente. O elemento AccordionContent estava sendo removido e recriado do DOM, impossibilitando qualquer transição suave de altura.

## Causa Raiz
1. **Remoção/Recriação do DOM**: O AccordionContent era completamente removido do DOM ao fechar
2. **Falta de controle de transição**: Não havia mecanismo para animar a altura durante open/close
3. **Baseado no problema comum relatado no GitHub**: [Bootstrap Vue 3 accordion issue](mdc:https:/github.com/orgs/twbs/discussions/36611)

## Solução Implementada

### 1. Vue Transition com JavaScript Hooks
Implementamos uma solução customizada usando o [componente `<Transition>` do Vue](mdc:https:/vuejs.org/guide/built-ins/transition) com JavaScript hooks para controlar transições de altura:

```javascript
// Hooks principais para controle de altura
const beforeEnter = (el) => {
  el.style.height = '0px';
  el.style.opacity = '0';
  el.style.overflow = 'hidden';
};

const enter = async (el, done) => {
  // Mede altura natural do conteúdo
  el.style.height = 'auto';
  const targetHeight = el.scrollHeight;
  
  // Anima de 0 para altura total
  el.style.height = '0px';
  requestAnimationFrame(() => {
    el.style.transition = 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out';
    el.style.height = `${targetHeight}px`;
    el.style.opacity = '1';
  });
};
```

### 2. MutationObserver para Estado do Accordion
Utilizamos MutationObserver para detectar mudanças no atributo `data-state` do reka-ui:

```javascript
const checkAccordionState = () => {
  const dataState = accordionContentRef.value.getAttribute('data-state');
  const newIsOpen = dataState === 'open';
  
  if (newIsOpen !== isOpen.value) {
    isOpen.value = newIsOpen;
  }
};

// Observer para mudanças no data-state
observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'attributes' && mutation.attributeName === 'data-state') {
      checkAccordionState();
    }
  });
});
```

### 3. Force Mount para Evitar Recriação
```html
<AccordionContent ref="accordionContentRef" :force-mount="true">
  <Transition :css="false" @before-enter="beforeEnter" @enter="enter">
    <div v-show="isOpen">
      <slot />
    </div>
  </Transition>
</AccordionContent>
```

## Arquivos Alterados
- `components/ui/accordion/AccordionContent.vue` - Implementação completa com Vue Transition
- `components/ResearchStepper.vue` - Ajustes estruturais e limpeza CSS
- `.cursor/rules/accordion-fix-bug.mdc` - Documentação da correção

## Resultado
✅ Accordion abre e fecha com transição suave de altura
✅ Elemento permanece no DOM (não é recriado)
✅ Animação controlada por JavaScript hooks
✅ Performance otimizada com `cubic-bezier(0.4, 0, 0.2, 1)`
✅ Suporte a `prefers-reduced-motion` para acessibilidade
✅ Chevron anima corretamente
✅ Compatível com qualquer versão do reka-ui

## Características Técnicas
- **Duração**: 300ms para enter/leave
- **Easing**: `cubic-bezier(0.4, 0, 0.2, 1)` (Material Design)
- **Propriedades animadas**: `height` e `opacity`
- **Fallback**: CSS `transition: none` para `prefers-reduced-motion`
- **Detecção de estado**: MutationObserver no atributo `data-state`

## Desafios Resolvidos
1. **Context não disponível**: `useAccordionContext` não existe no reka-ui
   - **Solução**: MutationObserver para detectar mudanças de estado
2. **Remoção do DOM**: Elemento era recriado a cada toggle
   - **Solução**: `force-mount="true"` + `v-show` em vez de `v-if`
3. **Timing de animação**: Medir altura antes de animar
   - **Solução**: `scrollHeight` + `requestAnimationFrame`
4. **Acesso ao DOM element**: `accordionContentRef.value.getAttribute is not a function`
   - **Solução**: Múltiplas estratégias para acessar o elemento DOM real
5. **Transição entre etapas**: Accordion ficava na mesma etapa após conclusão
   - **Solução**: Coordenação entre ResearchStepper e handlers de ação

### Correção da Lógica de Transição Entre Etapas

**Problema**: Após concluir uma etapa, o accordion permanecia na mesma etapa em vez de avançar para a próxima.

**Causa**: Duplicação de lógica de transição entre `ResearchStepper.vue` e `pesquisa.vue`, causando conflitos.

**Problema Adicional**: Etapas eram marcadas como concluídas antes de serem realmente executadas.

**Solução Implementada**:

1. **ResearchStepper só faz transição para ações internas**:
```javascript
const handleActionClick = async (step: Step, action: Action) => {
    // Only transition for TCLE acceptance (internal action)
    if (action.id === 'accept-tcle') {
        // TCLE is internal, so we can transition to next step immediately
        await transitionToStep(step.step + 1);
    }
    
    // For external actions, don't transition - they will return and trigger transition
    // The store's handleReturnFromExternal will update currentStep when they return
}
```

2. **Store marca etapas como concluídas apenas no retorno**:
```javascript
handleReturnFromExternal(queryParticipantId, stepCompleted) {
    if (stepCompleted && this.participantId) {
        if (stepCompleted === 'dass') {
            this.setCurrentStep(3); // Move to Panel step
            this.addActivity(2, 'Você concluiu o teste DASS-21');
        } else if (stepCompleted === 'panel') {
            this.setCurrentStep(4); // Move to Forms step
            this.addActivity(3, 'Você concluiu a análise no painel');
        }
    }
}
```

3. **Transição visual no retorno de aplicações externas**:
```javascript
onMounted(() => {
    // Handle return from external sites
    if (queryParticipantId || stepCompleted) {
        const previousStep = currentStep.value;
        researchStore.handleReturnFromExternal(queryParticipantId, stepCompleted)
        
        // If step changed due to external completion, trigger visual transition
        if (currentStep.value > previousStep && researchStepperRef.value) {
            nextTick(() => {
                researchStepperRef.value.transitionToStep(currentStep.value);
            });
        }
    }
});
```

4. **Navegação não avança etapas prematuramente**:
```javascript
const handleStep2_NavigateToDASS21 = () => {
    // NÃO atualizar para próxima etapa ainda - só após retornar
    researchStore.addActivity(2, 'Você iniciou o teste DASS-21');
    researchStore.addActivity(2, 'Redirecionando para aplicação do teste...');
    
    window.location.href = dass21Link.value;
};
```

**Fluxo Correto Agora**:
1. **Etapa 1 (TCLE)**: Transição imediata para Etapa 2 (ação interna)
2. **Etapa 2 (DASS-21)**: Navega para aplicação externa, só marca como concluída no retorno
3. **Etapa 3 (Painel)**: Navega para aplicação externa, só marca como concluída no retorno
4. **Etapa 4 (Forms)**: Abre em nova aba, marca como concluída imediatamente

### Estratégia de Acesso ao DOM Element
```javascript
// Múltiplas tentativas para encontrar o elemento DOM correto
let element = null;

if (accordionContentRef.value?.$el) {
  element = accordionContentRef.value.$el;  // Componente Vue
} else if (accordionContentRef.value?.querySelector) {
  element = accordionContentRef.value;      // Elemento DOM direto
} else if (accordionContentRef.value) {
  element = accordionContentRef.value;      // Fallback
}

if (element && typeof element.getAttribute === 'function') {
  // Proceder com MutationObserver
}
```

## Referências
- [Vue Transition Documentation](mdc:https:/vuejs.org/guide/built-ins/transition)
- [Bootstrap Vue 3 Accordion Issue](mdc:https:/github.com/orgs/twbs/discussions/36611)
- [MDN MutationObserver](mdc:https:/developer.mozilla.org/en-US/docs/Web/API/MutationObserver)

## Lições Aprendidas
1. **Vue Transition é poderoso**: JavaScript hooks oferecem controle total sobre animações
2. **Force mount é essencial**: Evita recriação desnecessária do DOM
3. **MutationObserver é confiável**: Detecta mudanças no DOM de forma eficiente
4. **Height: auto timing**: Medir altura natural antes de animar é crucial
5. **Fallback sempre**: Nem todas as bibliotecas exportam todas as funções esperadas
6. **Variáveis de ambiente no Nuxt**: Usar `runtimeConfig` para variáveis acessíveis no cliente

### Uso Correto de Variáveis de Ambiente no Nuxt 3

**Problema**: `import.meta.env.NUXT_PUBLIC_APP_URL` resultando em `undefined` no cliente.

**Causa**: Variáveis públicas no Nuxt 3 devem ser expostas via `runtimeConfig`.

**Solução**: 

1. **Configurar no `nuxt.config.ts`**:
```javascript
export default defineNuxtConfig({
  runtimeConfig: {
    public: {
      APP_URL: process.env.NUXT_PUBLIC_APP_URL || '' // Adicionar aqui
    }
  }
})
```

2. **Acessar no componente/página via `useRuntimeConfig()`**:
```javascript
<script setup>
const runtimeConfig = useRuntimeConfig();

const DASS21_BASE_URL = import.meta.env.DEV 
  ? runtimeConfig.public.APP_URL + '/DASS-21/apply' 
  : 'https://sistep.com.br/DASS-21/apply';
</script>
```

**Referências**:
- [Nuxt Runtime Config Documentation](https://nuxt.com/docs/api/configuration/nuxt-config#runtimeconfig)
- [Nuxt import.meta Documentation](https://nuxt.com/docs/api/advanced/import-meta)
- [GitHub Issue sobre import.meta](https://github.com/nuxt/nuxt/issues/24185)

## Próximos Passos
- Monitorar performance em dispositivos móveis
- Considerar implementar lazy loading para conteúdo pesado
- Avaliar aplicar padrão similar em outros componentes UI
- Testar com diferentes versões do reka-ui

