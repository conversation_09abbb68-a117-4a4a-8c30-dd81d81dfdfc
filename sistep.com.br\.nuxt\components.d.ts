
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AccessibilityPanel': typeof import("../components/AccessibilityPanel.vue")['default']
    'AppFooter': typeof import("../components/AppFooter.vue")['default']
    'AppHeader': typeof import("../components/AppHeader.vue")['default']
    'DarkModeToggle': typeof import("../components/DarkModeToggle.vue")['default']
    'QRCodeScanner': typeof import("../components/QRCodeScanner.vue")['default']
    'ResearchStepper': typeof import("../components/ResearchStepper.vue")['default']
    'ResultCard': typeof import("../components/ResultCard.vue")['default']
    'TestProgressIndicator': typeof import("../components/TestProgressIndicator.vue")['default']
    'IconsAkarIconsGithubOutlineFill': typeof import("../components/icons/AkarIconsGithubOutlineFill.vue")['default']
    'IconsArcticonsOpenaiChatgpt': typeof import("../components/icons/ArcticonsOpenaiChatgpt.vue")['default']
    'IconsArcticonsVisualStudioCode': typeof import("../components/icons/ArcticonsVisualStudioCode.vue")['default']
    'IconsFeGithub': typeof import("../components/icons/FeGithub.vue")['default']
    'IconsPhChat': typeof import("../components/icons/PhChat.vue")['default']
    'IconsPhDiscordLogoLight': typeof import("../components/icons/PhDiscordLogoLight.vue")['default']
    'IconsPhGoogleChromeLogo': typeof import("../components/icons/PhGoogleChromeLogo.vue")['default']
    'IconsPhTerminalWindowBold': typeof import("../components/icons/PhTerminalWindowBold.vue")['default']
    'IconsPhTerminalWindowLight': typeof import("../components/icons/PhTerminalWindowLight.vue")['default']
    'IconsPhWindowsLogoBold': typeof import("../components/icons/PhWindowsLogoBold.vue")['default']
    'IconsPhWindowsLogoLight': typeof import("../components/icons/PhWindowsLogoLight.vue")['default']
    'IconsTablerBrandDiscord': typeof import("../components/icons/TablerBrandDiscord.vue")['default']
    'IconsTablerBrandVscode': typeof import("../components/icons/TablerBrandVscode.vue")['default']
    'IconsUitGoogle': typeof import("../components/icons/UitGoogle.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'Accordion': typeof import("../components/ui/accordion/index")['Accordion']
    'AccordionContent': typeof import("../components/ui/accordion/index")['AccordionContent']
    'AccordionItem': typeof import("../components/ui/accordion/index")['AccordionItem']
    'AccordionTrigger': typeof import("../components/ui/accordion/index")['AccordionTrigger']
    'Alert': typeof import("../components/ui/alert/index")['Alert']
    'AlertTitle': typeof import("../components/ui/alert/index")['AlertTitle']
    'AlertDescription': typeof import("../components/ui/alert/index")['AlertDescription']
    'Badge': typeof import("../components/ui/badge/index")['Badge']
    'Button': typeof import("../components/ui/button/index")['Button']
    'Card': typeof import("../components/ui/card/index")['Card']
    'CardHeader': typeof import("../components/ui/card/index")['CardHeader']
    'CardTitle': typeof import("../components/ui/card/index")['CardTitle']
    'CardDescription': typeof import("../components/ui/card/index")['CardDescription']
    'CardContent': typeof import("../components/ui/card/index")['CardContent']
    'CardFooter': typeof import("../components/ui/card/index")['CardFooter']
    'Dialog': typeof import("../components/ui/dialog/index")['Dialog']
    'DialogClose': typeof import("../components/ui/dialog/index")['DialogClose']
    'DialogTrigger': typeof import("../components/ui/dialog/index")['DialogTrigger']
    'DialogHeader': typeof import("../components/ui/dialog/index")['DialogHeader']
    'DialogTitle': typeof import("../components/ui/dialog/index")['DialogTitle']
    'DialogDescription': typeof import("../components/ui/dialog/index")['DialogDescription']
    'DialogContent': typeof import("../components/ui/dialog/index")['DialogContent']
    'DialogScrollContent': typeof import("../components/ui/dialog/index")['DialogScrollContent']
    'DialogFooter': typeof import("../components/ui/dialog/index")['DialogFooter']
    'DropdownMenuPortal': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuPortal']
    'DropdownMenu': typeof import("../components/ui/dropdown-menu/index")['DropdownMenu']
    'DropdownMenuTrigger': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuTrigger']
    'DropdownMenuContent': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuContent']
    'DropdownMenuGroup': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuGroup']
    'DropdownMenuRadioGroup': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioGroup']
    'DropdownMenuItem': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuItem']
    'DropdownMenuCheckboxItem': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuCheckboxItem']
    'DropdownMenuRadioItem': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioItem']
    'DropdownMenuShortcut': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuShortcut']
    'DropdownMenuSeparator': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSeparator']
    'DropdownMenuLabel': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuLabel']
    'DropdownMenuSub': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSub']
    'DropdownMenuSubTrigger': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubTrigger']
    'DropdownMenuSubContent': typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubContent']
    'Form': typeof import("../components/ui/form/index")['Form']
    'FormField': typeof import("../components/ui/form/index")['FormField']
    'FormItem': typeof import("../components/ui/form/index")['FormItem']
    'FormLabel': typeof import("../components/ui/form/index")['FormLabel']
    'FormControl': typeof import("../components/ui/form/index")['FormControl']
    'FormMessage': typeof import("../components/ui/form/index")['FormMessage']
    'FormDescription': typeof import("../components/ui/form/index")['FormDescription']
    'Icon': typeof import("../components/ui/icon/index")['Icon']
    'Input': typeof import("../components/ui/input/index")['Input']
    'Label': typeof import("../components/ui/label/index")['Label']
    'Progress': typeof import("../components/ui/progress/index")['Progress']
    'RadioGroup': typeof import("../components/ui/radio-group/index")['RadioGroup']
    'RadioGroupItem': typeof import("../components/ui/radio-group/index")['RadioGroupItem']
    'Sheet': typeof import("../components/ui/sheet/index")['Sheet']
    'SheetTrigger': typeof import("../components/ui/sheet/index")['SheetTrigger']
    'SheetClose': typeof import("../components/ui/sheet/index")['SheetClose']
    'SheetContent': typeof import("../components/ui/sheet/index")['SheetContent']
    'SheetHeader': typeof import("../components/ui/sheet/index")['SheetHeader']
    'SheetTitle': typeof import("../components/ui/sheet/index")['SheetTitle']
    'SheetDescription': typeof import("../components/ui/sheet/index")['SheetDescription']
    'SheetFooter': typeof import("../components/ui/sheet/index")['SheetFooter']
    'Skeleton': typeof import("../components/ui/skeleton/index")['Skeleton']
    'Stepper': typeof import("../components/ui/stepper/index")['Stepper']
    'StepperDescription': typeof import("../components/ui/stepper/index")['StepperDescription']
    'StepperIndicator': typeof import("../components/ui/stepper/index")['StepperIndicator']
    'StepperItem': typeof import("../components/ui/stepper/index")['StepperItem']
    'StepperSeparator': typeof import("../components/ui/stepper/index")['StepperSeparator']
    'StepperTitle': typeof import("../components/ui/stepper/index")['StepperTitle']
    'StepperTrigger': typeof import("../components/ui/stepper/index")['StepperTrigger']
    'Switch': typeof import("../components/ui/switch/index")['Switch']
    'Toaster': typeof import("../components/ui/toast/index")['Toaster']
    'Toast': typeof import("../components/ui/toast/index")['Toast']
    'ToastViewport': typeof import("../components/ui/toast/index")['ToastViewport']
    'ToastAction': typeof import("../components/ui/toast/index")['ToastAction']
    'ToastClose': typeof import("../components/ui/toast/index")['ToastClose']
    'ToastTitle': typeof import("../components/ui/toast/index")['ToastTitle']
    'ToastDescription': typeof import("../components/ui/toast/index")['ToastDescription']
    'ToastProvider': typeof import("../components/ui/toast/index")['ToastProvider']
    'ToggleGroup': typeof import("../components/ui/toggle-group/index")['ToggleGroup']
    'ToggleGroupItem': typeof import("../components/ui/toggle-group/index")['ToggleGroupItem']
    'Tooltip': typeof import("../components/ui/tooltip/index")['Tooltip']
    'TooltipContent': typeof import("../components/ui/tooltip/index")['TooltipContent']
    'TooltipTrigger': typeof import("../components/ui/tooltip/index")['TooltipTrigger']
    'TooltipProvider': typeof import("../components/ui/tooltip/index")['TooltipProvider']
    'VitePwaManifest': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']
    'NuxtPwaManifest': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']
    'NuxtPwaAssets': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/NuxtPwaAssets")['default']
    'PwaAppleImage': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleImage.vue")['default']
    'PwaAppleSplashScreenImage': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleSplashScreenImage.vue")['default']
    'PwaFaviconImage': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaFaviconImage.vue")['default']
    'PwaMaskableImage': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaMaskableImage.vue")['default']
    'PwaTransparentImage': typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaTransparentImage.vue")['default']
    'ColorScheme': typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAccessibilityPanel': LazyComponent<typeof import("../components/AccessibilityPanel.vue")['default']>
    'LazyAppFooter': LazyComponent<typeof import("../components/AppFooter.vue")['default']>
    'LazyAppHeader': LazyComponent<typeof import("../components/AppHeader.vue")['default']>
    'LazyDarkModeToggle': LazyComponent<typeof import("../components/DarkModeToggle.vue")['default']>
    'LazyQRCodeScanner': LazyComponent<typeof import("../components/QRCodeScanner.vue")['default']>
    'LazyResearchStepper': LazyComponent<typeof import("../components/ResearchStepper.vue")['default']>
    'LazyResultCard': LazyComponent<typeof import("../components/ResultCard.vue")['default']>
    'LazyTestProgressIndicator': LazyComponent<typeof import("../components/TestProgressIndicator.vue")['default']>
    'LazyIconsAkarIconsGithubOutlineFill': LazyComponent<typeof import("../components/icons/AkarIconsGithubOutlineFill.vue")['default']>
    'LazyIconsArcticonsOpenaiChatgpt': LazyComponent<typeof import("../components/icons/ArcticonsOpenaiChatgpt.vue")['default']>
    'LazyIconsArcticonsVisualStudioCode': LazyComponent<typeof import("../components/icons/ArcticonsVisualStudioCode.vue")['default']>
    'LazyIconsFeGithub': LazyComponent<typeof import("../components/icons/FeGithub.vue")['default']>
    'LazyIconsPhChat': LazyComponent<typeof import("../components/icons/PhChat.vue")['default']>
    'LazyIconsPhDiscordLogoLight': LazyComponent<typeof import("../components/icons/PhDiscordLogoLight.vue")['default']>
    'LazyIconsPhGoogleChromeLogo': LazyComponent<typeof import("../components/icons/PhGoogleChromeLogo.vue")['default']>
    'LazyIconsPhTerminalWindowBold': LazyComponent<typeof import("../components/icons/PhTerminalWindowBold.vue")['default']>
    'LazyIconsPhTerminalWindowLight': LazyComponent<typeof import("../components/icons/PhTerminalWindowLight.vue")['default']>
    'LazyIconsPhWindowsLogoBold': LazyComponent<typeof import("../components/icons/PhWindowsLogoBold.vue")['default']>
    'LazyIconsPhWindowsLogoLight': LazyComponent<typeof import("../components/icons/PhWindowsLogoLight.vue")['default']>
    'LazyIconsTablerBrandDiscord': LazyComponent<typeof import("../components/icons/TablerBrandDiscord.vue")['default']>
    'LazyIconsTablerBrandVscode': LazyComponent<typeof import("../components/icons/TablerBrandVscode.vue")['default']>
    'LazyIconsUitGoogle': LazyComponent<typeof import("../components/icons/UitGoogle.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyAccordion': LazyComponent<typeof import("../components/ui/accordion/index")['Accordion']>
    'LazyAccordionContent': LazyComponent<typeof import("../components/ui/accordion/index")['AccordionContent']>
    'LazyAccordionItem': LazyComponent<typeof import("../components/ui/accordion/index")['AccordionItem']>
    'LazyAccordionTrigger': LazyComponent<typeof import("../components/ui/accordion/index")['AccordionTrigger']>
    'LazyAlert': LazyComponent<typeof import("../components/ui/alert/index")['Alert']>
    'LazyAlertTitle': LazyComponent<typeof import("../components/ui/alert/index")['AlertTitle']>
    'LazyAlertDescription': LazyComponent<typeof import("../components/ui/alert/index")['AlertDescription']>
    'LazyBadge': LazyComponent<typeof import("../components/ui/badge/index")['Badge']>
    'LazyButton': LazyComponent<typeof import("../components/ui/button/index")['Button']>
    'LazyCard': LazyComponent<typeof import("../components/ui/card/index")['Card']>
    'LazyCardHeader': LazyComponent<typeof import("../components/ui/card/index")['CardHeader']>
    'LazyCardTitle': LazyComponent<typeof import("../components/ui/card/index")['CardTitle']>
    'LazyCardDescription': LazyComponent<typeof import("../components/ui/card/index")['CardDescription']>
    'LazyCardContent': LazyComponent<typeof import("../components/ui/card/index")['CardContent']>
    'LazyCardFooter': LazyComponent<typeof import("../components/ui/card/index")['CardFooter']>
    'LazyDialog': LazyComponent<typeof import("../components/ui/dialog/index")['Dialog']>
    'LazyDialogClose': LazyComponent<typeof import("../components/ui/dialog/index")['DialogClose']>
    'LazyDialogTrigger': LazyComponent<typeof import("../components/ui/dialog/index")['DialogTrigger']>
    'LazyDialogHeader': LazyComponent<typeof import("../components/ui/dialog/index")['DialogHeader']>
    'LazyDialogTitle': LazyComponent<typeof import("../components/ui/dialog/index")['DialogTitle']>
    'LazyDialogDescription': LazyComponent<typeof import("../components/ui/dialog/index")['DialogDescription']>
    'LazyDialogContent': LazyComponent<typeof import("../components/ui/dialog/index")['DialogContent']>
    'LazyDialogScrollContent': LazyComponent<typeof import("../components/ui/dialog/index")['DialogScrollContent']>
    'LazyDialogFooter': LazyComponent<typeof import("../components/ui/dialog/index")['DialogFooter']>
    'LazyDropdownMenuPortal': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuPortal']>
    'LazyDropdownMenu': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenu']>
    'LazyDropdownMenuTrigger': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuTrigger']>
    'LazyDropdownMenuContent': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuContent']>
    'LazyDropdownMenuGroup': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuGroup']>
    'LazyDropdownMenuRadioGroup': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioGroup']>
    'LazyDropdownMenuItem': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuItem']>
    'LazyDropdownMenuCheckboxItem': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuCheckboxItem']>
    'LazyDropdownMenuRadioItem': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioItem']>
    'LazyDropdownMenuShortcut': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuShortcut']>
    'LazyDropdownMenuSeparator': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSeparator']>
    'LazyDropdownMenuLabel': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuLabel']>
    'LazyDropdownMenuSub': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSub']>
    'LazyDropdownMenuSubTrigger': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubTrigger']>
    'LazyDropdownMenuSubContent': LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubContent']>
    'LazyForm': LazyComponent<typeof import("../components/ui/form/index")['Form']>
    'LazyFormField': LazyComponent<typeof import("../components/ui/form/index")['FormField']>
    'LazyFormItem': LazyComponent<typeof import("../components/ui/form/index")['FormItem']>
    'LazyFormLabel': LazyComponent<typeof import("../components/ui/form/index")['FormLabel']>
    'LazyFormControl': LazyComponent<typeof import("../components/ui/form/index")['FormControl']>
    'LazyFormMessage': LazyComponent<typeof import("../components/ui/form/index")['FormMessage']>
    'LazyFormDescription': LazyComponent<typeof import("../components/ui/form/index")['FormDescription']>
    'LazyIcon': LazyComponent<typeof import("../components/ui/icon/index")['Icon']>
    'LazyInput': LazyComponent<typeof import("../components/ui/input/index")['Input']>
    'LazyLabel': LazyComponent<typeof import("../components/ui/label/index")['Label']>
    'LazyProgress': LazyComponent<typeof import("../components/ui/progress/index")['Progress']>
    'LazyRadioGroup': LazyComponent<typeof import("../components/ui/radio-group/index")['RadioGroup']>
    'LazyRadioGroupItem': LazyComponent<typeof import("../components/ui/radio-group/index")['RadioGroupItem']>
    'LazySheet': LazyComponent<typeof import("../components/ui/sheet/index")['Sheet']>
    'LazySheetTrigger': LazyComponent<typeof import("../components/ui/sheet/index")['SheetTrigger']>
    'LazySheetClose': LazyComponent<typeof import("../components/ui/sheet/index")['SheetClose']>
    'LazySheetContent': LazyComponent<typeof import("../components/ui/sheet/index")['SheetContent']>
    'LazySheetHeader': LazyComponent<typeof import("../components/ui/sheet/index")['SheetHeader']>
    'LazySheetTitle': LazyComponent<typeof import("../components/ui/sheet/index")['SheetTitle']>
    'LazySheetDescription': LazyComponent<typeof import("../components/ui/sheet/index")['SheetDescription']>
    'LazySheetFooter': LazyComponent<typeof import("../components/ui/sheet/index")['SheetFooter']>
    'LazySkeleton': LazyComponent<typeof import("../components/ui/skeleton/index")['Skeleton']>
    'LazyStepper': LazyComponent<typeof import("../components/ui/stepper/index")['Stepper']>
    'LazyStepperDescription': LazyComponent<typeof import("../components/ui/stepper/index")['StepperDescription']>
    'LazyStepperIndicator': LazyComponent<typeof import("../components/ui/stepper/index")['StepperIndicator']>
    'LazyStepperItem': LazyComponent<typeof import("../components/ui/stepper/index")['StepperItem']>
    'LazyStepperSeparator': LazyComponent<typeof import("../components/ui/stepper/index")['StepperSeparator']>
    'LazyStepperTitle': LazyComponent<typeof import("../components/ui/stepper/index")['StepperTitle']>
    'LazyStepperTrigger': LazyComponent<typeof import("../components/ui/stepper/index")['StepperTrigger']>
    'LazySwitch': LazyComponent<typeof import("../components/ui/switch/index")['Switch']>
    'LazyToaster': LazyComponent<typeof import("../components/ui/toast/index")['Toaster']>
    'LazyToast': LazyComponent<typeof import("../components/ui/toast/index")['Toast']>
    'LazyToastViewport': LazyComponent<typeof import("../components/ui/toast/index")['ToastViewport']>
    'LazyToastAction': LazyComponent<typeof import("../components/ui/toast/index")['ToastAction']>
    'LazyToastClose': LazyComponent<typeof import("../components/ui/toast/index")['ToastClose']>
    'LazyToastTitle': LazyComponent<typeof import("../components/ui/toast/index")['ToastTitle']>
    'LazyToastDescription': LazyComponent<typeof import("../components/ui/toast/index")['ToastDescription']>
    'LazyToastProvider': LazyComponent<typeof import("../components/ui/toast/index")['ToastProvider']>
    'LazyToggleGroup': LazyComponent<typeof import("../components/ui/toggle-group/index")['ToggleGroup']>
    'LazyToggleGroupItem': LazyComponent<typeof import("../components/ui/toggle-group/index")['ToggleGroupItem']>
    'LazyTooltip': LazyComponent<typeof import("../components/ui/tooltip/index")['Tooltip']>
    'LazyTooltipContent': LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipContent']>
    'LazyTooltipTrigger': LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipTrigger']>
    'LazyTooltipProvider': LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipProvider']>
    'LazyVitePwaManifest': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']>
    'LazyNuxtPwaManifest': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']>
    'LazyNuxtPwaAssets': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/NuxtPwaAssets")['default']>
    'LazyPwaAppleImage': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleImage.vue")['default']>
    'LazyPwaAppleSplashScreenImage': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleSplashScreenImage.vue")['default']>
    'LazyPwaFaviconImage': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaFaviconImage.vue")['default']>
    'LazyPwaMaskableImage': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaMaskableImage.vue")['default']>
    'LazyPwaTransparentImage': LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaTransparentImage.vue")['default']>
    'LazyColorScheme': LazyComponent<typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AccessibilityPanel: typeof import("../components/AccessibilityPanel.vue")['default']
export const AppFooter: typeof import("../components/AppFooter.vue")['default']
export const AppHeader: typeof import("../components/AppHeader.vue")['default']
export const DarkModeToggle: typeof import("../components/DarkModeToggle.vue")['default']
export const QRCodeScanner: typeof import("../components/QRCodeScanner.vue")['default']
export const ResearchStepper: typeof import("../components/ResearchStepper.vue")['default']
export const ResultCard: typeof import("../components/ResultCard.vue")['default']
export const TestProgressIndicator: typeof import("../components/TestProgressIndicator.vue")['default']
export const IconsAkarIconsGithubOutlineFill: typeof import("../components/icons/AkarIconsGithubOutlineFill.vue")['default']
export const IconsArcticonsOpenaiChatgpt: typeof import("../components/icons/ArcticonsOpenaiChatgpt.vue")['default']
export const IconsArcticonsVisualStudioCode: typeof import("../components/icons/ArcticonsVisualStudioCode.vue")['default']
export const IconsFeGithub: typeof import("../components/icons/FeGithub.vue")['default']
export const IconsPhChat: typeof import("../components/icons/PhChat.vue")['default']
export const IconsPhDiscordLogoLight: typeof import("../components/icons/PhDiscordLogoLight.vue")['default']
export const IconsPhGoogleChromeLogo: typeof import("../components/icons/PhGoogleChromeLogo.vue")['default']
export const IconsPhTerminalWindowBold: typeof import("../components/icons/PhTerminalWindowBold.vue")['default']
export const IconsPhTerminalWindowLight: typeof import("../components/icons/PhTerminalWindowLight.vue")['default']
export const IconsPhWindowsLogoBold: typeof import("../components/icons/PhWindowsLogoBold.vue")['default']
export const IconsPhWindowsLogoLight: typeof import("../components/icons/PhWindowsLogoLight.vue")['default']
export const IconsTablerBrandDiscord: typeof import("../components/icons/TablerBrandDiscord.vue")['default']
export const IconsTablerBrandVscode: typeof import("../components/icons/TablerBrandVscode.vue")['default']
export const IconsUitGoogle: typeof import("../components/icons/UitGoogle.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const Accordion: typeof import("../components/ui/accordion/index")['Accordion']
export const AccordionContent: typeof import("../components/ui/accordion/index")['AccordionContent']
export const AccordionItem: typeof import("../components/ui/accordion/index")['AccordionItem']
export const AccordionTrigger: typeof import("../components/ui/accordion/index")['AccordionTrigger']
export const Alert: typeof import("../components/ui/alert/index")['Alert']
export const AlertTitle: typeof import("../components/ui/alert/index")['AlertTitle']
export const AlertDescription: typeof import("../components/ui/alert/index")['AlertDescription']
export const Badge: typeof import("../components/ui/badge/index")['Badge']
export const Button: typeof import("../components/ui/button/index")['Button']
export const Card: typeof import("../components/ui/card/index")['Card']
export const CardHeader: typeof import("../components/ui/card/index")['CardHeader']
export const CardTitle: typeof import("../components/ui/card/index")['CardTitle']
export const CardDescription: typeof import("../components/ui/card/index")['CardDescription']
export const CardContent: typeof import("../components/ui/card/index")['CardContent']
export const CardFooter: typeof import("../components/ui/card/index")['CardFooter']
export const Dialog: typeof import("../components/ui/dialog/index")['Dialog']
export const DialogClose: typeof import("../components/ui/dialog/index")['DialogClose']
export const DialogTrigger: typeof import("../components/ui/dialog/index")['DialogTrigger']
export const DialogHeader: typeof import("../components/ui/dialog/index")['DialogHeader']
export const DialogTitle: typeof import("../components/ui/dialog/index")['DialogTitle']
export const DialogDescription: typeof import("../components/ui/dialog/index")['DialogDescription']
export const DialogContent: typeof import("../components/ui/dialog/index")['DialogContent']
export const DialogScrollContent: typeof import("../components/ui/dialog/index")['DialogScrollContent']
export const DialogFooter: typeof import("../components/ui/dialog/index")['DialogFooter']
export const DropdownMenuPortal: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuPortal']
export const DropdownMenu: typeof import("../components/ui/dropdown-menu/index")['DropdownMenu']
export const DropdownMenuTrigger: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuTrigger']
export const DropdownMenuContent: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuContent']
export const DropdownMenuGroup: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuGroup']
export const DropdownMenuRadioGroup: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioGroup']
export const DropdownMenuItem: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuItem']
export const DropdownMenuCheckboxItem: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuCheckboxItem']
export const DropdownMenuRadioItem: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioItem']
export const DropdownMenuShortcut: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuShortcut']
export const DropdownMenuSeparator: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSeparator']
export const DropdownMenuLabel: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuLabel']
export const DropdownMenuSub: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSub']
export const DropdownMenuSubTrigger: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubTrigger']
export const DropdownMenuSubContent: typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubContent']
export const Form: typeof import("../components/ui/form/index")['Form']
export const FormField: typeof import("../components/ui/form/index")['FormField']
export const FormItem: typeof import("../components/ui/form/index")['FormItem']
export const FormLabel: typeof import("../components/ui/form/index")['FormLabel']
export const FormControl: typeof import("../components/ui/form/index")['FormControl']
export const FormMessage: typeof import("../components/ui/form/index")['FormMessage']
export const FormDescription: typeof import("../components/ui/form/index")['FormDescription']
export const Icon: typeof import("../components/ui/icon/index")['Icon']
export const Input: typeof import("../components/ui/input/index")['Input']
export const Label: typeof import("../components/ui/label/index")['Label']
export const Progress: typeof import("../components/ui/progress/index")['Progress']
export const RadioGroup: typeof import("../components/ui/radio-group/index")['RadioGroup']
export const RadioGroupItem: typeof import("../components/ui/radio-group/index")['RadioGroupItem']
export const Sheet: typeof import("../components/ui/sheet/index")['Sheet']
export const SheetTrigger: typeof import("../components/ui/sheet/index")['SheetTrigger']
export const SheetClose: typeof import("../components/ui/sheet/index")['SheetClose']
export const SheetContent: typeof import("../components/ui/sheet/index")['SheetContent']
export const SheetHeader: typeof import("../components/ui/sheet/index")['SheetHeader']
export const SheetTitle: typeof import("../components/ui/sheet/index")['SheetTitle']
export const SheetDescription: typeof import("../components/ui/sheet/index")['SheetDescription']
export const SheetFooter: typeof import("../components/ui/sheet/index")['SheetFooter']
export const Skeleton: typeof import("../components/ui/skeleton/index")['Skeleton']
export const Stepper: typeof import("../components/ui/stepper/index")['Stepper']
export const StepperDescription: typeof import("../components/ui/stepper/index")['StepperDescription']
export const StepperIndicator: typeof import("../components/ui/stepper/index")['StepperIndicator']
export const StepperItem: typeof import("../components/ui/stepper/index")['StepperItem']
export const StepperSeparator: typeof import("../components/ui/stepper/index")['StepperSeparator']
export const StepperTitle: typeof import("../components/ui/stepper/index")['StepperTitle']
export const StepperTrigger: typeof import("../components/ui/stepper/index")['StepperTrigger']
export const Switch: typeof import("../components/ui/switch/index")['Switch']
export const Toaster: typeof import("../components/ui/toast/index")['Toaster']
export const Toast: typeof import("../components/ui/toast/index")['Toast']
export const ToastViewport: typeof import("../components/ui/toast/index")['ToastViewport']
export const ToastAction: typeof import("../components/ui/toast/index")['ToastAction']
export const ToastClose: typeof import("../components/ui/toast/index")['ToastClose']
export const ToastTitle: typeof import("../components/ui/toast/index")['ToastTitle']
export const ToastDescription: typeof import("../components/ui/toast/index")['ToastDescription']
export const ToastProvider: typeof import("../components/ui/toast/index")['ToastProvider']
export const ToggleGroup: typeof import("../components/ui/toggle-group/index")['ToggleGroup']
export const ToggleGroupItem: typeof import("../components/ui/toggle-group/index")['ToggleGroupItem']
export const Tooltip: typeof import("../components/ui/tooltip/index")['Tooltip']
export const TooltipContent: typeof import("../components/ui/tooltip/index")['TooltipContent']
export const TooltipTrigger: typeof import("../components/ui/tooltip/index")['TooltipTrigger']
export const TooltipProvider: typeof import("../components/ui/tooltip/index")['TooltipProvider']
export const VitePwaManifest: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']
export const NuxtPwaManifest: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']
export const NuxtPwaAssets: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/NuxtPwaAssets")['default']
export const PwaAppleImage: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleImage.vue")['default']
export const PwaAppleSplashScreenImage: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleSplashScreenImage.vue")['default']
export const PwaFaviconImage: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaFaviconImage.vue")['default']
export const PwaMaskableImage: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaMaskableImage.vue")['default']
export const PwaTransparentImage: typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaTransparentImage.vue")['default']
export const ColorScheme: typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAccessibilityPanel: LazyComponent<typeof import("../components/AccessibilityPanel.vue")['default']>
export const LazyAppFooter: LazyComponent<typeof import("../components/AppFooter.vue")['default']>
export const LazyAppHeader: LazyComponent<typeof import("../components/AppHeader.vue")['default']>
export const LazyDarkModeToggle: LazyComponent<typeof import("../components/DarkModeToggle.vue")['default']>
export const LazyQRCodeScanner: LazyComponent<typeof import("../components/QRCodeScanner.vue")['default']>
export const LazyResearchStepper: LazyComponent<typeof import("../components/ResearchStepper.vue")['default']>
export const LazyResultCard: LazyComponent<typeof import("../components/ResultCard.vue")['default']>
export const LazyTestProgressIndicator: LazyComponent<typeof import("../components/TestProgressIndicator.vue")['default']>
export const LazyIconsAkarIconsGithubOutlineFill: LazyComponent<typeof import("../components/icons/AkarIconsGithubOutlineFill.vue")['default']>
export const LazyIconsArcticonsOpenaiChatgpt: LazyComponent<typeof import("../components/icons/ArcticonsOpenaiChatgpt.vue")['default']>
export const LazyIconsArcticonsVisualStudioCode: LazyComponent<typeof import("../components/icons/ArcticonsVisualStudioCode.vue")['default']>
export const LazyIconsFeGithub: LazyComponent<typeof import("../components/icons/FeGithub.vue")['default']>
export const LazyIconsPhChat: LazyComponent<typeof import("../components/icons/PhChat.vue")['default']>
export const LazyIconsPhDiscordLogoLight: LazyComponent<typeof import("../components/icons/PhDiscordLogoLight.vue")['default']>
export const LazyIconsPhGoogleChromeLogo: LazyComponent<typeof import("../components/icons/PhGoogleChromeLogo.vue")['default']>
export const LazyIconsPhTerminalWindowBold: LazyComponent<typeof import("../components/icons/PhTerminalWindowBold.vue")['default']>
export const LazyIconsPhTerminalWindowLight: LazyComponent<typeof import("../components/icons/PhTerminalWindowLight.vue")['default']>
export const LazyIconsPhWindowsLogoBold: LazyComponent<typeof import("../components/icons/PhWindowsLogoBold.vue")['default']>
export const LazyIconsPhWindowsLogoLight: LazyComponent<typeof import("../components/icons/PhWindowsLogoLight.vue")['default']>
export const LazyIconsTablerBrandDiscord: LazyComponent<typeof import("../components/icons/TablerBrandDiscord.vue")['default']>
export const LazyIconsTablerBrandVscode: LazyComponent<typeof import("../components/icons/TablerBrandVscode.vue")['default']>
export const LazyIconsUitGoogle: LazyComponent<typeof import("../components/icons/UitGoogle.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyAccordion: LazyComponent<typeof import("../components/ui/accordion/index")['Accordion']>
export const LazyAccordionContent: LazyComponent<typeof import("../components/ui/accordion/index")['AccordionContent']>
export const LazyAccordionItem: LazyComponent<typeof import("../components/ui/accordion/index")['AccordionItem']>
export const LazyAccordionTrigger: LazyComponent<typeof import("../components/ui/accordion/index")['AccordionTrigger']>
export const LazyAlert: LazyComponent<typeof import("../components/ui/alert/index")['Alert']>
export const LazyAlertTitle: LazyComponent<typeof import("../components/ui/alert/index")['AlertTitle']>
export const LazyAlertDescription: LazyComponent<typeof import("../components/ui/alert/index")['AlertDescription']>
export const LazyBadge: LazyComponent<typeof import("../components/ui/badge/index")['Badge']>
export const LazyButton: LazyComponent<typeof import("../components/ui/button/index")['Button']>
export const LazyCard: LazyComponent<typeof import("../components/ui/card/index")['Card']>
export const LazyCardHeader: LazyComponent<typeof import("../components/ui/card/index")['CardHeader']>
export const LazyCardTitle: LazyComponent<typeof import("../components/ui/card/index")['CardTitle']>
export const LazyCardDescription: LazyComponent<typeof import("../components/ui/card/index")['CardDescription']>
export const LazyCardContent: LazyComponent<typeof import("../components/ui/card/index")['CardContent']>
export const LazyCardFooter: LazyComponent<typeof import("../components/ui/card/index")['CardFooter']>
export const LazyDialog: LazyComponent<typeof import("../components/ui/dialog/index")['Dialog']>
export const LazyDialogClose: LazyComponent<typeof import("../components/ui/dialog/index")['DialogClose']>
export const LazyDialogTrigger: LazyComponent<typeof import("../components/ui/dialog/index")['DialogTrigger']>
export const LazyDialogHeader: LazyComponent<typeof import("../components/ui/dialog/index")['DialogHeader']>
export const LazyDialogTitle: LazyComponent<typeof import("../components/ui/dialog/index")['DialogTitle']>
export const LazyDialogDescription: LazyComponent<typeof import("../components/ui/dialog/index")['DialogDescription']>
export const LazyDialogContent: LazyComponent<typeof import("../components/ui/dialog/index")['DialogContent']>
export const LazyDialogScrollContent: LazyComponent<typeof import("../components/ui/dialog/index")['DialogScrollContent']>
export const LazyDialogFooter: LazyComponent<typeof import("../components/ui/dialog/index")['DialogFooter']>
export const LazyDropdownMenuPortal: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuPortal']>
export const LazyDropdownMenu: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenu']>
export const LazyDropdownMenuTrigger: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuTrigger']>
export const LazyDropdownMenuContent: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuContent']>
export const LazyDropdownMenuGroup: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuGroup']>
export const LazyDropdownMenuRadioGroup: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioGroup']>
export const LazyDropdownMenuItem: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuItem']>
export const LazyDropdownMenuCheckboxItem: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuCheckboxItem']>
export const LazyDropdownMenuRadioItem: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuRadioItem']>
export const LazyDropdownMenuShortcut: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuShortcut']>
export const LazyDropdownMenuSeparator: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSeparator']>
export const LazyDropdownMenuLabel: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuLabel']>
export const LazyDropdownMenuSub: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSub']>
export const LazyDropdownMenuSubTrigger: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubTrigger']>
export const LazyDropdownMenuSubContent: LazyComponent<typeof import("../components/ui/dropdown-menu/index")['DropdownMenuSubContent']>
export const LazyForm: LazyComponent<typeof import("../components/ui/form/index")['Form']>
export const LazyFormField: LazyComponent<typeof import("../components/ui/form/index")['FormField']>
export const LazyFormItem: LazyComponent<typeof import("../components/ui/form/index")['FormItem']>
export const LazyFormLabel: LazyComponent<typeof import("../components/ui/form/index")['FormLabel']>
export const LazyFormControl: LazyComponent<typeof import("../components/ui/form/index")['FormControl']>
export const LazyFormMessage: LazyComponent<typeof import("../components/ui/form/index")['FormMessage']>
export const LazyFormDescription: LazyComponent<typeof import("../components/ui/form/index")['FormDescription']>
export const LazyIcon: LazyComponent<typeof import("../components/ui/icon/index")['Icon']>
export const LazyInput: LazyComponent<typeof import("../components/ui/input/index")['Input']>
export const LazyLabel: LazyComponent<typeof import("../components/ui/label/index")['Label']>
export const LazyProgress: LazyComponent<typeof import("../components/ui/progress/index")['Progress']>
export const LazyRadioGroup: LazyComponent<typeof import("../components/ui/radio-group/index")['RadioGroup']>
export const LazyRadioGroupItem: LazyComponent<typeof import("../components/ui/radio-group/index")['RadioGroupItem']>
export const LazySheet: LazyComponent<typeof import("../components/ui/sheet/index")['Sheet']>
export const LazySheetTrigger: LazyComponent<typeof import("../components/ui/sheet/index")['SheetTrigger']>
export const LazySheetClose: LazyComponent<typeof import("../components/ui/sheet/index")['SheetClose']>
export const LazySheetContent: LazyComponent<typeof import("../components/ui/sheet/index")['SheetContent']>
export const LazySheetHeader: LazyComponent<typeof import("../components/ui/sheet/index")['SheetHeader']>
export const LazySheetTitle: LazyComponent<typeof import("../components/ui/sheet/index")['SheetTitle']>
export const LazySheetDescription: LazyComponent<typeof import("../components/ui/sheet/index")['SheetDescription']>
export const LazySheetFooter: LazyComponent<typeof import("../components/ui/sheet/index")['SheetFooter']>
export const LazySkeleton: LazyComponent<typeof import("../components/ui/skeleton/index")['Skeleton']>
export const LazyStepper: LazyComponent<typeof import("../components/ui/stepper/index")['Stepper']>
export const LazyStepperDescription: LazyComponent<typeof import("../components/ui/stepper/index")['StepperDescription']>
export const LazyStepperIndicator: LazyComponent<typeof import("../components/ui/stepper/index")['StepperIndicator']>
export const LazyStepperItem: LazyComponent<typeof import("../components/ui/stepper/index")['StepperItem']>
export const LazyStepperSeparator: LazyComponent<typeof import("../components/ui/stepper/index")['StepperSeparator']>
export const LazyStepperTitle: LazyComponent<typeof import("../components/ui/stepper/index")['StepperTitle']>
export const LazyStepperTrigger: LazyComponent<typeof import("../components/ui/stepper/index")['StepperTrigger']>
export const LazySwitch: LazyComponent<typeof import("../components/ui/switch/index")['Switch']>
export const LazyToaster: LazyComponent<typeof import("../components/ui/toast/index")['Toaster']>
export const LazyToast: LazyComponent<typeof import("../components/ui/toast/index")['Toast']>
export const LazyToastViewport: LazyComponent<typeof import("../components/ui/toast/index")['ToastViewport']>
export const LazyToastAction: LazyComponent<typeof import("../components/ui/toast/index")['ToastAction']>
export const LazyToastClose: LazyComponent<typeof import("../components/ui/toast/index")['ToastClose']>
export const LazyToastTitle: LazyComponent<typeof import("../components/ui/toast/index")['ToastTitle']>
export const LazyToastDescription: LazyComponent<typeof import("../components/ui/toast/index")['ToastDescription']>
export const LazyToastProvider: LazyComponent<typeof import("../components/ui/toast/index")['ToastProvider']>
export const LazyToggleGroup: LazyComponent<typeof import("../components/ui/toggle-group/index")['ToggleGroup']>
export const LazyToggleGroupItem: LazyComponent<typeof import("../components/ui/toggle-group/index")['ToggleGroupItem']>
export const LazyTooltip: LazyComponent<typeof import("../components/ui/tooltip/index")['Tooltip']>
export const LazyTooltipContent: LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipContent']>
export const LazyTooltipTrigger: LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipTrigger']>
export const LazyTooltipProvider: LazyComponent<typeof import("../components/ui/tooltip/index")['TooltipProvider']>
export const LazyVitePwaManifest: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']>
export const LazyNuxtPwaManifest: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest")['default']>
export const LazyNuxtPwaAssets: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/NuxtPwaAssets")['default']>
export const LazyPwaAppleImage: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleImage.vue")['default']>
export const LazyPwaAppleSplashScreenImage: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleSplashScreenImage.vue")['default']>
export const LazyPwaFaviconImage: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaFaviconImage.vue")['default']>
export const LazyPwaMaskableImage: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaMaskableImage.vue")['default']>
export const LazyPwaTransparentImage: LazyComponent<typeof import("../node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaTransparentImage.vue")['default']>
export const LazyColorScheme: LazyComponent<typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
