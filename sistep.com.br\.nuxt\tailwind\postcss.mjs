// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 01/06/2025, 04:22:44
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["D:/SISTEP/web/sistep.com.br/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/plugins/**/*.{js,ts,mjs}","D:/SISTEP/web/sistep.com.br/composables/**/*.{js,ts,mjs}","D:/SISTEP/web/sistep.com.br/utils/**/*.{js,ts,mjs}","D:/SISTEP/web/sistep.com.br/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/SISTEP/web/sistep.com.br/app.config.{js,ts,mjs}","D:/SISTEP/web/sistep.com.br/app/spa-loading-template.html"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;