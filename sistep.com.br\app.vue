<template>
  <div class="relative">
    <NuxtLayout>

      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup>
// import { onMounted } from 'vue'; // Importação não mais necessária

// Lógica onMounted removida para simplificar e evitar conflitos
// A lógica de tema será centralizada no AppFooter e no plugin

</script>

<style>
/* Estilos globais específicos */
:root.dark,
html.dark {
  color-scheme: dark;
}

/* Garantir transição suave */
html,
body {
  transition: background-color 0.3s ease, color 0.3s ease;
}
</style>