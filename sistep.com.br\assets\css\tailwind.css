@tailwind base;

@custom-variant dark (&:is(.dark *));
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  /* Adicionar transição suave para mudar de tema */
  html {
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-weight: normal;
  }

  /* scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.5);
    border-radius: 6px;
    cursor: pointer;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 1);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.15);
  }

  .dark {
    /* scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 6px;
      cursor: pointer;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: white;
    }

    ::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
}

@layer components {
  .bg-background {
    background-color: hsl(var(--background));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .bg-card {
    background-color: hsl(var(--card));
  }

  .text-card-foreground {
    color: hsl(var(--card-foreground));
  }

  .bg-popover {
    background-color: hsl(var(--popover));
  }

  .text-popover-foreground {
    color: hsl(var(--popover-foreground));
  }

  .bg-primary {
    background-color: hsl(var(--primary));
  }

  .text-primary {
    color: hsl(var(--primary));
  }

  .text-primary-foreground {
    color: hsl(var(--primary-foreground));
  }

  .bg-secondary {
    background-color: hsl(var(--secondary));
  }

  .text-secondary {
    color: hsl(var(--secondary));
  }

  .text-secondary-foreground {
    color: hsl(var(--secondary-foreground));
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .text-muted {
    color: hsl(var(--muted));
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  .bg-accent {
    background-color: hsl(var(--accent));
  }

  .text-accent {
    color: hsl(var(--accent));
  }

  .text-accent-foreground {
    color: hsl(var(--accent-foreground));
  }

  .bg-destructive {
    background-color: hsl(var(--destructive));
  }

  .text-destructive {
    color: hsl(var(--destructive));
  }

  .text-destructive-foreground {
    color: hsl(var(--destructive-foreground));
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .bg-input {
    background-color: hsl(var(--input));
  }

  .ring-ring {
    --tw-ring-color: hsl(var(--ring));
  }
}

/* Animações personalizadas */
@keyframes accordion-down {
  from { 
    height: 0;
    opacity: 0;
    transform: scaleY(0.95);
  }
  to { 
    height: var(--reka-accordion-content-height);
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes accordion-up {
  from { 
    height: var(--reka-accordion-content-height);
    opacity: 1;
    transform: scaleY(1);
  }
  to { 
    height: 0;
    opacity: 0;
    transform: scaleY(0.95);
  }
}

@keyframes collapsible-down {
  from { height: 0 }
  to { height: var(--radix-collapsible-content-height) }
}

@keyframes collapsible-up {
  from { height: var(--radix-collapsible-content-height) }
  to { height: 0 }
}

/* Estilos adicionais para compatibilidade */
.animate-accordion-down {
  animation: accordion-down 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-accordion-up {
  animation: accordion-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-collapsible-down {
  animation: collapsible-down 0.3s ease-in-out;
}

.animate-collapsible-up {
  animation: collapsible-up 0.3s ease-in-out;
}

/* Smooth accordion transitions - global styles */
.accordion-content-smooth {
  overflow: hidden !important;
}

.accordion-content-smooth[data-state="closed"] {
  animation: accordion-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.accordion-content-smooth[data-state="open"] {
  animation: accordion-down 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Disable conflicting animations */
.accordion-content-smooth .accordion-content-inner {
  animation: none !important;
}

/* Enhanced easing for better feel */
.accordion-smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Estilos para o sistema de cores do projeto */
.sistep-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
}

.sistep-text-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Utilitários para responsividade */
.container-sistep {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-sistep {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container-sistep {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-sistep {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-sistep {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-sistep {
    max-width: 1536px;
  }
}

@theme inline {
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  @keyframes accordion-down {
  from {
    height: 0;
    }
  to {
    height: var(--reka-accordion-content-height);
    }
  }
  @keyframes accordion-up {
  from {
    height: var(--reka-accordion-content-height);
    }
  to {
    height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}