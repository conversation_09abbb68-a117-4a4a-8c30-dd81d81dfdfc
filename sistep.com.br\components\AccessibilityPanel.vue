<template>
  <div class="accessibility-panel" aria-label="Controles de acessibilidade">
    <Button @click="togglePanel" aria-label="Abrir painel de acessibilidade" class="accessibility-toggle">
      <span v-if="!isPanelOpen" class="sr-only">Abrir configurações de acessibilidade</span>
      <span v-else class="sr-only">Fechar configurações de acessibilidade</span>
      <Icon name="settings" aria-hidden="true" class="h-5 w-5" />
    </Button>
    
    <div v-if="isPanelOpen" class="accessibility-controls p-4 border rounded-lg shadow-md mt-2 bg-background absolute right-0 z-10">
      <h2 class="text-sm font-medium mb-3">Acessibilidade</h2>
      
      <div class="control-group">
        <div class="flex items-center justify-between">
          <Label for="contrast" class="text-sm">Alto contraste</Label>
          <Switch v-model="highContrast" id="contrast" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Button } from '~/components/ui/button'
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import { useTestStore } from '~/stores/test'
import { Icon } from '~/components/ui/icon'

const testStore = useTestStore()
const isPanelOpen = ref(false)
const highContrast = ref(false)

function togglePanel() {
  isPanelOpen.value = !isPanelOpen.value
}

watch(highContrast, (newValue) => {
  document.documentElement.classList.toggle('high-contrast', newValue)
})
</script>

<style scoped>
.accessibility-panel {
  position: relative;
}

.accessibility-toggle {
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.accessibility-controls {
  width: 250px;
}
</style> 