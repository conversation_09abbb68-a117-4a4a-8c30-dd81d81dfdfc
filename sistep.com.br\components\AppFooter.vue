<template>
    <!-- Footer Normal -->
    <footer class="flex h-auto items-center gap-4 p-4 md:px-6 z-20 bg-muted/20 border-t">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <!-- Links e Copyright (centralizados no mobile, à esquerda no desktop) -->
                <div class="flex flex-col md:flex-row items-center md:items-start gap-4 text-sm text-muted-foreground">
                    <div class="flex flex-wrap justify-center md:justify-start gap-x-4 gap-y-2">
                        <span class="text-xs text-muted-foreground/50">© {{ new Date().getFullYear() }} SISTEP</span>
                        <span class="text-muted-foreground/50">•</span>
                        <a @click="showTCLE" class="text-xs hover:cursor-pointer hover:underline">TCLE</a>
                        <a @click="showLGPD" class="text-xs hover:cursor-pointer hover:underline">LGPD</a>
                        <a @click="showTerms" class="text-xs hover:cursor-pointer hover:underline">Termos</a>
                        <a @click="showPrivacy" class="text-xs hover:cursor-pointer hover:underline">Privacidade</a>
                    </div>
                </div>

                <!-- Alternador de tema (à direita no desktop) -->
                <div class="flex items-center justify-center md:justify-end gap-2 mt-4 md:mt-0">
                    <svg v-if="colorMode.value === 'light'" class="w-4 h-4 text-yellow-500" fill="currentColor"
                        viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" />
                    </svg>
                    <svg v-else class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                    <Switch :checked="isDarkComputed" @update:checked="toggleTheme" class="data-[state=checked]:bg-slate-600" />
                </div>
            </div>
        </div>
    </footer>

    <!-- Modal para TCLE -->
    <Dialog :open="showTCLEModal" @update:open="showTCLEModal = false">
        <DialogContent class="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
                <DialogTitle>Termo de Consentimento Livre e Esclarecido (TCLE)</DialogTitle>
            </DialogHeader>
            <TCLEContent />
            <DialogFooter>
                <Button v-if="showAcceptButton" variant="default" @click="acceptTCLE">Concordo e Continuo</Button>
                <Button variant="outline" @click="showTCLEModal = false">{{ showAcceptButton ? 'Não Concordo' : 'Fechar' }}</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>

    <!-- Modal para LGPD -->
    <Dialog :open="showLGPDModal" @update:open="showLGPDModal = false">
        <DialogContent class="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
                <DialogTitle>Lei Geral de Proteção de Dados (LGPD)</DialogTitle>
            </DialogHeader>
            <LGPDContent />
            <DialogFooter>
                <Button variant="outline" @click="showLGPDModal = false">Fechar</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>

    <!-- Modal para Termos de Uso -->
    <Dialog :open="showTermsModal" @update:open="showTermsModal = false">
        <DialogContent class="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
                <DialogTitle>Termos de Uso</DialogTitle>
            </DialogHeader>
            <TermsContent />
            <DialogFooter>
                <Button variant="outline" @click="showTermsModal = false">Fechar</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>

    <!-- Modal para Privacidade -->
    <Dialog :open="showPrivacyModal" @update:open="showPrivacyModal = false">
        <DialogContent class="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
                <DialogTitle>Política de Privacidade</DialogTitle>
            </DialogHeader>
            <PrivacyContent />
            <DialogFooter>
                <Button variant="outline" @click="showPrivacyModal = false">Fechar</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Switch } from '~/components/ui/switch'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '~/components/ui/dialog'
import TCLEContent from '~/components/ui/legal/TCLEContent.vue'
import LGPDContent from '~/components/ui/legal/LGPDContent.vue'
import TermsContent from '~/components/ui/legal/TermsContent.vue'
import PrivacyContent from '~/components/ui/legal/PrivacyContent.vue'

// Props
const props = defineProps({
    showAcceptButton: {
        type: Boolean,
        default: false
    }
})

// Emits
const emit = defineEmits(['tcle-accepted'])

// Usar o composable do @nuxtjs/color-mode
const colorMode = useColorMode()

// Computed property para controlar o estado do Switch
const isDarkComputed = computed(() => colorMode.value === 'dark')

// Estados dos modais
const showTCLEModal = ref(false)
const showLGPDModal = ref(false)
const showTermsModal = ref(false)
const showPrivacyModal = ref(false)

// Função para alternar o tema usando a preferência do módulo
const toggleTheme = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

// Funções para abrir modais
const showTCLE = () => showTCLEModal.value = true
const showLGPD = () => showLGPDModal.value = true
const showTerms = () => showTermsModal.value = true
const showPrivacy = () => showPrivacyModal.value = true

// Função para aceitar TCLE
const acceptTCLE = () => {
    emit('tcle-accepted')
    showTCLEModal.value = false
}
</script>