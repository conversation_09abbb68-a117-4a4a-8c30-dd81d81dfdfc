<template>
    <header class="flex h-auto items-center gap-4 p-2 md:px-4 lg:h-[60px] lg:px-6 z-20 transition-all duration-300"
        :class="{ 'header-scrolled': isScrolled }">
        <div class="container mx-auto flex items-center justify-between gap-4">
            <NuxtLink to="/"
                class="logo logo-inverted dark:logo-inverted-dark text-foreground dark:text-foreground border-foreground dark:border-foreground flex flex-col items-center justify-center transition-all h-8 w-auto border rounded-sm p-2">
                <h3 v-if="miniLogo" class="font-mono text-2xl font-bold">S</h3>
                <h3 v-else class="font-mono text-2xl font-bold">SISTEP</h3>
            </NuxtLink>

            <div class="flex logo-inverted items-center gap-2 text-white dark:text-white" v-if="pageTitle">
                <p class="text-lg font-medium hidden sm:block text-white dark:text-white">/{{ pageTitle }}</p>
            </div>

            <div class="flex items-center gap-2 ml-auto">
                <!-- Ícone indicador da Pesquisa -->
                <div v-if="shouldShowIcon" class="flex items-center gap-2">
                    <button @click="toggleAlert" class="relative p-1.5 rounded-full transition-all duration-200" :class="needsAttention ?
                        'bg-amber-100 hover:bg-amber-200 text-amber-600' :
                        'bg-muted hover:bg-accent'"
                        :title="needsAttention ? 'Ação necessária na pesquisa' : 'Status da pesquisa'">
                        <AlertTriangle class="w-5 h-5" />

                        <!-- Indicador de ação necessária -->
                        <div v-if="needsAttention"
                            class="absolute top-0 right-0 w-2 h-2 bg-amber-500 rounded-full animate-pulse">
                        </div>
                    </button>
                </div>

                <!-- Botão contextual para a página de pesquisa -->
                <div v-if="isResearchPage" class="flex items-center gap-2">
                    <!-- Desktop: botão normal -->
                    <Button class="hidden md:flex dark:text-white w-full" size="lg" @click="handleResearchAction">
                        {{ researchButtonText }}
                        <component :is="buttonIcon" class="w-4 h-4 ml-2" />
                    </Button>
                    
                    <!-- Mobile: botão menor -->
                    <Button class="md:hidden flex dark:text-white w-full" size="sm" @click="handleResearchAction">
                        {{ researchButtonText }}
                        <component :is="buttonIcon" class="w-4 h-4 ml-2" />
                    </Button>
                </div>

            </div>
        </div>
    </header>


    <!-- Banner da Pesquisa (aparece no topo quando aberto) -->
    <div v-if="isResearchContext && isAlertOpen" class="bg-amber-50 border-b border-amber-200 p-3 z-50 shadow-sm">
        <div class="container mx-auto">
            <!-- Desktop Layout -->
            <div class="hidden md:flex items-center justify-between text-sm">
                <div class="flex w-full items-center justify-between gap-4">
                    <span class="text-amber-800">
                        {{ currentStepDescription }}
                    </span>
                    <div class="flex items-center gap-3 text-amber-700 ml-auto">
                        <a @click="goToResearchPage"
                            class="underline hover:no-underline cursor-pointer whitespace-nowrap">
                            ← Voltar para a pesquisa
                        </a>
                        <span v-if="shouldShowContinueLink" class="text-amber-400">•</span>
                        <a v-if="shouldShowContinueLink" @click="proceedToNextStep"
                            class="underline hover:no-underline cursor-pointer font-medium whitespace-nowrap">
                            {{ nextStepButtonText }} →
                        </a>
                    </div>
                </div>
                <button @click="closeAlert" class="text-amber-600 hover:text-amber-800 text-lg leading-none ml-4">
                    <X class="w-4 h-4" />
                </button>
            </div>

            <!-- Mobile Layout -->
            <div class="md:hidden text-sm">
                <div class="flex items-start justify-between mb-2">
                    <span class="text-amber-800 text-xs leading-relaxed pr-4">
                        {{ currentStepDescription }}
                    </span>
                    <button @click="closeAlert"
                        class="text-amber-600 hover:text-amber-800 text-lg leading-none flex-shrink-0">
                        <X class="w-4 h-4" />
                    </button>
                </div>
                <div class="text-amber-800 text-xs mb-3">
                    {{ currentStepDescription }}
                </div>
                <div class="flex items-center gap-3 text-amber-700 text-xs">
                    <a @click="goToResearchPage" class="underline hover:no-underline cursor-pointer">
                        ← Voltar para a pesquisa
                    </a>
                    <span v-if="shouldShowContinueLink" class="text-amber-400">•</span>
                    <a v-if="shouldShowContinueLink" @click="proceedToNextStep"
                        class="underline hover:no-underline cursor-pointer font-medium">
                        {{ nextStepButtonText }} →
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay quando banner está aberto -->
    <div v-if="isResearchContext && isAlertOpen" class="fixed inset-0 bg-black/25 z-40 pointer-events-none">
    </div>


</template>

<script setup>
import { AlertTriangle, Play, CheckCircle, X, Check, ArrowDown } from 'lucide-vue-next'
import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Button from './ui/button/Button.vue'
import { useResearchStore } from '~/stores/research'

const route = useRoute()
const router = useRouter()
const researchStore = useResearchStore()
const isAlertOpen = ref(false)
const isScrolled = ref(false)

const { miniLogo, pageTitle } = defineProps({
    miniLogo: {
        type: Boolean,
        default: true
    },
    pageTitle: {
        type: String,
        default: ''
    }
});

// Computed properties using the store
const isResearchContext = computed(() => researchStore.isResearchContext)

// Show icon only when not on /pesquisa page
const shouldShowIcon = computed(() => {
    return isResearchContext.value && route.path !== '/pesquisa'
})

const currentStep = computed(() => researchStore.currentStep)

// Detect if on DASS-21 results page
const isDASS21ResultsPage = computed(() => {
    return route.path === '/DASS-21/apply' && 
           route.query.participant_id && 
           typeof localStorage !== 'undefined' && 
           localStorage.getItem('researchDASS21Completed') === 'true'
})

// Detect if on any DASS-21 page during research
const isDASS21ResearchPage = computed(() => {
    return route.path === '/DASS-21/apply' && route.query.participant_id
})

const canProceedToNext = computed(() => researchStore.canProceedToNext)

// Icon needs attention when can proceed to next step AND when should show continue link
const needsAttention = computed(() => researchStore.needsAttention && shouldShowContinueLink.value)

// Check if on research page
const isResearchPage = computed(() => route.path === '/pesquisa')

// Button icon based on research state
const buttonIcon = computed(() => {
    return currentStep.value === 5 ? Check : ArrowDown
})

// Show continue link only if on research page or if step 1 or 4
const shouldShowContinueLink = computed(() => {
    return isResearchPage.value || currentStep.value === 1 || currentStep.value === 4 || isDASS21ResultsPage.value
})

// Reactive computed properties from store
const currentStepDescription = computed(() => {
    if (isDASS21ResultsPage.value) {
        return 'DASS-21 concluído! Prossiga para a próxima etapa.'
    }
    return researchStore.currentStepDescription
})

const nextStepButtonText = computed(() => {
    if (isDASS21ResultsPage.value) {
        return 'Acessar Painel do Psicólogo'
    }
    return researchStore.nextStepButtonText
})

// Research button text - now fully reactive
const researchButtonText = computed(() => {
    return researchStore.researchButtonText(isResearchPage.value)
})

// Funções
function toggleAlert() {
    isAlertOpen.value = !isAlertOpen.value
}

function closeAlert() {
    isAlertOpen.value = false
}

function handleResearchAction() {
    // If on research page, use the new scroll and expand function
    if (route.path === '/pesquisa') {
        // Try to use startResearch function if available
        if (typeof window !== 'undefined' && window.startResearch) {
            window.startResearch();
            return;
        }
        
        // Fallback: emit custom event
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('start-research'));
            return;
        }
    }
    
    // If not on research page, navigate there first
    if (route.path !== '/pesquisa') {
        router.push('/pesquisa').then(() => {
            // Wait a bit for page to load then try to call function
            setTimeout(() => {
                if (typeof window !== 'undefined' && window.startResearch) {
                    window.startResearch();
                } else {
                    // Fallback to old behavior
                    nextTick(() => {
                        const element = document.getElementById('participation-section');
                        if (element) {
                            element.scrollIntoView({ 
                                behavior: 'smooth', 
                                block: 'start',
                                inline: 'nearest'
                            });
                        }
                    });
                }
            }, 500);
        });
        return;
    }
    
    // Original fallback for uncovered cases
    nextTick(() => {
        const element = document.getElementById('participation-section');
        if (element) {
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start',
                inline: 'nearest'
            });
        }
    });
}

function goToResearchPage() {
    isAlertOpen.value = false
    if (route.path !== '/pesquisa') {
        router.push('/pesquisa')
    }
}

function proceedToNextStep() {
    isAlertOpen.value = false

    // Se estiver na página de resultados do DASS-21, simular clique no botão "Continuar para Visão do Psicólogo"
    if (isDASS21ResultsPage.value) {
        const continueButton = document.querySelector('[data-testid="continue-to-panel"]')
        if (continueButton) {
            continueButton.click()
            return
        }
        // Fallback: navegar diretamente se o botão não for encontrado
        const participantId = route.query.participant_id
        const returnUrl = route.query.research_return_url || `${window.location.origin}/pesquisa`
        window.location.href = `https://app.sistep.com.br?participant_id=${participantId}&current_step=3&research_return_url=${encodeURIComponent(returnUrl + '?stepCompleted=panel')}`
        return
    }

    // Se estiver na página de pesquisa, usar a nova função de scroll e expansão
    if (route.path === '/pesquisa') {
        if (typeof window !== 'undefined' && window.startResearch) {
            window.startResearch();
            return;
        }
        
        // Fallback: emitir evento para que a página pesquisa.vue possa reagir
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('research-proceed-next', {
                detail: { currentStep: currentStep.value }
            }));
        }
        return;
    }

    // Se não estiver na página de pesquisa, navegar para lá e então usar a nova funcionalidade
    router.push('/pesquisa').then(() => {
        setTimeout(() => {
            if (typeof window !== 'undefined' && window.startResearch) {
                window.startResearch();
            } else {
                // Fallback: emitir evento
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('research-proceed-next', {
                        detail: { currentStep: currentStep.value }
                    }));
                }
            }
        }, 500);
    });
}

// Função para detectar scroll
function handleScroll() {
    isScrolled.value = window.scrollY > 10
}

// Lifecycle hooks
onMounted(() => {
    if (typeof window !== 'undefined') {
        window.addEventListener('scroll', handleScroll)
        // Verificar posição inicial
        handleScroll()
    }
})

onUnmounted(() => {
    if (typeof window !== 'undefined') {
        window.removeEventListener('scroll', handleScroll)
    }
})
</script>

<style scoped>
/* Header sticky com transições suaves */
.header-sticky {
    position: sticky;
    top: 0;
    background-color: transparent;
    backdrop-filter: blur(0px);
}

.header-scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* Dark mode support */
.dark .header-scrolled {
    background-color: rgba(0, 0, 0, 0.95);
}

/* Logo transitions */
.logo {
    transition: all 0.3s ease;
}

/* Logo com efeito de inversão automática */
.logo-inverted {
    mix-blend-mode: difference;
    color: white;
    border-color: white;
    filter: contrast(1.2) brightness(1.1);
}

.logo-inverted:hover {
    filter: contrast(1.4) brightness(1.2);
    transform: scale(1.02);
}

/* Animação suave para o banner */
.bg-amber-50 {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

</style>