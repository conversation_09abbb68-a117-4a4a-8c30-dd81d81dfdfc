<script setup lang="ts">
import { computed, ref, nextTick } from 'vue'
import { Check, Clock, AlertCircle, RotateCcw, ChevronDown, Loader2, ChevronRight, ArrowRight } from 'lucide-vue-next'
import Accordion from '~/components/ui/accordion/Accordion.vue'
import AccordionContent from '~/components/ui/accordion/AccordionContent.vue'
import AccordionItem from '~/components/ui/accordion/AccordionItem.vue'
import AccordionTrigger from '~/components/ui/accordion/AccordionTrigger.vue'
import { Button } from '~/components/ui/button'

interface Action {
    id: string
    text: string
    url?: string
    icon?: any
    variant?: 'primary' | 'secondary' | 'outline'
    disabled?: boolean
    allowWhen?: 'active' | 'completed' | 'always'
}

interface Step {
    step: number
    title: string
    description: string
    detailsText?: string
    actionText?: string
    actionUrl?: string
    actions?: Action[]
    allowRetry?: boolean
    estimatedTime?: string
    requirements?: string[]
}

interface Props {
    steps?: Step[]
    currentStep?: number
    activityLog?: Record<string, string[]>
    orientation?: 'vertical' | 'horizontal'
    expandedStep?: string | null
}

const props = withDefaults(defineProps<Props>(), {
    steps: () => [],
    currentStep: 1,
    activityLog: () => ({}),
    orientation: 'vertical',
    expandedStep: null
})

const emit = defineEmits<{
    'step-action': [step: number, actionId: string, url?: string]
    'step-retry': [step: number]
    'update:expandedStep': [value: string | null]
    'step-transition-start': [fromStep: number, toStep: number]
    'step-transition-complete': [step: number]
}>()

// Transition states
const isTransitioning = ref(false)
const transitioningFromStep = ref<number | null>(null)
const transitioningToStep = ref<number | null>(null)

// Compute step status based on current step
const getStepStatus = (stepNumber: number) => {
    if (stepNumber < props.currentStep) return 'completed'
    if (stepNumber === props.currentStep) return 'active'
    return 'pending'
}

// Get activities for a specific step
const getStepActivities = (stepNumber: number) => {
    return props.activityLog[`step${stepNumber}`] || []
}

// Check if action is allowed based on step status and action rules
const isActionAllowed = (action: Action, stepStatus: string) => {
    if (action.disabled) return false

    switch (action.allowWhen) {
        case 'active':
            return stepStatus === 'active'
        case 'completed':
            return stepStatus === 'completed'
        case 'always':
            return true
        default:
            return stepStatus === 'active' || stepStatus === 'completed'
    }
}

// Get all actions for a step (including legacy single action)
const getStepActions = (step: Step): Action[] => {
    const actions: Action[] = []

    // Add new multiple actions if they exist
    if (step.actions && step.actions.length > 0) {
        actions.push(...step.actions)
    }

    // Add legacy single action for compatibility
    if (step.actionText && !step.actions) {
        actions.push({
            id: 'primary',
            text: step.actionText,
            url: step.actionUrl,
            variant: 'primary',
            allowWhen: 'active'
        })
    }

    return actions
}

// Smooth transition between steps
const transitionToStep = async (targetStep: number) => {
    if (isTransitioning.value) return

    const currentStepNumber = props.currentStep
    if (currentStepNumber === targetStep) return

    isTransitioning.value = true
    transitioningFromStep.value = currentStepNumber
    transitioningToStep.value = targetStep

    emit('step-transition-start', currentStepNumber, targetStep)

    try {
        // Step 1: Collapse current step if it's expanded
        if (props.expandedStep === `step-${currentStepNumber}`) {
            emit('update:expandedStep', null)
            await new Promise(resolve => setTimeout(resolve, 300))
        }

        // Step 2: Scroll to target step
        await scrollToStep(targetStep)

        // Step 3: Expand target step
        emit('update:expandedStep', `step-${targetStep}`)
        await new Promise(resolve => setTimeout(resolve, 300))

        emit('step-transition-complete', targetStep)
    } finally {
        isTransitioning.value = false
        transitioningFromStep.value = null
        transitioningToStep.value = null
    }
}

// Smooth scroll to specific step
const scrollToStep = async (stepNumber: number): Promise<void> => {
    return new Promise((resolve) => {
        const stepElement = document.querySelector(`[data-step="${stepNumber}"]`)
        if (stepElement) {
            stepElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            })
            // Wait for scroll to complete
            setTimeout(resolve, 500)
        } else {
            resolve()
        }
    })
}

// Handle action click with transition
const handleActionClick = async (step: Step, action: Action) => {
    // Only transition for TCLE acceptance (internal action)
    // External actions (DASS-21, Panel) will be handled by return logic
    if (action.id === 'accept-tcle') {
        // TCLE is internal, so we can transition to next step immediately
        await transitionToStep(step.step + 1);
    }
    
    // For external actions, don't transition - they will return and trigger transition
    // The store's handleReturnFromExternal will update currentStep when they return

    emit('step-action', step.step, action.id, action.url)
}

// Handle step retry with transition
const handleStepRetry = async (stepNumber: number) => {
    await transitionToStep(stepNumber)
    emit('step-retry', stepNumber)
}

// Handle accordion value change
const handleAccordionChange = (value: string | null) => {
    if (!isTransitioning.value) {
        emit('update:expandedStep', value)
    }
}

// Check if step is in transition
const isStepTransitioning = (stepNumber: number) => {
    return isTransitioning.value && (
        transitioningFromStep.value === stepNumber ||
        transitioningToStep.value === stepNumber
    )
}

// Expose transition function for external use
defineExpose({
    transitionToStep,
    scrollToStep
})
</script>

<template>
    <div class="research-stepper">
        <Accordion type="single" :model-value="expandedStep" @update:model-value="handleAccordionChange"
            class="w-full space-y-4" collapsible>


            <AccordionItem v-for="(step, index) in steps" :key="step.step" :data-step="step.step"
                :value="`step-${step.step}`" class="border-none relative">
                <div>
                    <!-- Timeline connector line -->
                    <div v-if="index !== steps.length - 1"
                        class="absolute left-[17px] top-[28px] w-0.5 h-[calc(100%)] z-0 transition-all duration-500"
                        :class="{
                            'bg-primary dark:bg-primary': getStepStatus(step.step) === 'completed',
                            'bg-border dark:bg-slate-600': getStepStatus(step.step) === 'active',
                            'bg-border dark:bg-slate-700': getStepStatus(step.step) === 'pending',
                            '': isStepTransitioning(step.step)
                        }" />
                </div>
                <AccordionTrigger class="hover:no-underline p-0 " :disabled="isTransitioning">

                    <div class="flex items-start gap-4 w-full pb-8 transition-all duration-300" :class="{
                        '': isTransitioning && !isStepTransitioning(step.step),
                        '': isStepTransitioning(step.step)
                    }">
                        <!-- Step indicator circle -->
                        <div class="relative z-10 flex items-center justify-center w-9 h-9 rounded-full border-2 transition-all duration-300"
                            :class="{
                                'border-primary bg-primary text-primary-foreground dark:border-primary dark:bg-blue-800 dark:text-white': getStepStatus(step.step) === 'completed',
                                'border-primary bg-primary text-primary-foreground ring-2 ring-primary/20 dark:border-primary dark:bg-primary dark:text-white dark:ring-primary/30': getStepStatus(step.step) === 'active',
                                'border-muted-foreground/30 bg-background dark:bg-slate-900 text-muted-foreground dark:border-slate-600 dark:text-slate-400': getStepStatus(step.step) === 'pending',
                                ' ring-4 ring-blue-200 dark:ring-blue-800': isStepTransitioning(step.step)
                            }">
                            <!-- Transition loading indicator -->
                            <!-- <Loader2 v-if="isStepTransitioning(step.step)" class="w-5 h-5 animate-spin" /> -->
                            <Check v-if="getStepStatus(step.step) === 'completed'" class="w-5 h-5" />
                            <ArrowRight v-else-if="getStepStatus(step.step) === 'active'" class="w-5 h-5" />
                            <span v-else class="text-sm font-semibold">{{ step.step }}</span>
                        </div>

                        <!-- Step content -->
                        <div class="flex-1 text-left">
                            <div class="flex items-center gap-2 mb-1">
                                <h3 class="text-xl font-semibold transition-colors" :class="{
                                    'text-foreground dark:text-white': getStepStatus(step.step) !== 'pending',
                                    'text-muted-foreground dark:text-slate-400': getStepStatus(step.step) === 'pending'
                                }">
                                    {{ step.title }}
                                </h3>

                                <!-- Transition status indicator -->
                                <Loader2 v-if="isStepTransitioning(step.step)" class="w-5 h-5 animate-spin" />
                                <!-- <div v-if="isStepTransitioning(step.step)"
                                        class="px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-full dark:bg-blue-900/30 dark:text-blue-300 animate-pulse">
                                        {{ transitioningToStep === step.step ? 'Ativando...' : 'Finalizando...' }}
                                    </div> -->
                            </div>

                            <p class="text-sm transition-colors" :class="{
                                'text-muted-foreground dark:text-slate-400': getStepStatus(step.step) === 'pending',
                                'text-foreground/80 dark:text-slate-300': getStepStatus(step.step) !== 'pending'
                            }">
                                {{ step.description }}
                            </p>
                        </div>
                    </div>

                    <!-- Custom icon slot -->
                    <template #icon>
                        <ChevronDown
                            class="w-6 h-6 text-muted-foreground dark:text-slate-400 transition-all duration-300 ease-in-out"
                            :class="{ 'opacity-50': isTransitioning }" />
                    </template>
                </AccordionTrigger>

                <AccordionContent class="pl-[75px] pr-0">
                    <div class="space-y-4 text-xs pb-12">
                        <!-- Step details -->
                        <div v-if="step.detailsText" class="text-muted-foreground dark:text-slate-300 space-y-2">
                            <h4 class="font-medium text-foreground dark:text-white">Instruções:</h4>
                            <p class="leading-normal text-muted-foreground dark:text-slate-300">{{ step.detailsText
                                }}</p>
                        </div>

                        <!-- Requirements -->
                        <div v-if="step.requirements && step.requirements.length > 0" class="space-y-2">
                            <h4 class="font-medium text-foreground dark:text-white">Requisitos:</h4>
                            <ul class="space-y-0">
                                <li v-for="requirement in step.requirements" :key="requirement"
                                    class="flex items-start gap-2 text-muted-foreground dark:text-slate-300">
                                    <div
                                        class="w-1 h-1 bg-muted-foreground dark:bg-slate-400 rounded-full mt-2 flex-shrink-0" />
                                    <span>{{ requirement }}</span>
                                </li>
                            </ul>
                        </div>

                        <!-- Activity log -->
                        <div v-if="getStepActivities(step.step).length > 0" class="space-y-2">
                            <h4 class="font-medium text-foreground dark:text-white">Atividade:</h4>
                            <div
                                class="text-xs font-mono text-muted-foreground dark:text-slate-300 bg-muted/30 dark:bg-slate-800/50 px-2 py-0.5 rounded border dark:border-slate-700">
                                <div v-for="activity in getStepActivities(step.step)" :key="activity" class="">
                                    {{ activity }}
                                </div>
                            </div>
                        </div>

                        <!-- Action buttons -->
                        <div class="flex items-center gap-3 pt-2">
                            <!-- Multiple actions -->
                            <div v-if="getStepActions(step).length > 0" class="flex flex-wrap items-center gap-2">
                                <Button v-for="action in getStepActions(step)" :key="action.id"
                                    @click="handleActionClick(step, action)"
                                    :disabled="!isActionAllowed(action, getStepStatus(step.step)) || isTransitioning"
                                    :variant="isActionAllowed(action, getStepStatus(step.step)) ? (action.variant || 'default') : 'outline'"
                                    size="sm" class="text-xs transition-all duration-200" :class="{
                                        'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 dark:border-blue-600': isActionAllowed(action, getStepStatus(step.step)) && (action.variant === 'primary' || !action.variant) && !isTransitioning,
                                        'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed hover:bg-gray-100 dark:bg-slate-700 dark:text-slate-500 dark:border-slate-600 dark:hover:bg-slate-700': !isActionAllowed(action, getStepStatus(step.step)) || isTransitioning,
                                        'bg-white text-blue-600 border-blue-600 hover:bg-blue-50 dark:bg-slate-800 dark:text-blue-400 dark:border-blue-500 dark:hover:bg-slate-700': isActionAllowed(action, getStepStatus(step.step)) && action.variant === 'outline' && !isTransitioning
                                    }">
                                    <Loader2 v-if="isTransitioning && isStepTransitioning(step.step)"
                                        class="w-3 h-3 mr-1 animate-spin" />
                                    <component v-else-if="action.icon" :is="action.icon" class="w-3 h-3 mr-1" />
                                    {{ action.text }}
                                </Button>
                            </div>

                            <!-- Retry action -->
                            <Button v-if="step.allowRetry && getStepStatus(step.step) === 'completed'"
                                @click="handleStepRetry(step.step)" variant="ghost" size="sm"
                                :disabled="isTransitioning"
                                class="text-xs text-muted-foreground hover:text-foreground dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
                                :class="{ 'opacity-50 cursor-not-allowed': isTransitioning }">
                                <RotateCcw class="w-3 h-3 mr-1" />
                                Refazer
                            </Button>
                        </div>
                    </div>
                </AccordionContent>
            </AccordionItem>

        </Accordion>
    </div>
</template>

<style scoped>
.research-stepper {
    @apply w-full max-w-2xl;
}

/* Remove default accordion borders and styling */
.research-stepper :deep(.accordion-item) {
    @apply border-none;
}

.research-stepper :deep(.accordion-trigger) {
    @apply hover:no-underline;
}

/* Ensure smooth chevron rotation */
.research-stepper :deep(.accordion-trigger svg) {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.research-stepper :deep(.accordion-trigger[data-state="open"] svg) {
    transform: rotate(180deg);
}

/* Enhanced step transitions */
.research-stepper :deep([data-step]) {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth timeline connector animations */
.research-stepper .absolute {
    transition: background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>