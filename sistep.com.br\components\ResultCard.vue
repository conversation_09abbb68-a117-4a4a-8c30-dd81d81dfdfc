<template>
  <div class="result-card p-4 border rounded-lg transition-colors" :class="levelClass">
    <div class="flex items-center mb-2">
      <h3 class="text-lg font-medium">{{ label }}</h3>
      <span class="ml-auto font-bold text-2xl">{{ value }}</span>
    </div>
    <p class="text-sm text-muted-foreground">{{ description }}</p>
    <div class="mt-3 h-2 w-full bg-secondary rounded-full overflow-hidden">
      <div class="h-full bg-primary transition-all" :style="{ width: `${(value / maxValue) * 100}%` }" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  label: {
    type: String,
    required: true
  },
  value: {
    type: Number,
    required: true
  },
  maxValue: {
    type: Number,
    default: 21 // Valor máximo para DASS-21 (7 questões * 3 pontos)
  },
  level: {
    type: String,
    default: 'normal',
    validator: (value) => ['normal', 'mild', 'moderate', 'severe', 'extreme'].includes(value)
  },
  description: {
    type: String,
    default: ''
  }
})

const levelClass = computed(() => {
  const classes = {
    normal: 'border-green-100 bg-green-50 dark:bg-green-950 dark:border-green-900',
    mild: 'border-blue-100 bg-blue-50 dark:bg-blue-950 dark:border-blue-900',
    moderate: 'border-yellow-100 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-900',
    severe: 'border-orange-100 bg-orange-50 dark:bg-orange-950 dark:border-orange-900',
    extreme: 'border-red-100 bg-red-50 dark:bg-red-950 dark:border-red-900'
  }
  return classes[props.level] || classes.normal
})
</script> 