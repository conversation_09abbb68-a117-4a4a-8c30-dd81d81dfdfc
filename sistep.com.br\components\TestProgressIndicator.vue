<template>
  <div class="progress-indicator">
    <!-- Auto-save status -->
    <transition name="fade" mode="out-in">
      <div v-if="showSaveStatus" class="save-status flex items-center gap-2 text-sm">
        <Icon v-if="saveStatus === 'saving'" name="loader-2" class="h-4 w-4 animate-spin text-blue-500" />
        <Icon v-else-if="saveStatus === 'saved'" name="check-circle" class="h-4 w-4 text-green-500" />
        <Icon v-else-if="saveStatus === 'error'" name="alert-circle" class="h-4 w-4 text-red-500" />
        
        <span :class="{
          'text-blue-600': saveStatus === 'saving',
          'text-green-600': saveStatus === 'saved',
          'text-red-600': saveStatus === 'error',
          'text-muted-foreground': saveStatus === 'idle'
        }">
          {{ saveStatusText }}
        </span>
      </div>
    </transition>

    <!-- Progress bar -->
    <div class="progress-bar-container mt-2">
      <div class="flex justify-between items-center mb-1">
        <span class="text-sm text-muted-foreground">
          {{ currentQuestion + 1 }} de {{ totalQuestions }} questões
        </span>
        <span class="text-sm font-medium">
          {{ Math.round(progressPercentage) }}%
        </span>
      </div>
      
      <div class="progress-bar bg-muted h-2 rounded-full overflow-hidden">
        <div 
          class="progress-fill h-full bg-primary rounded-full transition-all duration-500 ease-out"
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
      
      <!-- Answered questions indicator -->
      <div class="answered-indicator flex justify-between mt-2 text-xs text-muted-foreground">
        <span>{{ answeredCount }} respondidas</span>
        <span v-if="lastSavedAt">
          Salvo {{ formatLastSaved(lastSavedAt) }}
        </span>
      </div>
    </div>

    <!-- Question navigation dots (optional) -->
    <div v-if="showQuestionDots" class="question-dots flex flex-wrap gap-1 mt-3">
      <button
        v-for="(question, index) in questions"
        :key="index"
        @click="$emit('navigate-to-question', index)"
        :disabled="!canNavigateToQuestion(index)"
        class="question-dot w-3 h-3 rounded-full border transition-all duration-200"
        :class="{
          'bg-primary border-primary': index === currentQuestion,
          'bg-green-500 border-green-500': answers[index] !== undefined && index !== currentQuestion,
          'bg-muted border-muted-foreground/30': answers[index] === undefined && index !== currentQuestion,
          'cursor-pointer hover:scale-110': canNavigateToQuestion(index),
          'cursor-not-allowed opacity-50': !canNavigateToQuestion(index)
        }"
        :title="`Questão ${index + 1}${answers[index] !== undefined ? ' (respondida)' : ''}`"
      ></button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Icon } from '@/components/ui/icon'

const props = defineProps({
  currentQuestion: {
    type: Number,
    default: 0
  },
  totalQuestions: {
    type: Number,
    default: 0
  },
  answers: {
    type: Object,
    default: () => ({})
  },
  questions: {
    type: Array,
    default: () => []
  },
  lastSavedAt: {
    type: String,
    default: null
  },
  saveStatus: {
    type: String,
    default: 'idle', // 'idle', 'saving', 'saved', 'error'
    validator: (value) => ['idle', 'saving', 'saved', 'error'].includes(value)
  },
  showQuestionDots: {
    type: Boolean,
    default: false
  },
  canNavigateToQuestion: {
    type: Function,
    default: () => () => true
  }
})

const emit = defineEmits(['navigate-to-question'])

// Estado local
const showSaveStatus = ref(false)

// Computed properties
const progressPercentage = computed(() => {
  if (props.totalQuestions === 0) return 0
  return ((props.currentQuestion + 1) / props.totalQuestions) * 100
})

const answeredCount = computed(() => {
  return Object.keys(props.answers).length
})

const saveStatusText = computed(() => {
  switch (props.saveStatus) {
    case 'saving':
      return 'Salvando...'
    case 'saved':
      return 'Salvo automaticamente'
    case 'error':
      return 'Erro ao salvar'
    default:
      return ''
  }
})

// Watchers
watch(() => props.saveStatus, (newStatus) => {
  if (newStatus !== 'idle') {
    showSaveStatus.value = true
    
    // Auto-hide after delay for 'saved' status
    if (newStatus === 'saved') {
      setTimeout(() => {
        showSaveStatus.value = false
      }, 3000)
    }
  } else {
    showSaveStatus.value = false
  }
})

/**
 * Formata o timestamp da última salvamento
 */
function formatLastSaved(timestamp) {
  if (!timestamp) return 'nunca'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now - date
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'agora'
  if (diffMins < 60) return `${diffMins}min atrás`
  
  const diffHours = Math.floor(diffMins / 60)
  if (diffHours < 24) return `${diffHours}h atrás`
  
  return date.toLocaleDateString('pt-BR')
}
</script>

<style scoped>
.progress-indicator {
  @apply w-full;
}

.progress-bar {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

.progress-fill {
  background: linear-gradient(90deg, 
    hsl(var(--primary)) 0%, 
    hsl(var(--primary) / 0.8) 100%
  );
}

.question-dot {
  transition: all 0.2s ease;
}

.question-dot:hover:not(:disabled) {
  transform: scale(1.2);
}

.question-dot:disabled {
  cursor: not-allowed;
}

/* Transições */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Animação para o indicador de salvamento */
@keyframes pulse-save {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(0.95);
  }
}

.save-status {
  animation: pulse-save 2s ease-in-out;
}

/* Responsividade */
@media (max-width: 640px) {
  .question-dots {
    max-height: 60px;
    overflow-y: auto;
  }
  
  .question-dot {
    @apply w-2 h-2;
  }
}
</style>
