<script setup>
import { reactiveOmit } from "@vueuse/core";
import { AccordionContent } from "reka-ui";
import { cn } from "@/lib/utils";
import { ref, nextTick, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  forceMount: { type: Boolean, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: [String, Object, Function], required: false },
  class: { type: null, required: false },
});

const delegatedProps = reactiveOmit(props, "class");

// Refs for transition control
const contentElement = ref(null);
const accordionContentRef = ref(null);
const isTransitioning = ref(false);
const isOpen = ref(false);

// Observer to watch for data-state changes
let observer = null;

// JavaScript hooks for smooth height transitions
const beforeEnter = (el) => {
  el.style.height = '0px';
  el.style.opacity = '0';
  el.style.overflow = 'hidden';
  isTransitioning.value = true;
};

const enter = async (el, done) => {
  await nextTick();
  
  // Get the natural height by temporarily showing the content
  el.style.height = 'auto';
  const targetHeight = el.scrollHeight;
  
  // Reset to 0 and animate to target height
  el.style.height = '0px';
  
  // Force reflow
  el.offsetHeight;
  
  // Start animation
  requestAnimationFrame(() => {
    el.style.transition = 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out';
    el.style.height = `${targetHeight}px`;
    el.style.opacity = '1';
    
    // Clean up after animation
    setTimeout(() => {
      if (el.style.height !== '0px') { // Only cleanup if still open
        el.style.height = 'auto';
        el.style.overflow = 'visible';
      }
      isTransitioning.value = false;
      done();
    }, 300);
  });
};

const beforeLeave = (el) => {
  el.style.height = `${el.scrollHeight}px`;
  el.style.overflow = 'hidden';
  isTransitioning.value = true;
  
  // Force reflow
  el.offsetHeight;
};

const leave = (el, done) => {
  requestAnimationFrame(() => {
    el.style.transition = 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out';
    el.style.height = '0px';
    el.style.opacity = '0';
    
    setTimeout(() => {
      isTransitioning.value = false;
      done();
    }, 300);
  });
};

const afterEnter = (el) => {
  el.style.transition = '';
  el.style.overflow = 'visible';
};

const afterLeave = (el) => {
  el.style.transition = '';
  el.style.height = '';
  el.style.opacity = '';
  el.style.overflow = '';
};

// Watch for data-state changes on the AccordionContent element
const checkAccordionState = () => {
  // Try multiple ways to get the DOM element
  let element = null;
  
  if (accordionContentRef.value?.$el) {
    element = accordionContentRef.value.$el;
  } else if (accordionContentRef.value?.querySelector) {
    element = accordionContentRef.value;
  } else if (accordionContentRef.value) {
    // If it's already a DOM element
    element = accordionContentRef.value;
  }
  
  if (!element || typeof element.getAttribute !== 'function') return;
  
  const dataState = element.getAttribute('data-state');
  const newIsOpen = dataState === 'open';
  
  if (newIsOpen !== isOpen.value) {
    isOpen.value = newIsOpen;
  }
};

onMounted(async () => {
  await nextTick();
  
  // Try multiple ways to get the DOM element
  let element = null;
  
  if (accordionContentRef.value?.$el) {
    element = accordionContentRef.value.$el;
  } else if (accordionContentRef.value?.querySelector) {
    element = accordionContentRef.value;
  } else if (accordionContentRef.value) {
    element = accordionContentRef.value;
  }
  
  if (element && typeof element.getAttribute === 'function') {
    // Initial state check
    checkAccordionState();
    
    // Set up mutation observer to watch for data-state changes
    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-state') {
          checkAccordionState();
        }
      });
    });
    
    observer.observe(element, {
      attributes: true,
      attributeFilter: ['data-state']
    });
  } else {
    console.warn('AccordionContent: Could not find DOM element for state observation');
  }
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
    observer = null;
  }
});
</script>

<template>
  <AccordionContent
    ref="accordionContentRef"
    data-slot="accordion-content"
    v-bind="delegatedProps"
    :force-mount="true"
    :class="cn('overflow-hidden', props.class)"
  >
    <Transition
      @before-enter="beforeEnter"
      @enter="enter"
      @before-leave="beforeLeave"
      @leave="leave"
      @after-enter="afterEnter"
      @after-leave="afterLeave"
      :css="false"
    >
      <div 
        v-show="isOpen"
        ref="contentElement"
        class="accordion-content-wrapper"
      >
        <div class="pt-0 pb-4">
          <slot />
        </div>
      </div>
    </Transition>
  </AccordionContent>
</template>

<style scoped>
.accordion-content-wrapper {
  transform-origin: top;
}

/* Fallback CSS for browsers that don't support smooth transitions */
@media (prefers-reduced-motion: reduce) {
  .accordion-content-wrapper {
    transition: none !important;
  }
}
</style>
