<template>
  <component
    :is="icon"
    :class="className"
    :size="size"
  />
</template>

<script setup>
import * as LucideIcons from 'lucide-vue-next'
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  className: {
    type: String,
    default: '',
  },
  size: {
    type: [String, Number],
    default: 16,
  },
})

// Convert kebab-case to PascalCase for Lucide icon names
const icon = computed(() => {
  const iconName = props.name
    .split('-')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join('')
  
  return LucideIcons[iconName] || LucideIcons.HelpCircle
})
</script>

<style scoped>
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 1em;
  width: 1em;
  line-height: 1;
}
</style> 