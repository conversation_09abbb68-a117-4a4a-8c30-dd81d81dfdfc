<template>
  <div
    class="relative w-full overflow-hidden rounded-full bg-secondary h-2"
    role="progressbar"
    :aria-valuenow="value"
    aria-valuemin="0"
    aria-valuemax="100"
  >
    <div
      class="h-full w-full flex-1 bg-primary transition-all"
      :style="{ transform: `translateX(-${100 - (value || 0)}%)` }"
    />
  </div>
</template>

<script setup>
defineProps({
  value: {
    type: Number,
    default: 0
  }
})
</script> 