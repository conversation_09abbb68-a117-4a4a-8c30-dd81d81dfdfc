<script setup>
import { provide, ref, computed, onMounted, watch } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: ''
  },
  type: {
    type: String,
    default: 'single',
    validator: (value) => ['single', 'multiple'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  variant: {
    type: String,
    default: 'default'
  },
  size: {
    type: String,
    default: 'default'
  },
  class: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const toggleGroupRef = ref(null)
const itemRefs = ref([])

const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

provide('toggleGroup', {
  type: computed(() => props.type),
  size: computed(() => props.size),
  variant: computed(() => props.variant),
  disabled: computed(() => props.disabled),
  value,
  registerItem: (ref) => {
    itemRefs.value.push(ref)
  }
})

const variantStyles = {
  default: 'bg-background border border-input rounded-md p-1',
  outline: 'border border-input rounded-md p-1',
}

const toggleGroupClasses = computed(() => {
  return cn(
    'inline-flex',
    variantStyles[props.variant] || '',
    props.class
  )
})
</script>

<template>
  <div
    ref="toggleGroupRef"
    :class="toggleGroupClasses"
    role="group"
  >
    <slot />
  </div>
</template> 