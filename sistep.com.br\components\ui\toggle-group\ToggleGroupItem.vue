<script setup>
import { inject, ref, computed, onMounted } from 'vue'
import { cn } from '@/lib/utils'
import { cva } from 'class-variance-authority'

const props = defineProps({
  value: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  class: {
    type: String,
    default: ''
  }
})

const toggleGroup = inject('toggleGroup')
const itemRef = ref(null)

onMounted(() => {
  if (toggleGroup && itemRef.value) {
    toggleGroup.registerItem(itemRef.value)
  }
})

const toggleGroupItemVariants = cva(
  'inline-flex items-center justify-center text-sm font-medium transition-colors data-[state=on]:bg-accent data-[state=on]:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 ring-offset-background hover:bg-muted hover:text-muted-foreground',
  {
    variants: {
      variant: {
        default: '',
        outline: 'bg-transparent'
      },
      size: {
        default: 'h-10 px-3',
        sm: 'h-8 px-2',
        lg: 'h-12 px-4'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)

const isSelected = computed(() => {
  if (toggleGroup.type.value === 'single') {
    return toggleGroup.value.value === props.value
  } else {
    return Array.isArray(toggleGroup.value.value) && toggleGroup.value.value.includes(props.value)
  }
})

const isDisabled = computed(() => props.disabled || toggleGroup.disabled.value)

const handleClick = () => {
  if (isDisabled.value) return

  if (toggleGroup.type.value === 'single') {
    toggleGroup.value.value = props.value
  } else {
    const currentValue = Array.isArray(toggleGroup.value.value) ? [...toggleGroup.value.value] : []
    if (currentValue.includes(props.value)) {
      toggleGroup.value.value = currentValue.filter(v => v !== props.value)
    } else {
      toggleGroup.value.value = [...currentValue, props.value]
    }
  }
}

const toggleItemClasses = computed(() => {
  return cn(
    toggleGroupItemVariants({
      variant: toggleGroup.variant.value,
      size: toggleGroup.size.value
    }),
    props.class
  )
})
</script>

<template>
  <button
    ref="itemRef"
    type="button"
    role="tab"
    :aria-selected="isSelected"
    :data-state="isSelected ? 'on' : 'off'"
    :disabled="isDisabled"
    :class="toggleItemClasses"
    @click="handleClick"
  >
    <slot />
  </button>
</template> 