import { ref, computed } from 'vue'
import { useTestNavigation } from './useTestNavigation.js'
import { getTestProgressStats, clearTestData, exportTestData } from '~/utils/testPersistence.js'

/**
 * Composable específico para o teste DASS-21
 * Utiliza o sistema genérico de navegação de testes
 */
export function useDASS21() {
  const testType = 'DASS-21'
  
  // Questões do DASS-21
  const questions = ref([
    { text: 'Eu achei difícil acalmar-me.', category: 'stress' },
    { text: 'Minha boca ficou seca.', category: 'anxiety' },
    { text: 'Eu não consegui sentir nenhuma emoção positiva.', category: 'depression' },
    { text: 'Eu tive dificuldade para respirar (por exemplo, respiração excessivamente rápida, falta de ar na ausência de esforço físico).', category: 'anxiety' },
    { text: 'Eu achei difícil ter iniciativa para fazer as coisas.', category: 'depression' },
    { text: 'Eu reagi exageradamente a situações.', category: 'stress' },
    { text: 'Eu senti tremores (por exemplo, nas mãos).', category: 'anxiety' },
    { text: 'Eu senti que estava gastando muita energia.', category: 'stress' },
    { text: 'Eu fiquei preocupado com situações em que eu poderia entrar em pânico e parecer ridículo.', category: 'anxiety' },
    { text: 'Eu senti que não tinha nada para esperar.', category: 'depression' },
    { text: 'Eu achei que estava ficando impaciente.', category: 'stress' },
    { text: 'Eu senti que estava prestes a entrar em pânico.', category: 'anxiety' },
    { text: 'Eu não consegui me entusiasmar com nada.', category: 'depression' },
    { text: 'Eu senti que estava valendo muito pouco como pessoa.', category: 'depression' },
    { text: 'Eu senti que estava sendo intolerante com tudo.', category: 'stress' },
    { text: 'Eu senti que meu coração estava acelerado, mesmo sem esforço físico.', category: 'anxiety' },
    { text: 'Eu senti medo sem motivo.', category: 'anxiety' },
    { text: 'Eu senti que a vida não tinha sentido.', category: 'depression' },
    { text: 'Eu senti que estava inquieto.', category: 'stress' },
    { text: 'Eu senti que estava com medo.', category: 'anxiety' },
    { text: 'Eu senti que estava muito nervoso.', category: 'stress' }
  ])
  
  // Opções de resposta
  const answerOptions = ref(['Não', 'Um pouco', 'Bastante', 'O tempo todo'])
  
  // Usar o sistema de navegação genérico
  const navigation = useTestNavigation(testType, questions.value)
  
  // Estado específico do DASS-21
  const results = ref({ depression: 0, anxiety: 0, stress: 0 })
  const isPaused = ref(false)
  
  // Computed properties específicos
  const progressStats = computed(() => getTestProgressStats(testType, questions.value.length))
  
  const resultsData = computed(() => [
    { label: 'Depressão', value: results.value.depression },
    { label: 'Ansiedade', value: results.value.anxiety },
    { label: 'Estresse', value: results.value.stress }
  ])
  
  /**
   * Calcula os resultados do teste baseado nas respostas
   */
  function calculateResults() {
    const scores = { depression: 0, anxiety: 0, stress: 0 }
    
    Object.entries(navigation.answers.value).forEach(([index, answer]) => {
      const question = questions.value[parseInt(index)]
      if (question && typeof answer === 'string') {
        scores[question.category] += parseInt(answer)
      }
    })
    
    results.value = scores
    return scores
  }
  
  /**
   * Obtém o nível de resultado para uma categoria
   */
  function getResultLevel(category, value) {
    const thresholds = {
      depression: { normal: 4, mild: 6, moderate: 10, severe: 13 },
      anxiety: { normal: 3, mild: 5, moderate: 7, severe: 10 },
      stress: { normal: 7, mild: 9, moderate: 12, severe: 16 }
    }
    
    const categoryKey = category.toLowerCase()
    const categoryThresholds = thresholds[categoryKey] || thresholds.depression
    
    if (value <= categoryThresholds.normal) return 'normal'
    if (value <= categoryThresholds.mild) return 'mild'
    if (value <= categoryThresholds.moderate) return 'moderate'
    if (value <= categoryThresholds.severe) return 'severe'
    return 'extreme'
  }
  
  /**
   * Obtém a descrição do resultado
   */
  function getResultDescription(category, value) {
    const level = getResultLevel(category, value)
    const descriptions = {
      depression: {
        normal: 'Nível normal de sintomas depressivos.',
        mild: 'Sintomas depressivos leves.',
        moderate: 'Sintomas depressivos moderados.',
        severe: 'Sintomas depressivos significativos.',
        extreme: 'Sintomas depressivos extremamente elevados.'
      },
      anxiety: {
        normal: 'Nível normal de sintomas de ansiedade.',
        mild: 'Sintomas leves de ansiedade.',
        moderate: 'Sintomas moderados de ansiedade.',
        severe: 'Sintomas significativos de ansiedade.',
        extreme: 'Sintomas de ansiedade extremamente elevados.'
      },
      stress: {
        normal: 'Nível normal de estresse.',
        mild: 'Nível leve de estresse.',
        moderate: 'Nível moderado de estresse.',
        severe: 'Nível significativo de estresse.',
        extreme: 'Nível extremamente elevado de estresse.'
      }
    }
    
    const categoryKey = category.toLowerCase()
    return descriptions[categoryKey]?.[level] || ''
  }
  
  /**
   * Obtém a cor para o nível de resultado
   */
  function getLevelColor(level) {
    const colorMap = {
      normal: '#16a34a',   // verde
      mild: '#f59e0b',     // amarelo
      moderate: '#d97706', // âmbar
      severe: '#dc2626',   // vermelho
      extreme: '#991b1b'   // vermelho escuro
    }
    
    return colorMap[level] || colorMap.normal
  }
  
  /**
   * Calcula a porcentagem para a barra de progresso do resultado
   */
  function getLevelPercentage(category, value) {
    const maxValues = {
      'depressão': 21,
      'ansiedade': 21,
      'estresse': 21
    }
    
    const max = maxValues[category.toLowerCase()] || 21
    return Math.min(100, (value / max) * 100)
  }
  
  /**
   * Mapeamento de níveis para cores CSS
   */
  const levelColorMap = {
    normal: 'success',
    mild: 'warning',
    moderate: 'amber',
    severe: 'destructive',
    extreme: 'destructive'
  }
  
  /**
   * Pausa ou retoma o teste
   */
  function togglePause() {
    isPaused.value = !isPaused.value
  }
  
  /**
   * Reinicia o teste completamente
   */
  function restartTest() {
    clearTestData(testType)
    navigation.clearProgress()
    results.value = { depression: 0, anxiety: 0, stress: 0 }
    isPaused.value = false
    navigation.navigateToIntro()
  }
  
  /**
   * Compartilha os resultados
   */
  async function shareResults() {
    const resultText = `Meus resultados do teste DASS-21: Depressão: ${results.value.depression}, Ansiedade: ${results.value.anxiety}, Estresse: ${results.value.stress}`
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Resultados do teste DASS-21',
          text: resultText,
        })
      } else {
        // Fallback para copiar para a área de transferência
        await navigator.clipboard.writeText(resultText)
        // Aqui você pode mostrar uma notificação de sucesso
        console.log('Resultados copiados para a área de transferência!')
      }
    } catch (error) {
      console.error('Erro ao compartilhar os resultados:', error)
    }
  }
  
  /**
   * Exporta todos os dados do teste
   */
  function exportData() {
    return exportTestData(testType)
  }
  
  /**
   * Verifica se o teste está completo
   */
  function isTestComplete() {
    return navigation.answeredQuestionsCount.value >= questions.value.length
  }
  
  return {
    // Dados do teste
    testType,
    questions,
    answerOptions,
    results,
    resultsData,
    isPaused,
    
    // Sistema de navegação
    ...navigation,
    
    // Estatísticas
    progressStats,
    
    // Métodos específicos do DASS-21
    calculateResults,
    getResultLevel,
    getResultDescription,
    getLevelColor,
    getLevelPercentage,
    levelColorMap,
    
    // Controles do teste
    togglePause,
    restartTest,
    shareResults,
    exportData,
    isTestComplete
  }
}
