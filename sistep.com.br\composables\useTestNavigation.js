import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { saveTestProgress, getTestProgress, saveTestAnswer, getTestAnswer, hasTestProgress } from '~/utils/testPersistence.js'

/**
 * Composable para navegação e persistência de testes psicológicos
 * Funciona de forma genérica para qualquer tipo de teste
 */
export function useTestNavigation(testType, questions = []) {
  const router = useRouter()
  const route = useRoute()
  
  // Estado reativo
  const currentQuestionIndex = ref(0)
  const selectedAnswer = ref(null)
  const answers = ref({})
  const isLoading = ref(false)
  const autoSaveEnabled = ref(true)
  const lastSavedAt = ref(null)
  
  // Computed properties
  const totalQuestions = computed(() => questions.length)
  const currentQuestion = computed(() => questions[currentQuestionIndex.value])
  const isFirstQuestion = computed(() => currentQuestionIndex.value === 0)
  const isLastQuestion = computed(() => currentQuestionIndex.value >= totalQuestions.value - 1)
  const progressPercentage = computed(() => {
    if (totalQuestions.value === 0) return 0
    return Math.round(((currentQuestionIndex.value + 1) / totalQuestions.value) * 100)
  })
  
  const answeredQuestionsCount = computed(() => Object.keys(answers.value).length)
  const canNavigateToQuestion = computed(() => (questionIndex) => {
    // Permite navegar para questões já respondidas ou a próxima questão não respondida
    return questionIndex <= Math.max(answeredQuestionsCount.value, currentQuestionIndex.value)
  })
  
  /**
   * Inicializa o sistema de navegação
   */
  async function initialize() {
    isLoading.value = true
    
    try {
      // Carregar progresso salvo
      await loadSavedProgress()
      
      // Sincronizar com a URL atual
      syncWithRoute()
      
      // Configurar watchers
      setupWatchers()
      
    } catch (error) {
      console.error('Erro ao inicializar navegação do teste:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * Carrega o progresso salvo do localStorage
   */
  async function loadSavedProgress() {
    const savedProgress = getTestProgress(testType)
    const savedAnswers = {}
    
    // Carregar respostas salvas
    for (let i = 0; i < totalQuestions.value; i++) {
      const savedAnswer = getTestAnswer(testType, i)
      if (savedAnswer !== null) {
        savedAnswers[i] = savedAnswer
      }
    }
    
    answers.value = savedAnswers
    
    if (savedProgress) {
      currentQuestionIndex.value = savedProgress.currentQuestion || 0
      lastSavedAt.value = savedProgress.lastUpdated
    }
  }
  
  /**
   * Sincroniza o estado com a rota atual
   */
  function syncWithRoute() {
    const questionParam = route.params.id
    if (questionParam) {
      const questionNumber = parseInt(questionParam)
      if (questionNumber >= 1 && questionNumber <= totalQuestions.value) {
        const questionIndex = questionNumber - 1
        if (canNavigateToQuestion.value(questionIndex)) {
          currentQuestionIndex.value = questionIndex
          selectedAnswer.value = answers.value[questionIndex] || null
        } else {
          // Redirecionar para a questão apropriada
          navigateToQuestion(Math.min(answeredQuestionsCount.value, totalQuestions.value - 1))
        }
      }
    }
  }
  
  /**
   * Configura watchers para auto-save e sincronização
   */
  function setupWatchers() {
    // Watch para mudanças na questão atual
    watch(currentQuestionIndex, (newIndex) => {
      selectedAnswer.value = answers.value[newIndex] || null
      saveProgress()
    })
    
    // Watch para auto-save de respostas
    watch(selectedAnswer, (newAnswer) => {
      if (newAnswer !== null && autoSaveEnabled.value) {
        saveAnswer(currentQuestionIndex.value, newAnswer)
      }
    })
  }
  
  /**
   * Navega para uma questão específica
   */
  async function navigateToQuestion(questionIndex) {
    if (questionIndex < 0 || questionIndex >= totalQuestions.value) {
      console.warn('Índice de questão inválido:', questionIndex)
      return false
    }
    
    if (!canNavigateToQuestion.value(questionIndex)) {
      console.warn('Navegação não permitida para questão:', questionIndex + 1)
      return false
    }
    
    const questionNumber = questionIndex + 1
    await router.push(`/${testType}/apply/question/${questionNumber}`)
    return true
  }
  
  /**
   * Navega para a próxima questão
   */
  async function nextQuestion() {
    if (isLastQuestion.value) {
      return navigateToResults()
    }
    
    const nextIndex = currentQuestionIndex.value + 1
    return navigateToQuestion(nextIndex)
  }
  
  /**
   * Navega para a questão anterior
   */
  async function previousQuestion() {
    if (isFirstQuestion.value) return false
    
    const prevIndex = currentQuestionIndex.value - 1
    return navigateToQuestion(prevIndex)
  }
  
  /**
   * Navega para a página de resultados
   */
  async function navigateToResults() {
    await router.push(`/${testType}/apply/results`)
    return true
  }
  
  /**
   * Navega para a introdução do teste
   */
  async function navigateToIntro() {
    await router.push(`/${testType}/apply`)
    return true
  }
  
  /**
   * Salva uma resposta
   */
  function saveAnswer(questionIndex, answer) {
    if (questionIndex < 0 || questionIndex >= totalQuestions.value) return false
    
    answers.value[questionIndex] = answer
    
    const questionData = questions[questionIndex]
    const success = saveTestAnswer(testType, questionIndex, answer, questionData)
    
    if (success) {
      lastSavedAt.value = new Date().toISOString()
      saveProgress()
    }
    
    return success
  }
  
  /**
   * Salva o progresso atual
   */
  function saveProgress() {
    const progressData = {
      currentQuestion: currentQuestionIndex.value,
      totalQuestions: totalQuestions.value,
      answeredCount: answeredQuestionsCount.value,
      progressPercentage: progressPercentage.value
    }
    
    saveTestProgress(testType, progressData)
  }
  
  /**
   * Responde a questão atual e avança
   */
  async function answerCurrentQuestion(answer) {
    if (answer === null || answer === undefined) return false
    
    selectedAnswer.value = answer
    const success = saveAnswer(currentQuestionIndex.value, answer)
    
    if (success) {
      // Pequeno delay para feedback visual
      await new Promise(resolve => setTimeout(resolve, 300))
      return nextQuestion()
    }
    
    return false
  }
  
  /**
   * Verifica se o teste pode ser finalizado
   */
  function canFinishTest() {
    return answeredQuestionsCount.value >= totalQuestions.value
  }
  
  /**
   * Verifica se existe progresso salvo
   */
  function hasSavedProgress() {
    return hasTestProgress(testType)
  }
  
  /**
   * Limpa todo o progresso salvo
   */
  function clearProgress() {
    answers.value = {}
    currentQuestionIndex.value = 0
    selectedAnswer.value = null
    lastSavedAt.value = null
    
    // Limpar do localStorage será feito pelo componente pai se necessário
  }
  
  return {
    // Estado
    currentQuestionIndex,
    selectedAnswer,
    answers,
    isLoading,
    autoSaveEnabled,
    lastSavedAt,
    
    // Computed
    totalQuestions,
    currentQuestion,
    isFirstQuestion,
    isLastQuestion,
    progressPercentage,
    answeredQuestionsCount,
    canNavigateToQuestion,
    
    // Métodos de navegação
    initialize,
    navigateToQuestion,
    nextQuestion,
    previousQuestion,
    navigateToResults,
    navigateToIntro,
    
    // Métodos de resposta
    answerCurrentQuestion,
    saveAnswer,
    
    // Métodos utilitários
    canFinishTest,
    hasSavedProgress,
    clearProgress,
    loadSavedProgress,
    saveProgress
  }
}
