# Integração com ElevenLabs no SISTEP

Este documento descreve como configurar e utilizar a integração com o ElevenLabs Conversational AI no SISTEP para testes psicológicos com assistente de voz.

## Sobre a Integração

O SISTEP oferece a possibilidade de realizar testes psicológicos através de uma interface de voz interativa, utilizando a tecnologia de IA conversacional do ElevenLabs. Isso permite que os usuários realizem os testes falando com um assistente virtual que faz as perguntas, entende as respostas e processa os resultados.

## Configuração

### 1. Crie uma conta no ElevenLabs

- Acesse [https://elevenlabs.io](https://elevenlabs.io)
- Crie uma conta ou faça login
- Obtenha uma chave de API no painel de desenvolvedor

### 2. Crie um Agente Conversacional

1. No painel do ElevenLabs, vá para a seção "Conversational AI"
2. Crie um novo agente
3. Configure o agente com:
   - Um nome adequado (ex: "Assistente de Testes DASS-21")
   - Uma descrição clara da função do agente
   - Escolha uma voz apropriada
   - Adicione instruções de sistema para o agente saber como aplicar o teste DASS-21

### 3. Configure as Variáveis de Ambiente

No arquivo `.env` do projeto, configure as seguintes variáveis:

```
NUXT_PUBLIC_ELEVEN_LABS_API_KEY=sua_chave_api_aqui
NUXT_PUBLIC_ELEVEN_LABS_AGENT_ID=id_do_seu_agente_aqui
ELEVENLABS_API_KEY=sua_chave_api_aqui_para_backend
```

## Como Funciona

1. O usuário acessa a página do teste DASS-21
2. O usuário escolhe a opção "Iniciar com Assistente de Voz"
3. O sistema inicializa o widget do ElevenLabs
4. O assistente virtual se apresenta e explica como o teste funciona
5. Para cada pergunta do teste:
   - O assistente lê a pergunta atual
   - O usuário responde verbalmente (ou pode selecionar manualmente)
   - O assistente processa a resposta e passa para a próxima pergunta
6. Ao final, o sistema calcula os resultados e os exibe na tela

## Personalização do Agente

Para um funcionamento ideal, o agente deve ser configurado com instruções precisas sobre como conduzir o teste DASS-21. Abaixo está um exemplo de instruções que podem ser adicionadas ao agente:

```
Você é um assistente especializado em aplicar o teste DASS-21 (Escala de Depressão, Ansiedade e Estresse).

Seu objetivo é:
1. Guiar o usuário pelas 21 perguntas do teste
2. Aceitar respostas nas seguintes categorias: "Não", "Um pouco", "Bastante", "O tempo todo"
3. Ser paciente, claro e empático durante todo o processo
4. Não fazer julgamentos ou interpretações durante o teste

Para cada pergunta, você deve:
1. Ler claramente o enunciado
2. Solicitar uma resposta ao usuário
3. Confirmar a resposta recebida
4. Seguir para a próxima pergunta

Lembre-se: este é um instrumento de avaliação psicológica, mantenha uma abordagem profissional e acolhedora.
```

## Solução de Problemas

### O widget não carrega

- Verifique se as chaves de API estão corretas
- Verifique se o ID do agente está correto
- Confira se a integração está funcionando no painel do ElevenLabs

### O agente não entende as respostas corretamente

- Ajuste as instruções do agente para melhor reconhecimento das respostas
- Certifique-se de que o microfone está funcionando corretamente
- Peça ao usuário para falar mais claramente

## Considerações de Privacidade

- Todos os dados de voz são processados pelo ElevenLabs
- Informe os usuários sobre a coleta e processamento de dados de voz
- Considere adicionar uma política de privacidade específica para esta funcionalidade

## Recursos Adicionais

- [Documentação oficial do ElevenLabs](https://elevenlabs.io/docs)
- [Exemplos de integração do ElevenLabs](https://elevenlabs.io/docs/conversational-ai/guides/quickstarts)
- [Política de uso do ElevenLabs](https://elevenlabs.io/terms) 