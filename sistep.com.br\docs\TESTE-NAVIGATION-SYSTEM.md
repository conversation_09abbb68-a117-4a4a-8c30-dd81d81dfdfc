# Sistema de Navegação e Persistência de Testes

Este documento descreve o novo sistema implementado para navegação e persistência automática de testes psicológicos na plataforma SISTEP.

## 🎯 Objetivos

- **Salvamento automático**: Progresso salvo a cada resposta
- **Navegação por URL**: Acesso direto a questões específicas via URL/slug
- **Recuperação de progresso**: Continuar de onde parou automaticamente
- **UX impecável**: Indicadores visuais e feedback em tempo real
- **Escalabilidade**: Sistema genérico para múltiplos tipos de teste

## 🏗️ Arquitetura

### Estrutura de URLs

```
/DASS-21/apply                    # Introdução do teste
/DASS-21/apply/question/1         # Questão específica (1-21)
/DASS-21/apply/question/2         # Questão específica (1-21)
...
/DASS-21/apply/question/21        # Última questão
/DASS-21/apply/results            # Resultados finais
```

### Componentes Principais

#### 1. Utilitários de Persistência (`utils/testPersistence.js`)
- `saveTestProgress()` - Salva progresso geral
- `saveTestAnswer()` - Salva resposta específica
- `getTestProgress()` - Recupera progresso
- `getTestAnswers()` - Recupera respostas
- `clearTestData()` - Limpa dados salvos
- `validateTestData()` - Valida consistência

#### 2. Composable de Navegação (`composables/useTestNavigation.js`)
- Sistema genérico para qualquer tipo de teste
- Gerencia navegação entre questões
- Sincroniza com URLs
- Auto-save automático
- Validação de navegação

#### 3. Composable Específico (`composables/useDASS21.js`)
- Implementação específica para DASS-21
- Utiliza o sistema genérico de navegação
- Cálculo de resultados
- Interpretação de scores

#### 4. Componente de Progresso (`components/TestProgressIndicator.vue`)
- Barra de progresso visual
- Indicador de salvamento automático
- Navegação por pontos (dots)
- Status em tempo real

### Páginas

#### 1. Introdução (`pages/DASS-21/apply.vue`)
- Apresentação do teste
- Detecção de progresso salvo
- Opção de continuar ou recomeçar
- Modal de confirmação

#### 2. Questões (`pages/DASS-21/apply/question/[id].vue`)
- Questão individual com navegação
- Auto-save em tempo real
- Dropdown de navegação
- Botão pausar/continuar
- Indicadores visuais

#### 3. Resultados (`pages/DASS-21/apply/results.vue`)
- Exibição de resultados calculados
- Interpretação dos scores
- Opções de compartilhamento
- Resumo do teste

## 🔄 Fluxo de Funcionamento

### 1. Início do Teste
```mermaid
graph TD
    A[Usuário acessa /DASS-21/apply] --> B{Existe progresso salvo?}
    B -->|Sim| C[Mostra opções: Continuar/Recomeçar]
    B -->|Não| D[Mostra introdução]
    C --> E[Usuário escolhe]
    E -->|Continuar| F[Vai para questão atual]
    E -->|Recomeçar| G[Limpa dados e inicia]
    D --> H[Clica Iniciar]
    F --> I[/DASS-21/apply/question/X]
    G --> I
    H --> I
```

### 2. Navegação entre Questões
```mermaid
graph TD
    A[Questão carregada] --> B[Usuário responde]
    B --> C[Auto-save ativado]
    C --> D[Salva no localStorage]
    D --> E[Atualiza progresso]
    E --> F[Mostra feedback visual]
    F --> G{É última questão?}
    G -->|Não| H[Navega para próxima]
    G -->|Sim| I[Vai para resultados]
    H --> J[/DASS-21/apply/question/X+1]
    I --> K[/DASS-21/apply/results]
```

### 3. Recuperação de Progresso
```mermaid
graph TD
    A[Usuário retorna] --> B[Sistema verifica localStorage]
    B --> C{Dados válidos?}
    C -->|Sim| D[Carrega progresso]
    C -->|Não| E[Inicia novo teste]
    D --> F[Sincroniza estado]
    F --> G[Mostra questão atual]
    E --> H[Mostra introdução]
```

## 💾 Estrutura de Dados

### LocalStorage Keys
```javascript
// Progresso geral
test_progress_DASS-21 = {
  currentQuestion: 5,
  totalQuestions: 21,
  answeredCount: 6,
  progressPercentage: 28,
  lastUpdated: "2024-01-15T10:30:00Z",
  version: "1.0"
}

// Respostas específicas
test_answers_DASS-21 = {
  "0": {
    answer: "1",
    questionData: { text: "...", category: "stress" },
    answeredAt: "2024-01-15T10:25:00Z"
  },
  "1": {
    answer: "2",
    questionData: { text: "...", category: "anxiety" },
    answeredAt: "2024-01-15T10:26:00Z"
  }
}

// Metadados
test_metadata_DASS-21 = {
  isRequestedByProfessional: false,
  professionalCode: null,
  testStartTime: "2024-01-15T10:20:00Z",
  savedAt: "2024-01-15T10:30:00Z"
}
```

## 🎨 Recursos de UX

### Indicadores Visuais
- ✅ **Progresso salvo**: Ícone verde com "Salvo automaticamente"
- 🔄 **Salvando**: Spinner com "Salvando..."
- ❌ **Erro**: Ícone vermelho com "Erro ao salvar"
- 📊 **Barra de progresso**: Animada com percentual
- 🎯 **Navegação por pontos**: Questões respondidas em verde

### Feedback em Tempo Real
- Auto-save a cada resposta
- Animações suaves entre questões
- Confirmações visuais
- Timestamps de última atualização

### Acessibilidade
- Navegação por teclado
- Labels apropriados
- Contraste adequado
- Textos descritivos

## 🔧 Configuração e Uso

### Para Novos Tipos de Teste

1. **Criar composable específico**:
```javascript
// composables/useBDI.js
import { useTestNavigation } from './useTestNavigation.js'

export function useBDI() {
  const testType = 'BDI'
  const questions = ref([...]) // Questões do BDI
  
  const navigation = useTestNavigation(testType, questions.value)
  
  // Lógica específica do BDI
  function calculateBDIResults() { ... }
  
  return {
    ...navigation,
    calculateBDIResults,
    // outros métodos específicos
  }
}
```

2. **Criar páginas**:
```
pages/BDI/apply.vue                    # Introdução
pages/BDI/apply/question/[id].vue      # Questões
pages/BDI/apply/results.vue            # Resultados
```

3. **Usar o composable**:
```vue
<script setup>
import { useBDI } from '~/composables/useBDI.js'

const {
  currentQuestion,
  selectedAnswer,
  navigateToQuestion,
  answerCurrentQuestion
} = useBDI()
</script>
```

## 🚀 Benefícios

### Para Usuários
- ✅ Nunca perdem progresso
- ✅ Podem pausar e continuar depois
- ✅ Navegação intuitiva
- ✅ Feedback visual claro
- ✅ URLs compartilháveis

### Para Desenvolvedores
- ✅ Sistema reutilizável
- ✅ Código organizado
- ✅ Fácil manutenção
- ✅ Testes isolados
- ✅ Escalável para novos testes

### Para a Plataforma
- ✅ Melhor retenção de usuários
- ✅ Dados mais consistentes
- ✅ Menos abandono de testes
- ✅ Analytics detalhados
- ✅ SEO melhorado

## 🔍 Monitoramento

### Métricas Importantes
- Taxa de conclusão de testes
- Tempo médio por questão
- Frequência de pausas/retomadas
- Erros de salvamento
- Navegação entre questões

### Logs e Debug
- Validação de dados salvos
- Detecção de inconsistências
- Backup automático
- Recuperação de erros

## 🛠️ Manutenção

### Limpeza de Dados
- Implementar TTL para dados antigos
- Limpeza automática de testes incompletos
- Migração de versões de dados

### Atualizações
- Versionamento de estruturas de dados
- Migração automática
- Compatibilidade retroativa

## 📝 Próximos Passos

1. **Implementar analytics** de uso
2. **Adicionar backup** em nuvem
3. **Criar dashboard** de progresso
4. **Implementar notificações** push
5. **Adicionar modo offline**
6. **Integrar com backend** para sincronização
