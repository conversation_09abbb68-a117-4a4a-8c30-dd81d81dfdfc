<template>
    <div :class="`text-${testStore.fontSize}`"
        class="text-foreground bg-background bg-gradient-to-br from-blue-50/50 to-indigo-100/50 dark:from-blue-900/5 dark:to-black/95"
        v-cloak>
        <AppHeader :miniLogo="false" :pageTitle="pageTitle" />
        <div class="border border-black/5 rounded-lg flex-1 mx-4">
            <slot />
        </div>
        <AppFooter />
    </div>
</template>

<script setup>
import AppHeader from '@/components/AppHeader.vue';
import AppFooter from '@/components/AppFooter.vue';
import { useTestStore } from '~/stores/test';
import { usePageTitle } from '~/composables/usePageTitle';
import { watch } from 'vue';

const testStore = useTestStore();
const colorMode = useColorMode();
const { pageTitle } = usePageTitle();

// Observar mudanças no modo de cor e aplicar as classes correspondentes
watch(() => colorMode.value, (newColorMode) => {
    if (process.client) {
        if (newColorMode === 'dark') {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light');
        } else {
            document.documentElement.classList.add('light');
            document.documentElement.classList.remove('dark');
        }

        // Atualizar localStorage para manter a consistência
        localStorage.theme = newColorMode;
    }
}, { immediate: true });
</script>