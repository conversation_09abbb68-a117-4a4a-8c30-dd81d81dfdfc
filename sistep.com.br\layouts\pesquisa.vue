<template>
    <div :class="`text-${testStore.fontSize}`"
        class="min-h-dvh relative h-full text-foreground bg-background flex flex-col"
        v-cloak>
        <div class="sticky top-0 z-50 -mb-[calc(60px)]">
            <AppHeader :miniLogo="false" :pageTitle="pageTitle" />
        </div>

        <slot />

        <AppFooter />
    </div>
</template>

<script setup>
import AppHeader from '@/components/AppHeader.vue';
import AppFooter from '@/components/AppFooter.vue';
import { useTestStore } from '~/stores/test';
import { usePageTitle } from '~/composables/usePageTitle';
import { watch } from 'vue';

const testStore = useTestStore();
const colorMode = useColorMode();
const { pageTitle } = usePageTitle();

// Observar mudanças no modo de cor e aplicar as classes correspondentes
watch(() => colorMode.value, (newColorMode) => {
    if (process.client) {
        if (newColorMode === 'dark') {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light');
        } else {
            document.documentElement.classList.add('light');
            document.documentElement.classList.remove('dark');
        }

        // Atualizar localStorage para manter a consistência
        localStorage.theme = newColorMode;
    }
}, { immediate: true });
</script>