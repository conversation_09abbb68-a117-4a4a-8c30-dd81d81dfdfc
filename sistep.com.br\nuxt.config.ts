import { defineNuxtConfig } from "nuxt/config";
import { VitePWA } from 'vite-plugin-pwa';

export default defineNuxtConfig({
  app: {
    head: {
      title: 'SISTEP - Sistema de Testes Psicológicos',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'Sistema de Testes Psicológicos online' },
        { name: 'format-detection', content: 'telephone=no' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        // Adicionando links para as fontes
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: "" },
        { href: 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Lexend:wght@100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap', rel: 'stylesheet' }
      ]
    }
  },
  css: [
    '@/assets/css/tailwind.css',
  ],
  compatibilityDate: '2024-09-11',
  ssr: false,
  devtools: {
    enabled: false,
    timeline: {
      enabled: false
    }
  },
  modules: [
    ["shadcn-nuxt", {
      prefix: '',
      componentDir: './components/ui'
    }], 
    '@pinia/nuxt', 
    '@vite-pwa/nuxt', 
    ['@nuxtjs/color-mode', {
      classSuffix: ''
    }], 
    '@nuxtjs/tailwindcss'
  ],
  sourcemap: {
    server: true,
    client: true
  },
  build: {
    transpile: ['html5-qrcode', 'vite-plugin-pwa'],
  },
  // Configuração de variáveis de ambiente para ElevenLabs
  runtimeConfig: {
    // Variáveis privadas (apenas no servidor)
    elevenlabsApiKey: process.env.ELEVENLABS_API_KEY || '',
    
    // Variáveis públicas (acessíveis no cliente)
    public: {
      ELEVEN_LABS_AGENT_ID: process.env.NUXT_PUBLIC_ELEVEN_LABS_AGENT_ID || '',
      ELEVEN_LABS_API_KEY: process.env.NUXT_PUBLIC_ELEVEN_LABS_API_KEY || '',
      APP_URL: process.env.NUXT_PUBLIC_APP_URL || '',
      BACK_URL: process.env.NUXT_PUBLIC_BACK_URL || '',
      API_URL: process.env.NUXT_PUBLIC_API_URL || ''
    }
  },
  vite: {
    plugins: [
      VitePWA({
        registerType: 'autoUpdate',
        manifest: {
          name: 'SISTEP App',
          short_name: 'SISTEP',
          description: 'Sistema de Testes Psicológicos online',
          theme_color: '#ffffff',
          icons: [
            {
              src: 'icon-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: 'icon-512x512.png',
              sizes: '512x512',
              type: 'image/png',
            },
          ],
        },
      }),
    ],
    optimizeDeps: {
      include: ['devalue']
    }
  }
})