{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build --dotenv .env.production", "dev": "nuxt dev --dotenv .env --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@11labs/client": "^0.1.2", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.12.1", "@pinia/nuxt": "^0.11.0", "@vee-validate/zod": "^4.13.2", "@vite-pwa/nuxt": "^1.0.1", "@vueuse/core": "^13.3.0", "ably": "^2.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "elevenlabs": "^1.56.1", "lucide-vue-next": "^0.511.0", "marked": "^15.0.12", "nuxt": "^3.13.1", "pinia": "^3.0.2", "radix-vue": "^1.9.5", "reka-ui": "^2.3.0", "shadcn-nuxt": "^2.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.2", "uuid": "^11.1.0", "vee-validate": "^4.13.2", "vue": "^3.5.4", "vue-json-pretty": "^2.4.0", "vue-qrcode-reader": "^5.5.8", "vue-router": "^4.4.4", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@vue/reactivity": "^3.5.4", "autoprefixer": "^10.4.20", "postcss": "^8.4.49"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}