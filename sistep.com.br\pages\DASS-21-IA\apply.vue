<template>
  <main class="flex flex-1 flex-col leading-tight select-none p-4 w-full max-w-4xl mx-auto">
    <transition name="fade" mode="out-in">
      <!-- Tela de introdução -->
      <section v-if="currentView === 'intro'" key="intro" class="flex flex-col gap-6 mb-8">
        <header class="text-center mb-6">
          <h1 class="text-2xl font-bold mb-2">Escala de Depressão, Ansiedade e Estresse (DASS-21)</h1>
          <p class="text-muted-foreground">Um instrumento para avaliação de sintomas emocionais com assistente de voz</p>
        </header>

        <div class="info-card p-4 border rounded-lg">
          <h2 class="text-lg font-medium mb-2">Sobre este teste</h2>
          <p>O DASS-21 é um conjunto de três escalas para medir os estados emocionais de depressão, ansiedade e estresse.</p>
          <p class="mt-2">Cada escala contém 7 questões, totalizando 21 itens que você responderá sobre como se sentiu na última semana.</p>
          <p class="mt-2 font-medium">Versão com Assistente de Voz: Você poderá interagir com um assistente de voz inteligente para realizar o teste.</p>
        </div>
        
        <div class="instructions p-4 border rounded-lg">
          <h2 class="text-lg font-medium mb-2">Instruções</h2>
          <ul class="list-disc pl-5 space-y-2">
            <li>Este teste utilizará uma assistente de voz para guiá-lo através das perguntas.</li>
            <li>Você poderá responder falando naturalmente suas respostas.</li>
            <li>O assistente lhe pedirá para escolher entre: "Não", "Um pouco", "Bastante" ou "O tempo todo".</li>
            <li>O teste leva aproximadamente 5-10 minutos para ser concluído.</li>
            <li>Certifique-se de que seu microfone está funcionando e permita o acesso quando solicitado.</li>
          </ul>
        </div>
        
        <Button @click="startTest" class="mt-4" size="lg">Iniciar Teste com Assistente de Voz</Button>
      </section>

      <!-- Assistente de voz -->
      <section v-else-if="currentView === 'assistant'" key="assistant" class="flex flex-col h-full">
        <header class="mb-6">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm text-muted-foreground">Questão {{ currentQuestion + 1 }} de {{ questions.length }}</span>
            <div class="flex items-center gap-2">
              <Button v-if="!isPaused" @click="togglePause" variant="ghost" size="sm" class="flex items-center">
                <Icon name="pause" class="mr-1" /> Pausar
              </Button>
              <Button v-else @click="togglePause" variant="ghost" size="sm" class="flex items-center">
                <Icon name="play" class="mr-1" /> Continuar
              </Button>
            </div>
          </div>
          <Progress :value="(currentQuestion / questions.length) * 100" />
        </header>
        
        <div class="flex-1 flex flex-col">
          <!-- Interface de chat com ElevenLabs -->
          <div class="voice-assistant-container flex-1 border rounded-lg p-4 mb-4 flex flex-col">
            <!-- Área que mostra a última pergunta e a seleção atual -->
            <div v-if="!isPaused" class="mb-4 p-4 border-b">
              <h3 class="text-lg font-medium mb-2">Questão atual:</h3>
              <p class="text-xl">{{ questions[currentQuestion].text }}</p>
              
              <div class="mt-4">
                <p class="text-sm text-muted-foreground mb-2">Sua resposta:</p>
                <div class="flex gap-2 flex-wrap">
                  <Badge 
                    v-for="(option, index) in options" 
                    :key="index" 
                    :variant="selectedOption === String(index) ? 'default' : 'outline'"
                    class="text-base py-1 px-3 cursor-pointer"
                    @click="selectedOption = String(index)"
                  >
                    {{ option }}
                  </Badge>
                </div>
              </div>
            </div>
            
            <!-- Widget Conversacional do ElevenLabs -->
            <div ref="elevenLabsWidgetContainer" class="elevenlabs-widget flex-1">
              <!-- O widget será montado aqui via JavaScript -->
              
              <!-- Área de carregamento enquanto o widget inicializa -->
              <div v-if="!widgetInitialized && !widgetError" class="h-full flex flex-col items-center justify-center p-4">
                <div class="flex flex-col items-center text-center">
                  <div class="loading-spinner mb-4">
                    <Icon name="loader-2" class="h-10 w-10 animate-spin" />
                  </div>
                  <h3 class="text-lg font-medium mb-2">Inicializando assistente de voz</h3>
                  <p class="text-sm text-muted-foreground">Aguarde enquanto conectamos ao serviço de voz...</p>
                </div>
              </div>
              
              <!-- Área de fallback para quando o widget não carregar -->
              <div v-if="widgetError" class="h-full flex flex-col items-center justify-center p-4 bg-muted/20 rounded">
                <div class="flex flex-col items-center text-center max-w-sm">
                  <Icon name="alert-triangle" class="h-10 w-10 text-amber-500 mb-3" />
                  <h3 class="text-xl font-semibold mb-2">Não foi possível carregar o assistente de voz</h3>
                  <p class="mb-4 text-muted-foreground">Houve um problema ao conectar com o serviço de voz. Você ainda pode continuar o teste manualmente.</p>
                  
                  <div class="flex flex-wrap gap-2 mb-4">
                    <Badge 
                      v-for="(option, index) in options" 
                      :key="index" 
                      :variant="selectedOption === String(index) ? 'default' : 'outline'"
                      class="text-base py-1 px-3 cursor-pointer"
                      @click="selectedOption = String(index)"
                    >
                      {{ option }}
                    </Badge>
                  </div>
                  
                  <Button 
                    variant="default" 
                    size="sm"
                    @click="retryWidget" 
                    class="flex items-center"
                  >
                    <Icon name="refresh-ccw" class="h-4 w-4 mr-2" />
                    Tentar novamente
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Controles manuais para navegação -->
          <div class="navigation-buttons flex justify-between mt-4">
            <Button v-if="currentQuestion > 0" @click="goBack" variant="outline" class="flex items-center">
              <Icon name="arrow-left" class="mr-1" /> Voltar
            </Button>
            <div v-else class="invisible"><!-- Placeholder para manter o layout --></div>
            
            <Button 
              v-if="!isPaused" 
              @click="answerQuestion" 
              :disabled="selectedOption === null" 
              class="flex items-center"
            >
              Continuar <Icon name="arrow-right" class="ml-1" />
            </Button>
          </div>
        </div>
      </section>

      <!-- Resultados do teste -->
      <section v-else key="results" class="flex flex-col w-full max-w-3xl mx-auto">
        <header class="text-center mb-8">
          <h1 class="text-2xl font-bold mb-2">Resultados do DASS-21</h1>
          <p class="text-muted-foreground">Seus níveis de depressão, ansiedade e estresse</p>
        </header>
        
        <!-- Legenda única para todos os resultados -->
        <div class="scale-legend flex justify-between mb-4 px-4 text-xs text-muted-foreground">
          <span>Normal</span>
          <span>Leve</span>
          <span>Moderado</span>
          <span>Severo</span>
          <span>Extremo</span>
        </div>
        
        <div class="results-grid space-y-6 mb-8">
          <div v-for="result in resultsData" :key="result.label" 
               class="result-item border p-5 rounded-lg"
               :class="`border-${levelColorMap[getResultLevel(result.label, result.value)]}/50`">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold">{{ result.label }}</h3>
              <span class="text-2xl font-bold">{{ result.value }}</span>
            </div>
            
            <div class="level-indicator mb-5">
              <div class="bg-muted h-3 w-full rounded-full overflow-hidden">
                <div class="h-full rounded-full" 
                     :style="{
                       width: getLevelPercentage(result.label, result.value) + '%',
                       backgroundColor: getLevelColor(getResultLevel(result.label, result.value))
                     }"></div>
              </div>
            </div>
            
            <p class="text-sm">{{ getResultDescription(result.label, result.value) }}</p>
          </div>
        </div>
        
        <div class="interpretation p-5 border rounded-lg mb-8 bg-primary-foreground/30">
          <h2 class="text-lg font-semibold mb-3">Entendendo seus resultados</h2>
          <p class="mb-3">Esta escala fornece uma medida da gravidade dos sintomas de depressão, ansiedade e estresse. Os resultados são baseados em suas respostas sobre como você se sentiu na última semana.</p>
          <p class="text-sm text-muted-foreground italic">Lembre-se que esta é uma ferramenta de auto-avaliação e não substitui o diagnóstico profissional. Se você estiver preocupado com seus resultados, considere consultar um profissional de saúde mental.</p>
        </div>
        
        <!-- Diferenciação entre auto-aplicação e aplicação por profissional -->
        <div v-if="testStore.isRequestedByProfessional" class="professional-feedback p-5 border rounded-lg mb-6 bg-success/10 border-success/30">
          <div class="flex items-center gap-3 mb-3">
            <Icon name="check-circle" class="text-success h-6 w-6" />
            <h3 class="text-lg font-medium">Resultados enviados</h3>
          </div>
          <p class="mb-4">Seus resultados foram enviados para o {{ testStore.professionalName }}. Obrigado por completar este teste.</p>
          <Button @click="goToHome" variant="default" class="w-full md:w-auto">
            <Icon name="home" class="mr-2 h-4 w-4" /> Voltar para Início
          </Button>
        </div>
        
        <div v-else class="action-buttons grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button @click="restartTest" variant="outline" class="flex items-center justify-center">
            <Icon name="arrow-left" class="mr-2" /> Refazer Teste
          </Button>
          <Button @click="shareResults" variant="default" class="flex items-center justify-center">
            <Icon name="share" class="mr-2" /> Compartilhar Resultados
          </Button>
        </div>
      </section>
    </transition>

    <!-- Debug Panel - Visível apenas em modo de desenvolvimento -->
    <section v-if="debugMode && currentView === 'assistant'" class="debug-panel border border-yellow-500 p-4 mt-4 rounded-lg bg-yellow-50 dark:bg-yellow-950">
      <div class="flex justify-between items-center mb-3">
        <h3 class="font-bold text-yellow-800 dark:text-yellow-300">ElevenLabs Debug Panel</h3>
        <Button @click="debugMode = false" size="sm" variant="outline">Ocultar</Button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <h4 class="text-sm font-medium mb-2">Ferramentas de Teste</h4>
          <div class="flex flex-wrap gap-2">
            <Button @click="testSetResponseTool" size="sm" variant="secondary" class="flex items-center">
              <Icon name="zap" class="mr-1 h-4 w-4" /> Testar set_response
            </Button>
            <Button @click="testPreviousQuestionTool" size="sm" variant="secondary" class="flex items-center">
              <Icon name="arrow-left" class="mr-1 h-4 w-4" /> Testar previous_question
            </Button>
            <Button @click="testNextQuestionTool" size="sm" variant="secondary" class="flex items-center">
              <Icon name="arrow-right" class="mr-1 h-4 w-4" /> Testar next_question
            </Button>
            <Button @click="testGotoQuestionTool" size="sm" variant="secondary" class="flex items-center">
              <Icon name="move-horizontal" class="mr-1 h-4 w-4" /> Testar goto_question
            </Button>
            <Button @click="sendToolInstructions" size="sm" variant="default" class="flex items-center">
              <Icon name="info" class="mr-1 h-4 w-4" /> Enviar Instruções para IA
            </Button>
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium mb-2">Estado do Widget</h4>
          <div class="text-xs space-y-1">
            <p><span class="font-medium">Inicializado:</span> {{ widgetInitialized }}</p>
            <p><span class="font-medium">Erro:</span> {{ widgetError }}</p>
            <p><span class="font-medium">Questão Atual:</span> {{ currentQuestion + 1 }}</p>
            <p><span class="font-medium">Opção Selecionada:</span> {{ selectedOption !== null ? options[selectedOption] : 'Nenhuma' }}</p>
          </div>
        </div>
      </div>
      
      <div v-if="clientToolCalls.length > 0">
        <h4 class="text-sm font-medium mb-2">Client Tool Calls ({{ clientToolCalls.length }})</h4>
        <div class="max-h-60 overflow-y-auto text-xs bg-white dark:bg-gray-900 border rounded p-2">
          <div v-for="(call, index) in clientToolCalls" :key="index" class="mb-2 pb-2 border-b last:border-0">
            <p class="font-medium">{{ call.timestamp }} - {{ call.tool }}</p>
            <p><span class="font-medium">Parâmetros:</span> {{ JSON.stringify(call.parameters) }}</p>
            <p><span class="font-medium">Resultado:</span> {{ JSON.stringify(call.result) }}</p>
            <p><span class="font-medium">Questão:</span> {{ call.questionIndex + 1 }}</p>
          </div>
        </div>
      </div>
      <div v-else class="text-sm text-gray-500 italic">
        Nenhuma chamada de Client Tool registrada ainda.
      </div>
    </section>
  </main>
</template>

<script setup>
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useTestStore } from '~/stores/test'
import { Icon } from '@/components/ui/icon'
import { useRouter } from 'vue-router'
import { useRuntimeConfig } from '#app'
import { Conversation } from '@11labs/client'

const router = useRouter()
const testStore = useTestStore()
const config = useRuntimeConfig()

// Configurações do teste
const currentView = ref('intro') // 'intro', 'assistant', 'results'
const isPaused = ref(false)
const elevenLabsWidgetContainer = ref(null)
let elevenLabsWidget = null
const widgetError = ref(false) // Estado de erro do widget
const widgetInitialized = ref(false) // Estado de inicialização do widget
const clientToolCalls = ref([]) // Armazenar chamadas de Client Tools para depuração
const debugMode = ref(process.env.NODE_ENV === 'development') // Habilitar modo de depuração em ambiente de desenvolvimento

// Tentativas de inicialização
const maxRetries = 3
let retryCount = 0

// Variáveis para a integração do ElevenLabs
const ELEVEN_LABS_API_KEY = config.public.ELEVEN_LABS_API_KEY
const ELEVEN_LABS_AGENT_ID = config.public.ELEVEN_LABS_AGENT_ID
const ELEVEN_LABS_WEBSOCKET_URL = config.public.ELEVEN_LABS_WEBSOCKET_URL

// Dados do teste
const questions = ref([
  { text: 'Eu achei difícil acalmar-me.', category: 'stress' },
  { text: 'Minha boca ficou seca.', category: 'anxiety' },
  { text: 'Eu não consegui sentir nenhuma emoção positiva.', category: 'depression' },
  { text: 'Eu tive dificuldade para respirar (por exemplo, respiração excessivamente rápida, falta de ar na ausência de esforço físico).', category: 'anxiety' },
  { text: 'Eu achei difícil ter iniciativa para fazer as coisas.', category: 'depression' },
  { text: 'Eu reagi exageradamente a situações.', category: 'stress' },
  { text: 'Eu senti tremores (por exemplo, nas mãos).', category: 'anxiety' },
  { text: 'Eu senti que estava gastando muita energia.', category: 'stress' },
  { text: 'Eu fiquei preocupado com situações em que eu poderia entrar em pânico e parecer ridículo.', category: 'anxiety' },
  { text: 'Eu senti que não tinha nada para esperar.', category: 'depression' },
  { text: 'Eu achei que estava ficando impaciente.', category: 'stress' },
  { text: 'Eu senti que estava prestes a entrar em pânico.', category: 'anxiety' },
  { text: 'Eu não consegui me entusiasmar com nada.', category: 'depression' },
  { text: 'Eu senti que estava valendo muito pouco como pessoa.', category: 'depression' },
  { text: 'Eu senti que estava sendo intolerante com tudo.', category: 'stress' },
  { text: 'Eu senti que meu coração estava acelerado, mesmo sem esforço físico.', category: 'anxiety' },
  { text: 'Eu senti medo sem motivo.', category: 'anxiety' },
  { text: 'Eu senti que a vida não tinha sentido.', category: 'depression' },
  { text: 'Eu senti que estava inquieto.', category: 'stress' },
  { text: 'Eu senti que estava com medo.', category: 'anxiety' },
  { text: 'Eu senti que estava muito nervoso.', category: 'stress' }
])

const options = ['Não', 'Um pouco', 'Bastante', 'O tempo todo']
const currentQuestion = ref(0)
const selectedOption = ref(null)
const answers = ref([])
const results = ref({ depression: 0, anxiety: 0, stress: 0 })

testStore.initializeNewTest()

definePageMeta({
  layout: 'test'
})

// Watchers e métodos de controle do fluxo
watch(currentQuestion, (newVal) => {
  selectedOption.value = answers.value.find(a => a.questionIndex === newVal)?.answer || null
  // Se o widget estiver inicializado, envie a pergunta atual
  if (widgetInitialized.value && elevenLabsWidget && !isPaused.value) {
    sendCurrentQuestionToAI()
  }
})

watch(selectedOption, (newVal, oldVal) => {
  if (newVal !== null && widgetInitialized.value && elevenLabsWidget) {
    const optionText = options[parseInt(newVal)]
    
    // Apenas notificar o agente sobre a seleção do usuário feita na interface
    // Não emular mais a chamada set_response aqui para evitar duplicidade e race conditions
    console.log('Watcher selectedOption: Notificando agente sobre seleção manual');
    sendMessageToAI(`O usuário selecionou diretamente na interface: "${optionText}" (opção ${newVal})`)
  }
})

function startTest() {
  currentView.value = 'assistant'
  // Inicializa o widget após a mudança de vista para garantir que o DOM está pronto
  console.log('Alterando para a vista assistant')
  
  // Usar um tempo maior para garantir que o DOM seja renderizado completamente
  setTimeout(async () => {
    console.log('Verificando container após mudança de vista:', elevenLabsWidgetContainer.value)
    if (elevenLabsWidgetContainer.value) {
      try {
        await startElevenLabsSession()
      } catch (error) {
        console.error('Erro ao inicializar widget:', error)
        widgetError.value = true
      }
    } else {
      console.error('Container ainda não está disponível após mudança de vista')
      widgetError.value = true
    }
  }, 1000)
}

function togglePause() {
  isPaused.value = !isPaused.value
  if (!isPaused.value && elevenLabsWidget) {
    // Inform the AI the test is resumed using contextual update if available
    // elevenLabsWidget.sendContextualUpdate('O teste foi retomado. Continuarei de onde paramos.');
    // For now, keep using sendMessageToAI if AI reaction is needed
    sendMessageToAI('O teste foi retomado. Continuarei de onde paramos.')
  }
}

function answerQuestion(calledFromClientTool = false) {
  if (selectedOption.value !== null) {
    const existingAnswer = answers.value.find(a => a.questionIndex === currentQuestion.value)
    if (existingAnswer) {
      existingAnswer.answer = selectedOption.value
    } else {
      answers.value.push({ 
        questionIndex: currentQuestion.value, 
        question: questions.value[currentQuestion.value], 
        answer: selectedOption.value 
      })
    }
    
    currentQuestion.value++
    
    if (currentQuestion.value >= questions.value.length) {
      calculateResults()
      currentView.value = 'results'
      // Encerrar a sessão do widget quando o teste terminar
      if (elevenLabsWidget) {
        sendMessageToAI('O teste foi concluído. Obrigado por participar!')
        setTimeout(() => {
          cleanupWidget()
        }, 3000)
      }
    }
    
    selectedOption.value = null
  }
  
  // Informar o agente sobre a mudança apenas se não foi chamado a partir de uma Client Tool
  // para evitar mensagens duplicadas
  if (elevenLabsWidget && !calledFromClientTool) {
    sendMessageToAI('Voltamos para a questão anterior.')
  }
}

function goBack() {
  currentQuestion.value--
  if (elevenLabsWidget) {
    sendMessageToAI('Voltamos para a questão anterior.')
  }
}

function calculateResults() {
  const scores = { depression: 0, anxiety: 0, stress: 0 }
  answers.value.forEach(({ question, answer }) => {
    scores[question.category] += parseInt(answer)
  })
  results.value = scores
  
  // Salvar os resultados no store para eventual envio ao profissional
  testStore.saveTestResults({
    test: 'DASS-21',
    timestamp: new Date().toISOString(),
    scores: scores,
    answers: answers.value.map(a => ({
      question: a.question.text,
      category: a.question.category,
      answer: parseInt(a.answer)
    }))
  })
}

function restartTest() {
  currentView.value = 'intro'
  currentQuestion.value = 0
  selectedOption.value = null
  answers.value = []
  results.value = { depression: 0, anxiety: 0, stress: 0 }
  isPaused.value = false
  cleanupWidget()
}

function shareResults() {
  const resultText = `Meus resultados do teste DASS-21: Depressão: ${results.value.depression}, Ansiedade: ${results.value.anxiety}, Estresse: ${results.value.stress}`
  
  try {
    navigator.share({
      title: 'Resultados do teste DASS-21',
      text: resultText,
    })
  } catch (error) {
    console.error('Compartilhamento não suportado neste navegador:', error)
    // Fallback para copiar para a área de transferência
    navigator.clipboard.writeText(resultText)
      .then(() => alert('Resultados copiados para a área de transferência!'))
      .catch(err => console.error('Erro ao copiar os resultados:', err))
  }
}

function goToHome() {
  router.push('/')
}

// Dados dos resultados
const resultsData = computed(() => [
  { label: 'Depressão', value: results.value.depression },
  { label: 'Ansiedade', value: results.value.anxiety },
  { label: 'Estresse', value: results.value.stress }
])

// Funções auxiliares para interpretação de resultados
function getResultLevel(category, value) {
  // Valores limites para interpretação do DASS-21
  const thresholds = {
    depression: { normal: 4, mild: 6, moderate: 10, severe: 13 },
    anxiety: { normal: 3, mild: 5, moderate: 7, severe: 10 },
    stress: { normal: 7, mild: 9, moderate: 12, severe: 16 }
  }
  
  const categoryKey = category.toLowerCase()
  if (categoryKey === 'Depressão'.toLowerCase()) {
    if (value <= thresholds.depression.normal) return 'normal'
    if (value <= thresholds.depression.mild) return 'mild'
    if (value <= thresholds.depression.moderate) return 'moderate'
    if (value <= thresholds.depression.severe) return 'severe'
    return 'extreme'
  } else if (categoryKey === 'Ansiedade'.toLowerCase()) {
    if (value <= thresholds.anxiety.normal) return 'normal'
    if (value <= thresholds.anxiety.mild) return 'mild'
    if (value <= thresholds.anxiety.moderate) return 'moderate'
    if (value <= thresholds.anxiety.severe) return 'severe'
    return 'extreme'
  } else if (categoryKey === 'Estresse'.toLowerCase()) {
    if (value <= thresholds.stress.normal) return 'normal'
    if (value <= thresholds.stress.mild) return 'mild'
    if (value <= thresholds.stress.moderate) return 'moderate'
    if (value <= thresholds.stress.severe) return 'severe'
    return 'extreme'
  }
  return 'normal'
}

function getResultDescription(category, value) {
  const level = getResultLevel(category, value)
  const descriptions = {
    depression: {
      normal: 'Nível normal de sintomas depressivos.',
      mild: 'Sintomas depressivos leves.',
      moderate: 'Sintomas depressivos moderados.',
      severe: 'Sintomas depressivos significativos.',
      extreme: 'Sintomas depressivos extremamente elevados.'
    },
    anxiety: {
      normal: 'Nível normal de sintomas de ansiedade.',
      mild: 'Sintomas leves de ansiedade.',
      moderate: 'Sintomas moderados de ansiedade.',
      severe: 'Sintomas significativos de ansiedade.',
      extreme: 'Sintomas de ansiedade extremamente elevados.'
    },
    stress: {
      normal: 'Nível normal de estresse.',
      mild: 'Nível leve de estresse.',
      moderate: 'Nível moderado de estresse.',
      severe: 'Nível significativo de estresse.',
      extreme: 'Nível extremamente elevado de estresse.'
    }
  }
  
  const categoryKey = category.toLowerCase()
  if (categoryKey === 'Depressão'.toLowerCase()) {
    return descriptions.depression[level]
  } else if (categoryKey === 'Ansiedade'.toLowerCase()) {
    return descriptions.anxiety[level]
  } else if (categoryKey === 'Estresse'.toLowerCase()) {
    return descriptions.stress[level]
  }
  return ''
}

// Mapeamento de níveis para cores
const levelColorMap = {
  normal: 'success',
  mild: 'warning',
  moderate: 'amber',
  severe: 'destructive',
  extreme: 'destructive'
}

// Função para obter a cor com base no nível
function getLevelColor(level) {
  const colorMap = {
    normal: '#16a34a', // verde
    mild: '#f59e0b',   // amarelo
    moderate: '#d97706', // âmbar
    severe: '#dc2626',   // vermelho
    extreme: '#991b1b'   // vermelho escuro
  }
  
  return colorMap[level] || colorMap.normal
}

// Função para calcular a porcentagem da barra de progresso
function getLevelPercentage(category, value) {
  const categoryKey = category.toLowerCase()
  
  // Valores máximos teóricos para cada categoria
  const maxValues = {
    'depressão': 21,
    'ansiedade': 21,
    'estresse': 21
  }
  
  const max = maxValues[categoryKey] || 21
  return Math.min(100, (value / max) * 100)
}

// Funções de integração com ElevenLabs

// Limpar o widget quando necessário
function cleanupWidget() {
  if (elevenLabsWidget) {
    try {
      elevenLabsWidget.destroy()
    } catch (error) {
      console.error('Erro ao destruir widget:', error)
    }
    elevenLabsWidget = null
  }
  widgetInitialized.value = false
}

// Tentar inicializar o widget novamente
function retryWidget() {
  console.log('Tentando inicializar o widget novamente...')
  widgetError.value = false
  cleanupWidget()
  startElevenLabsSession()
}

// Obter uma URL assinada para a sessão
async function getSignedUrl() {
  console.log('Obtendo URL assinada...')
  
  try {
    // Verificar se temos um endpoint de backend para gerar URL assinada
    // (Recomendado para produção para não expor a API key no cliente)
    if (config.public.ELEVENLABS_BACKEND_URL) {
      console.log('Usando backend para obter URL assinada')
      const backendResponse = await fetch(`${config.public.ELEVENLABS_BACKEND_URL}/api/elevenlabs/signed-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          agentId: ELEVEN_LABS_AGENT_ID
        })
      })
      
      if (!backendResponse.ok) {
        throw new Error(`Erro ao obter URL assinada do backend: ${backendResponse.status}`)
      }
      
      const data = await backendResponse.json()
      console.log('URL assinada obtida com sucesso do backend')
      return data.signedUrl
    }
    
    // Fallback: Obter URL assinada diretamente do ElevenLabs se temos uma chave API
    if (ELEVEN_LABS_API_KEY) {
      console.log('Usando API key para obter URL assinada diretamente')
      const url = `https://api.elevenlabs.io/v1/convai/conversation/get_signed_url?agent_id=${ELEVEN_LABS_AGENT_ID}`
      
      const response = await fetch(url, {
        headers: {
          'xi-api-key': ELEVEN_LABS_API_KEY,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Resposta de erro:', errorText)
        throw new Error(`Falha ao obter URL assinada: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (!data.signed_url) {
        console.error('URL assinada não encontrada na resposta')
        throw new Error('URL assinada não encontrada na resposta')
      }
      
      return data.signed_url
    }
    
    // Se não temos nenhuma forma de autenticação, retornar null para usar autenticação por API key
    console.log('Nenhum método de obtenção de URL assinada disponível, usando autenticação por API key')
    return null
  } catch (error) {
    console.error('Erro ao obter URL assinada:', error)
    
    // Em caso de erro, retornar null para usar autenticação por API key
    console.log('Usando autenticação por API key devido a erro na obtenção da URL assinada')
    return null
  }
}

// Enviar mensagem inicial ao iniciar o widget
function sendWelcomeMessage() {
  const message = `
    Olá! Sou o assistente virtual do teste DASS-21. 
    Vou guiá-lo através de um conjunto de perguntas sobre como você se sentiu na última semana.
    
    Para cada afirmação, você pode responder:
    - Não (0 pontos)
    - Um pouco (1 ponto)
    - Bastante (2 pontos)
    - O tempo todo (3 pontos)
    
    Você pode usar as seguintes ferramentas durante o teste:
    
    1. set_response: Quando o usuário indicar uma resposta, use esta ferramenta com o parâmetro "option" definido para a opção escolhida.
       Exemplos:
       - set_response(option="0") ou set_response(option="Não") para registrar a resposta "Não"
       - set_response(option="1") ou set_response(option="Um pouco") para registrar "Um pouco"
       - set_response(option="2") ou set_response(option="Bastante") para registrar "Bastante"
       - set_response(option="3") ou set_response(option="O tempo todo") para registrar "O tempo todo"
    
    2. previous_question: Use esta ferramenta para voltar à questão anterior.
       Exemplo: previous_question()
    
    3. next_question: Use esta ferramenta para avançar para a próxima questão (requer uma resposta selecionada).
       Exemplo: next_question()
    
    4. goto_question: Use esta ferramenta com o parâmetro "question_number" para ir diretamente a uma questão específica.
       Exemplo: goto_question(question_number="5") para ir à quinta questão
    
    Importante: Quando o usuário informar uma resposta, você DEVE usar a ferramenta set_response com o parâmetro option. Por exemplo, se o usuário responder "Um pouco", use set_response(option="1") ou set_response(option="Um pouco").
    
    Vamos começar com a primeira pergunta!
  `
  sendMessageToAI(message)
  
  // Depois de uma pausa para o usuário ler/ouvir a introdução, envie a primeira pergunta
  setTimeout(() => {
    sendCurrentQuestionToAI()
  }, 2000)
}

// Enviar a pergunta atual para a IA
function sendCurrentQuestionToAI() {
  const question = questions.value[currentQuestion.value]
  const message = `
    Questão ${currentQuestion.value + 1} de ${questions.value.length}:
    ${question.text}
    
    Por favor, responda como você se sentiu na última semana escolhendo uma das opções:
    0: Não
    1: Um pouco
    2: Bastante
    3: O tempo todo
  `
  sendMessageToAI(message)
}

// Enviar mensagem para a IA
function sendMessageToAI(message) {
  if (!elevenLabsWidget) {
      console.warn('Não foi possível enviar a mensagem, o widget não está inicializado.');
      return;
  }

  console.log('Enviando mensagem para IA:', message);
  
  // Usar o método sendContextualUpdate que está disponível no objeto 
  // retornado por Conversation.startSession()
  try {
    elevenLabsWidget.sendContextualUpdate(message);
  } catch (error) {
    console.error('Erro ao enviar mensagem:', error);
  }
}

// Manipular mensagens recebidas da IA
function handleAIMessage(message) {
  console.log('Mensagem recebida da IA:', message) // Structure might be { type: '...', text: '...' } based on README

  // Ensure message and message.text exist
  const content = message?.text || '';

  if (!content) {
      console.warn('Received message from AI without text content:', message);
      return;
  }

  console.log('AI Message Content:', content);

  // Verificar se a mensagem contém uma resposta reconhecível
  // Regex improved slightly for robustness
  const responseRegex = /\b(Não|Um\s+pouco|Bastante|O\s+tempo\s+todo|[0-3])\b/i
  const match = content.match(responseRegex)

  if (match) {
    const response = match[0].toLowerCase().trim() // Trim whitespace
    let optionIndex = null

    console.log('Matched response:', response);

    // Mapear a resposta para o índice da opção
    if (response === 'não' || response === '0') {
      optionIndex = '0'
    } else if (response.includes('pouco') || response === '1') {
      optionIndex = '1'
    } else if (response.includes('bastante') || response === '2') {
      optionIndex = '2'
    } else if (response.includes('tempo todo') || response === '3') { // Use includes for "tempo todo"
      optionIndex = '3'
    }

    if (optionIndex !== null) {
      console.log('Setting selectedOption to:', optionIndex);
      selectedOption.value = optionIndex

      // Após um pequeno atraso, avançar para a próxima pergunta
      // Ensure this doesn't conflict with manual navigation
      setTimeout(() => {
        // Check if the user hasn't already manually navigated or changed the option
        if (selectedOption.value === optionIndex && currentView.value === 'assistant') {
          console.log('Auto-advancing to next question after AI response.');
          answerQuestion(true);
        } else {
            console.log('Auto-advance cancelled (option changed or view changed).');
        }
      }, 1500) // Slightly shorter delay
    } else {
        console.log('Matched text but could not map to option index:', response);
    }
  } else {
      console.log('No valid response option found in AI message.');
      // Handle non-response messages? E.g., AI asking for clarification?
  }
}

// Gerenciamento do ciclo de vida
onMounted(() => {
  // Atraso para garantir que o DOM está completamente carregado
  setTimeout(() => {
    console.log('DOM carregado, container:', elevenLabsWidgetContainer.value)
    
    // Se estivermos na vista do assistente, inicializar o widget
    if (currentView.value === 'assistant') {
      initElevenLabsWidget()
    }
  }, 500)
})

onBeforeUnmount(() => {
  cleanupWidget()
})

// Adicionar método de monitoramento de Client Tools
function logClientToolCall(toolName, parameters, result) {
  const callInfo = {
    timestamp: new Date().toISOString(),
    tool: toolName,
    parameters,
    result,
    questionIndex: currentQuestion.value
  }
  
  console.log('Client Tool Call:', callInfo)
  clientToolCalls.value.push(callInfo)
  
  // Opcional: Limite o número de logs armazenados para não consumir muita memória
  if (clientToolCalls.value.length > 50) {
    clientToolCalls.value.shift()
  }
}

// Initialize ElevenLabs Conversational Client
// Renamed from setupElevenLabsAPI
async function startElevenLabsSession() {
  if (widgetInitialized.value) {
    console.log('ElevenLabs session already initialized.');
    return elevenLabsWidget; // Return existing instance
  }
  if (!elevenLabsWidgetContainer.value) {
    console.error('Container for widget not found!');
    widgetError.value = true;
    return null;
  }

  console.log('Initializing ElevenLabs Session...');
  widgetError.value = false; // Reset error state

  try {
    const signedUrl = await getSignedUrl();
    console.log('Signed URL obtained:', !!signedUrl);

    // Options for Conversation.startSession based on README and previous code
    const sessionOptions = {
      // Authentication: Prefer signedUrl if available, otherwise use apiKey if available
      ...(signedUrl ? { signedUrl } : (ELEVEN_LABS_API_KEY ? { apiKey: ELEVEN_LABS_API_KEY } : {})),
      agentId: ELEVEN_LABS_AGENT_ID,
      container: elevenLabsWidgetContainer.value, // Assuming this works for UI mounting
      webSocketBaseUrl: ELEVEN_LABS_WEBSOCKET_URL || undefined, // Keep if needed for custom endpoint
      displayOptions: { // Assuming this works
        showAvatar: true,
        showInput: true,
        style: {
          width: '100%',
          height: '100%',
          borderRadius: '8px'
        }
      },
      // Registrar as Client Tools que foram configuradas no painel ElevenLabs
      clientTools: {
        // Tool set_response configurada pelo usuário no painel da ElevenLabs
        set_response: (params) => {
          try {
            console.log('[Client Tool] set_response chamado:', JSON.stringify(params));
            if (!params) {
              console.error('[Client Tool] set_response recebeu params nulos ou indefinidos');
              return { success: false, error: 'Parâmetros ausentes' };
            }
            
            // Diagnóstico detalhado dos parâmetros
            console.log('[Client Tool] Tipo de params:', typeof params);
            console.log('[Client Tool] Chaves em params:', Object.keys(params));
            
            // Tentativa de extrair a opção de várias formas possíveis
            let option = null;
            
            // Verificar se params é um objeto
            if (typeof params === 'object') {
              if ('option' in params) {
                option = params.option;
                console.log('[Client Tool] Opção encontrada na chave "option":', option);
              } else if ('Option' in params) {
                option = params.Option;
                console.log('[Client Tool] Opção encontrada na chave "Option" (case sensitive):', option);
              } else {
                // Tentar encontrar alguma chave que contenha "option" de forma case-insensitive
                const optionKey = Object.keys(params).find(key => key.toLowerCase() === 'option');
                if (optionKey) {
                  option = params[optionKey];
                  console.log(`[Client Tool] Opção encontrada na chave "${optionKey}":`, option);
                }
              }
            } else if (typeof params === 'string' || typeof params === 'number') {
              // Se params não for um objeto, mas uma string ou número, tentar usar diretamente
              option = params;
              console.log('[Client Tool] Params é um valor direto, não um objeto:', option);
            }
            
            if (option === null) {
              console.error('[Client Tool] Não foi possível extrair uma opção válida dos parâmetros');
              return { success: false, error: 'Opção não encontrada nos parâmetros' };
            }
            
            // Converter para string para facilitar comparações
            option = String(option).trim();
            
            // Agora podemos processar diferentes formatos de opção
            let optionIndex = -1;
            
            // Verificar se é um número (0, 1, 2, 3)
            if (['0', '1', '2', '3'].includes(option)) {
              optionIndex = parseInt(option);
              console.log('[Client Tool] Opção reconhecida como número:', optionIndex);
            } 
            // Verificar se é texto exato que corresponde às opções
            else if (option.toLowerCase() === 'não' || option.toLowerCase() === 'nao') {
              optionIndex = 0;
              console.log('[Client Tool] Opção reconhecida como texto "Não"');
            } else if (option.toLowerCase() === 'um pouco') {
              optionIndex = 1;
              console.log('[Client Tool] Opção reconhecida como texto "Um pouco"');
            } else if (option.toLowerCase() === 'bastante') {
              optionIndex = 2;
              console.log('[Client Tool] Opção reconhecida como texto "Bastante"');
            } else if (option.toLowerCase() === 'o tempo todo') {
              optionIndex = 3;
              console.log('[Client Tool] Opção reconhecida como texto "O tempo todo"');
            } else {
              // Tentar interpretar como número mesmo se não for exatamente "0", "1", "2", "3"
              const num = parseInt(option);
              if (!isNaN(num) && num >= 0 && num <= 3) {
                optionIndex = num;
                console.log('[Client Tool] Opção convertida para número:', optionIndex);
              } else {
                console.error('[Client Tool] Opção não reconhecida:', option);
                return { success: false, error: 'Opção não reconhecida. Use 0-3 ou o texto correspondente.' };
              }
            }
            
            if (optionIndex >= 0 && optionIndex <= 3) {
              // Selecionar a opção na interface
              selectedOption.value = optionIndex.toString();
              console.log(`[Client Tool] Selecionou opção ${optionIndex}: ${options[optionIndex]}`);
              
              // Auto-avanço após seleção (aumento no tempo para dar mais tempo para o agente perceber)
              setTimeout(() => {
                if (elevenLabsWidget && !isPaused.value) {
                  // Verificar se ainda estamos na mesma pergunta
                  answerQuestion(true);
                }
              }, 2000); // Aumentei para 2 segundos
              
              return { success: true, selected: optionIndex };
            } else {
              console.error('[Client Tool] Índice de opção inválido:', optionIndex);
              return { success: false, error: 'Índice de opção inválido' };
            }
          } catch (error) {
            console.error('[Client Tool] Erro ao processar set_response:', error);
            return { success: false, error: String(error) };
          }
        },

        // Nova Client Tool: previous_question - voltar para a questão anterior
        previous_question: (parameters) => {
          console.log('Client Tool previous_question chamada pelo agente');
          
          if (currentQuestion.value > 0) {
            currentQuestion.value--;
            console.log(`Voltando para a questão ${currentQuestion.value + 1}`);
            
            // Atualizar a pergunta no assistente após um pequeno delay
            setTimeout(() => {
              sendCurrentQuestionToAI();
            }, 500);
            
            const result = { 
              success: true, 
              message: `Voltando para a questão ${currentQuestion.value + 1}`,
              current_question: currentQuestion.value + 1
            };
            logClientToolCall('previous_question', parameters, result);
            return result;
          } else {
            // Já está na primeira questão
            const result = { 
              success: false, 
              message: "Já está na primeira questão",
              current_question: currentQuestion.value + 1
            };
            logClientToolCall('previous_question', parameters, result);
            return result;
          }
        },
        
        // Nova Client Tool: next_question - avançar para a próxima questão
        next_question: (parameters) => {
          console.log('Client Tool next_question chamada pelo agente');
          
          // Só permitir avançar se tiver uma resposta selecionada
          if (selectedOption.value === null) {
            const result = { 
              success: false, 
              message: "Não é possível avançar sem selecionar uma resposta",
              current_question: currentQuestion.value + 1
            };
            logClientToolCall('next_question', parameters, result);
            return result;
          }
          
          if (currentQuestion.value < questions.value.length - 1) {
            // Salvar a resposta atual antes de avançar
            answerQuestion(true);
            console.log(`Avançando para a questão ${currentQuestion.value + 1}`);
            
            // Atualizar a pergunta no assistente após um pequeno delay
            setTimeout(() => {
              sendCurrentQuestionToAI();
            }, 500);
            
            const result = { 
              success: true, 
              message: `Avançando para a questão ${currentQuestion.value + 1}`,
              current_question: currentQuestion.value + 1
            };
            logClientToolCall('next_question', parameters, result);
            return result;
          } else if (currentQuestion.value === questions.value.length - 1) {
            // Na última questão, finalizar o teste
            answerQuestion(true);
            
            const result = { 
              success: true, 
              message: "Teste finalizado, exibindo resultados",
              current_question: "Finalizado"
            };
            logClientToolCall('next_question', parameters, result);
            return result;
          }
        },
        
        // Nova Client Tool: goto_question - ir para uma questão específica
        goto_question: (parameters) => {
          console.log('Client Tool goto_question chamada pelo agente:', parameters);
          
          // Verifica se o número da questão foi fornecido
          if (parameters && parameters.question_number !== undefined) {
            let targetQuestion = parseInt(parameters.question_number);
            
            // Ajusta para índice base-0
            targetQuestion = targetQuestion - 1;
            
            // Verifica se é um número válido de questão
            if (!isNaN(targetQuestion) && targetQuestion >= 0 && targetQuestion < questions.value.length) {
              // Se já tiver uma resposta selecionada na questão atual, salva antes de mudar
              if (selectedOption.value !== null) {
                const existingAnswer = answers.value.find(a => a.questionIndex === currentQuestion.value);
                if (existingAnswer) {
                  existingAnswer.answer = selectedOption.value;
                } else {
                  answers.value.push({ 
                    questionIndex: currentQuestion.value, 
                    question: questions.value[currentQuestion.value], 
                    answer: selectedOption.value 
                  });
                }
              }
              
              // Atualiza a questão atual
              currentQuestion.value = targetQuestion;
              console.log(`Indo para a questão ${targetQuestion + 1}`);
              
              // Atualiza o selectedOption para o valor já respondido, se existir
              const existing = answers.value.find(a => a.questionIndex === targetQuestion);
              selectedOption.value = existing ? existing.answer : null;
              
              // Atualizar a pergunta no assistente após um pequeno delay
              setTimeout(() => {
                sendCurrentQuestionToAI();
              }, 500);
              
              const result = { 
                success: true, 
                message: `Indo para a questão ${targetQuestion + 1}`,
                current_question: targetQuestion + 1
              };
              logClientToolCall('goto_question', parameters, result);
              return result;
            } else {
              const result = { 
                success: false, 
                message: `Número de questão inválido: ${parameters.question_number}. Deve estar entre 1 e ${questions.value.length}`,
                current_question: currentQuestion.value + 1
              };
              logClientToolCall('goto_question', parameters, result);
              return result;
            }
          }
          
          const result = { 
            success: false, 
            message: "Parâmetro question_number não fornecido",
            current_question: currentQuestion.value + 1
          };
          logClientToolCall('goto_question', parameters, result);
          return result;
        }
      },
      callbacks: {
        onConnect: () => {
          console.log('Widget conectado!');
          widgetInitialized.value = true;
          widgetError.value = false;
          // Enviar mensagem de boas-vindas após conectar
          sendWelcomeMessage();
          exposeWidgetForTest(); // Expor para depuração
        },
        onDisconnect: (event) => {
          console.warn('Widget desconectado.', 
            {
              code: event?.code,
              reason: event?.reason,
              wasClean: event?.wasClean
            }
          );
          widgetInitialized.value = false;
          // Não limpar o widget aqui automaticamente, pode ser temporário
        },
        onError: (error) => {
          console.error('Erro no widget ElevenLabs:', 
            {
              message: error?.message,
              name: error?.name,
              stack: error?.stack,
              details: error // Log completo do objeto de erro
            }
          );
          widgetError.value = true;
          widgetInitialized.value = false;
          // Considerar limpar o widget ou tentar reconectar dependendo do erro
          // cleanupWidget(); 
        },
        onModeChange: (mode) => {
          console.log('Mudança de modo do agente:', mode.mode); // 'speaking' or 'listening'
          // Atualizar UI se necessário
        },
        // Remover ou comentar callbacks não utilizados ou não documentados
        // onMessage: (message) => { console.log('Mensagem recebida:', message); },
        // onInputWorkletMessage: (message) => { console.log('Input Worklet Msg:', message); },
        // onOutputWorkletMessage: (message) => { console.log('Output Worklet Msg:', message); },
      }
    };

    // Check if any authentication method is provided
    if (!sessionOptions.signedUrl && !sessionOptions.apiKey) {
        console.error('No ElevenLabs Signed URL or API Key provided for authentication.');
        widgetError.value = true;
        return null;
    }

    // Start the session
    elevenLabsWidget = await Conversation.startSession(sessionOptions);

    console.log('Sessão ElevenLabs criada com sucesso');
    
    // Adicionar event listener para o evento elevenlabs-convai:call, que é disparado
    // quando o agente tenta chamar uma Client Tool
    if (elevenLabsWidgetContainer.value) {
      elevenLabsWidgetContainer.value.addEventListener('elevenlabs-convai:call', (event) => {
        console.log('Evento elevenlabs-convai:call detectado:', event.detail);
        // Aqui você pode fazer tratamentos adicionais se necessário
      });
    }

    // Importante: Definir widgetInitialized como true manualmente,
    // já que o callback onConnect não está sendo chamado
    widgetInitialized.value = true;
    widgetError.value = false;

    // Enviar a mensagem de boas-vindas após um breve atraso
    setTimeout(() => {
      if (elevenLabsWidget) {
        sendWelcomeMessage();
        // Expor widget para testes se estiver em modo de depuração
        exposeWidgetForTest();
      }
    }, 1000);
    
    return elevenLabsWidget;

  } catch (error) {
    console.error('Error starting ElevenLabs Session:', error);
    console.error('Error details:', error.message, error.stack);
    widgetError.value = true;
    widgetInitialized.value = false; // Ensure state is correct on error
    throw error; // Rethrow to allow retry logic in onMounted
  }
}

// Keep the second onMounted hook, ensure it calls the correct function
onMounted(async () => {
  console.log('Component mounted, awaiting DOM render')

  // Delay to ensure DOM is fully loaded
  setTimeout(async () => {
    console.log('DOM loaded, container:', elevenLabsWidgetContainer.value)

    // Initialize if in the assistant view
    if (currentView.value === 'assistant') {
      try {
        await startElevenLabsSession() // Call the renamed function
        console.log('ElevenLabs Session initialization attempt complete.')
        // Note: Actual success confirmed by onConnect callback setting widgetInitialized = true
      } catch (error) {
        console.error('Error during initial ElevenLabs setup:', error)
        // widgetError is set within startElevenLabsSession's catch block

        // Retry logic
        retryCount++
        if (retryCount < maxRetries) {
          console.log(`Attempt ${retryCount} of ${maxRetries}. Retrying initialization...`)
          setTimeout(startElevenLabsSession, 2000) // Retry call to renamed function
        } else {
          console.error(`Max retries (${maxRetries}) reached. Failed to initialize ElevenLabs.`);
        }
      }
    }
  }, 500) // Keep delay
})

// Registrar e exportar a referência do widget para testes
function exposeWidgetForTest() {
  if (debugMode.value && elevenLabsWidget && typeof window !== 'undefined') {
    window._elevenLabsWidget = elevenLabsWidget;
    console.log('Widget exposto na variável global window._elevenLabsWidget para testes.');
  }
}

// Teste forçado da Client Tool set_response (apenas em modo de depuração)
function testSetResponseTool() {
  if (!elevenLabsWidget) {
    console.warn('Widget não inicializado.');
    return;
  }
  
  // Detecção do método para testar a ferramenta
  if (elevenLabsWidget.options && elevenLabsWidget.options.clientTools) {
    console.log('Testando Client Tool set_response diretamente...');
    
    // Chama a função diretamente com um parâmetro de teste
    const result = elevenLabsWidget.options.clientTools.set_response({ option: '1' });
    console.log('Resultado do teste:', result);
  } else {
    console.warn('Não foi possível acessar as Client Tools diretamente.');
    
    // Alternativa: enviar uma mensagem que deve acionar o uso da ferramenta pelo agente
    sendMessageToAI("Por favor, use a ferramenta set_response para registrar minha resposta como 'Um pouco'");
  }
}

// Funções de teste para as novas Client Tools
function testPreviousQuestionTool() {
  if (!elevenLabsWidget) {
    console.warn('Widget não inicializado.');
    return;
  }
  
  if (elevenLabsWidget.options && elevenLabsWidget.options.clientTools) {
    console.log('Testando Client Tool previous_question diretamente...');
    const result = elevenLabsWidget.options.clientTools.previous_question({});
    console.log('Resultado do teste:', result);
  } else {
    console.warn('Não foi possível acessar as Client Tools diretamente.');
    sendMessageToAI("Por favor, volte para a questão anterior usando a ferramenta previous_question.");
  }
}

function testNextQuestionTool() {
  if (!elevenLabsWidget) {
    console.warn('Widget não inicializado.');
    return;
  }
  
  if (elevenLabsWidget.options && elevenLabsWidget.options.clientTools) {
    console.log('Testando Client Tool next_question diretamente...');
    const result = elevenLabsWidget.options.clientTools.next_question({});
    console.log('Resultado do teste:', result);
  } else {
    console.warn('Não foi possível acessar as Client Tools diretamente.');
    sendMessageToAI("Por favor, avance para a próxima questão usando a ferramenta next_question.");
  }
}

function testGotoQuestionTool() {
  if (!elevenLabsWidget) {
    console.warn('Widget não inicializado.');
    return;
  }
  
  // Pedir ao usuário para qual questão deseja ir
  const questionNumber = prompt('Digite o número da questão para a qual deseja ir (1-21):');
  if (!questionNumber) return;
  
  if (elevenLabsWidget.options && elevenLabsWidget.options.clientTools) {
    console.log(`Testando Client Tool goto_question diretamente para questão ${questionNumber}...`);
    const result = elevenLabsWidget.options.clientTools.goto_question({ question_number: questionNumber });
    console.log('Resultado do teste:', result);
  } else {
    console.warn('Não foi possível acessar as Client Tools diretamente.');
    sendMessageToAI(`Por favor, vá para a questão ${questionNumber} usando a ferramenta goto_question.`);
  }
}

// Adicionar nova função de instrução ao agente
function sendToolInstructions() {
  const message = `
    IMPORTANTE: Lembre-se de como usar as ferramentas disponíveis corretamente.
    
    Quando o usuário indicar uma escolha (como "Não", "Um pouco", etc.), você deve usar a ferramenta set_response com o parâmetro option.
    
    Exemplos corretos:
    - set_response(option="0") para registrar "Não"
    - set_response(option="1") para registrar "Um pouco"
    - set_response(option="2") para registrar "Bastante"
    - set_response(option="3") para registrar "O tempo todo"
    
    A ferramenta set_response espera receber um objeto com o parâmetro "option" e NÃO apenas o valor numérico.
    
    Quando você receber uma resposta do usuário, sempre utilize a ferramenta set_response.
  `;
  
  sendMessageToAI(message);
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.option-card:focus-within {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

.result-item {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.level-indicator {
  position: relative;
}

.level-indicator .bg-muted {
  background-color: rgba(0, 0, 0, 0.05);
}

.scale-legend {
  font-size: 0.7rem;
  opacity: 0.7;
}

/* Cores específicas para cada nível (modo claro) */
:root {
  --color-normal: 22, 163, 74;    /* verde */
  --color-mild: 245, 158, 11;     /* amarelo */
  --color-moderate: 217, 119, 6;  /* âmbar */
  --color-severe: 220, 38, 38;    /* vermelho */
  --color-extreme: 153, 27, 27;   /* vermelho escuro */
}

/* Ajuste para modo escuro */
.dark .result-item {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .level-indicator .bg-muted {
  background-color: rgba(255, 255, 255, 0.05);
}

.voice-assistant-container {
  height: 500px;
  max-height: 60vh;
}

.elevenlabs-widget {
  min-height: 300px;
}
</style> 