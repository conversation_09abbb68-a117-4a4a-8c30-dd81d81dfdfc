<template>
  <main class="flex flex-1 flex-col leading-tight select-none p-4 w-full max-w-4xl mx-auto">
    <!-- Tela de introdução -->
    <section class="flex flex-col gap-6 mb-8">
      <header class="text-center mb-6">
        <h1 class="text-4xl font-bold mb-1">DASS-21</h1>
        <h1 class="text-2xl font-bold mb-2">Escala de Depressão, Ansiedade e Estresse</h1>
        <p class="text-muted-foreground">Um instrumento para avaliação de sintomas emocionais</p>
      </header>

      <!-- Indicador de progresso salvo -->
      <div v-if="hasSavedProgress" class="saved-progress-card p-6 border-2 border-blue-200 bg-blue-50 rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <Icon name="clock" class="text-blue-600 h-6 w-6" />
          <h3 class="text-lg font-semibold text-blue-800">Progresso Salvo Encontrado</h3>
        </div>
        <p class="text-blue-700 mb-4">
          Você tem um teste em andamento com {{ progressStats.answeredCount }} de {{ progressStats.totalQuestions }} questões respondidas
          ({{ progressStats.percentComplete }}% completo).
        </p>
        <div class="flex gap-3">
          <Button @click="continueTest" class="bg-blue-600 hover:bg-blue-700">
            <Icon name="play" class="mr-2 h-4 w-4" />
            Continuar de onde parei
          </Button>
          <Button @click="showRestartConfirmation = true" variant="outline" class="border-blue-300 text-blue-700">
            <Icon name="refresh-cw" class="mr-2 h-4 w-4" />
            Recomeçar
          </Button>
        </div>
      </div>

      <div class="info-card p-8 border rounded-lg">
        <h2 class="text-xl font-black mb-2">Sobre este teste</h2>
        <p>O DASS-21 é um conjunto de três escalas para medir os estados emocionais de depressão, ansiedade e
          estresse.</p>
        <p class="mt-2">Cada escala contém 7 questões, totalizando 21 itens que você responderá sobre como se sentiu
          na última semana.</p>
      </div>

      <div class="instructions p-8 border rounded-lg">
        <h2 class="text-xl font-black mb-2">Instruções</h2>
        <ul class="list-disc pl-5 space-y-2">
          <li>Leia cada afirmação e selecione a opção que melhor indica o quanto ela se aplicou a você <strong>durante
              a última semana</strong>.</li>
          <li>Não há respostas certas ou erradas - responda com sinceridade.</li>
          <li>Tente não gastar muito tempo em qualquer afirmação.</li>
          <li>O teste leva aproximadamente 5-10 minutos para ser concluído.</li>
          <li><strong>Seu progresso é salvo automaticamente</strong> - você pode pausar e continuar depois.</li>
        </ul>
      </div>

      <Button @click="startTest" class="mt-4" size="lg" :disabled="isLoading">
        <Icon v-if="isLoading" name="loader-2" class="mr-2 h-4 w-4 animate-spin" />
        <Icon v-else name="play" class="mr-2 h-4 w-4" />
        {{ hasSavedProgress ? 'Recomeçar Teste' : 'Iniciar Teste' }}
      </Button>

      <!-- Botão de debug temporário -->
      <div class="mt-4 p-4 bg-gray-100 rounded-lg text-sm">
        <p><strong>Debug Info:</strong></p>
        <p>hasSavedProgress: {{ hasSavedProgress }}</p>
        <p>progressStats: {{ JSON.stringify(progressStats) }}</p>
        <Button @click="createTestData" variant="outline" size="sm" class="mt-2">
          Criar Dados de Teste
        </Button>
      </div>
    </section>

    <!-- Modal de confirmação para recomeçar -->
    <div v-if="showRestartConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-3">Confirmar Reinício</h3>
        <p class="text-gray-600 mb-4">
          Tem certeza que deseja recomeçar o teste? Todo o progresso atual será perdido.
        </p>
        <div class="flex gap-3 justify-end">
          <Button @click="showRestartConfirmation = false" variant="outline">
            Cancelar
          </Button>
          <Button @click="confirmRestart" variant="destructive">
            Sim, Recomeçar
          </Button>
        </div>
      </div>
    </div>

  </main>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useDASS21 } from '~/composables/useDASS21.js'
import { clearTestData } from '~/utils/testPersistence.js'

const router = useRouter()

const {
  // Estado
  isLoading,
  progressStats,

  // Métodos
  hasSavedProgress,
  navigateToQuestion,
  loadSavedProgress
} = useDASS21()

// Estado local da página
const showRestartConfirmation = ref(false)

// Inicializar ao montar o componente
onMounted(async () => {
  console.log('🔄 Carregando progresso salvo...')
  await loadSavedProgress()
  console.log('📊 Progresso carregado:', {
    hasSavedProgress: hasSavedProgress.value,
    progressStats: progressStats.value
  })
})

/**
 * Inicia um novo teste
 */
async function startTest() {
  try {
    if (hasSavedProgress.value) {
      // Se há progresso salvo, limpar antes de começar novo
      clearTestData('DASS-21')
    }

    // Navegar para a primeira questão
    await router.push('/DASS-21/apply/question/1')
  } catch (error) {
    console.error('Erro ao iniciar teste:', error)
  }
}

/**
 * Continua o teste de onde parou
 */
async function continueTest() {
  try {
    console.log('▶️ Continuando teste...')

    // Carregar progresso salvo primeiro
    await loadSavedProgress()

    // Encontrar a próxima questão não respondida
    let nextQuestionIndex = 0

    if (progressStats.value.answeredCount > 0) {
      // Se há respostas, ir para a próxima questão não respondida
      nextQuestionIndex = Math.min(progressStats.value.answeredCount, 20)
    }

    console.log('🎯 Navegando para questão:', nextQuestionIndex + 1, {
      answeredCount: progressStats.value.answeredCount,
      nextQuestionIndex
    })

    // Navegar para a questão
    const questionNumber = nextQuestionIndex + 1
    await router.push(`/DASS-21/apply/question/${questionNumber}`)
  } catch (error) {
    console.error('❌ Erro ao continuar teste:', error)
    // Fallback: ir para a primeira questão
    await router.push('/DASS-21/apply/question/1')
  }
}

/**
 * Confirma o reinício do teste
 */
async function confirmRestart() {
  showRestartConfirmation.value = false
  clearTestData('DASS-21')
  await loadSavedProgress() // Recarregar para atualizar o estado
}

/**
 * Função de teste para simular dados salvos (apenas para debug)
 */
function createTestData() {
  if (typeof window !== 'undefined') {
    // Simular algumas respostas salvas
    const testAnswers = {
      0: { answer: '1', questionData: { text: 'Teste 1', category: 'stress' }, answeredAt: new Date().toISOString() },
      1: { answer: '2', questionData: { text: 'Teste 2', category: 'anxiety' }, answeredAt: new Date().toISOString() },
      2: { answer: '0', questionData: { text: 'Teste 3', category: 'depression' }, answeredAt: new Date().toISOString() }
    }

    const testProgress = {
      currentQuestion: 3,
      totalQuestions: 21,
      answeredCount: 3,
      progressPercentage: 14,
      lastUpdated: new Date().toISOString(),
      version: '1.0'
    }

    localStorage.setItem('test_answers_DASS-21', JSON.stringify(testAnswers))
    localStorage.setItem('test_progress_DASS-21', JSON.stringify(testProgress))

    console.log('🧪 Dados de teste criados!')

    // Recarregar progresso
    loadSavedProgress()
  }
}

// Expor função para debug no console
if (typeof window !== 'undefined') {
  window.createTestData = createTestData
}

definePageMeta({
  layout: 'test'
})
</script>

<style scoped>
.saved-progress-card {
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card,
.instructions {
  transition: all 0.2s ease-in-out;
}

.info-card:hover,
.instructions:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
</style>
