<template>
  <main class="flex flex-1 flex-col leading-tight select-none p-4 w-full max-w-4xl mx-auto">
    <transition name="fade" mode="out-in">
      <!-- Tela de introdução -->
      <section v-if="currentView === 'intro'" key="intro" class="flex flex-col gap-6 mb-8">
        <header class="text-center mb-6">
          <h1 class="text-4xl font-bold mb-1">DASS-21</h1>
          <h1 class="text-2xl font-bold mb-2">Escala de Depressão, Ansiedade e Estresse</h1>
          <p class="text-muted-foreground">Um instrumento para avaliação de sintomas emocionais</p>
        </header>

        <div class="info-card p-8 border rounded-lg">
          <h2 class="text-xl font-black mb-2">Sobre este teste</h2>
          <p>O DASS-21 é um conjunto de três escalas para medir os estados emocionais de depressão, ansiedade e
            estresse.</p>
          <p class="mt-2">Cada escala contém 7 questões, totalizando 21 itens que você responderá sobre como se sentiu
            na última semana.</p>
        </div>

        <div class="instructions p-8 border rounded-lg">
          <h2 class="text-xl font-black mb-2">Instruções</h2>
          <ul class="list-disc pl-5 space-y-2">
            <li>Leia cada afirmação e selecione a opção que melhor indica o quanto ela se aplicou a você <strong>durante
                a última semana</strong>.</li>
            <li>Não há respostas certas ou erradas - responda com sinceridade.</li>
            <li>Tente não gastar muito tempo em qualquer afirmação.</li>
            <li>O teste leva aproximadamente 5-10 minutos para ser concluído.</li>
          </ul>
        </div>

        <Button @click="startTest" class="mt-4" size="lg">Iniciar Teste</Button>
      </section>

      <!-- Questões do teste -->
      <section v-else-if="currentView === 'questions'" key="questions" class="flex flex-col">
        <header class="mb-12">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm text-muted-foreground">Questão {{ currentQuestion + 1 }} de {{ questions.length
            }}</span>
            <div class="flex items-center gap-2">
              <Button v-if="!isPaused" @click="togglePause" variant="ghost" size="sm" class="flex items-center">
                <Icon name="pause" class="mr-1" /> Pausar
              </Button>
              <Button v-else @click="togglePause" variant="ghost" size="sm" class="flex items-center">
                <Icon name="play" class="mr-1" /> Continuar
              </Button>
            </div>
          </div>
          <Progress :value="(currentQuestion / questions.length) * 100" />
        </header>

        <div v-if="!isPaused" class="question-card py-6 mb-6">
          <transition name="slide-fade" mode="out-in">
            <div :key="currentQuestion">
              <h2 class="text-xl font-medium mb-4">
                <span class="block text-sm text-muted-foreground mb-1">Na última semana...</span>
                {{ questions[currentQuestion].text }}
              </h2>

              <form @submit.prevent="answerQuestion">
                <RadioGroup v-model="selectedOption">
                  <div v-for="(option, index) in options" :key="index"
                    class="option-card p-3 border rounded hover:bg-accent transition-colors cursor-pointer"
                    :class="{ 'border-primary bg-accent/20': selectedOption === String(index) }"
                    @click="selectedOption = String(index)">
                    <div class="flex items-center gap-2">
                      <RadioGroupItem :id="'option-' + index" :value="String(index)" />
                      <Label :for="'option-' + index" class="cursor-pointer block w-full">{{ option }}</Label>
                    </div>
                  </div>
                </RadioGroup>
                <button type="submit" class="hidden">Submit</button>
              </form>
            </div>
          </transition>
        </div>

        <div v-else class="pause-overlay flex flex-col items-center justify-center p-8 border rounded-lg text-center">
          <h3 class="text-xl font-medium mb-4">Teste pausado</h3>
          <p class="text-muted-foreground mb-6">Você pode continuar quando estiver pronto</p>
          <Button @click="togglePause" class="flex items-center">
            <Icon name="play" class="mr-2" /> Continuar o teste
          </Button>
        </div>

        <div class="navigation-buttons flex justify-between mt-4">
          <Button v-if="currentQuestion > 0" @click="goBack" variant="outline" class="flex items-center">
            <Icon name="arrow-left" class="mr-1" /> Voltar
          </Button>
          <div v-else class="invisible"><!-- Placeholder para manter o layout --></div>

          <Button v-if="!isPaused" @click="answerQuestion" :disabled="selectedOption === null"
            class="flex items-center">
            Continuar
            <Icon name="arrow-right" class="ml-1" />
          </Button>
        </div>
      </section>

      <!-- Resultados do teste -->
      <section v-else key="results" class="flex flex-col w-full max-w-3xl mx-auto">
        <header class="text-center mb-8">
          <h1 class="text-2xl font-bold mb-2">Resultados do DASS-21</h1>
          <p class="text-muted-foreground">Seus níveis de depressão, ansiedade e estresse</p>

          <!-- Contexto da Pesquisa -->
          <div v-if="isResearchContext" class="research-context mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center gap-2 mb-2">
              <Icon name="info" class="text-blue-600 h-5 w-5" />
              <h3 class="text-sm font-semibold text-blue-800">Contexto da Pesquisa - Visão do Paciente</h3>
            </div>
            <p class="text-sm text-blue-700">
              Você está visualizando os resultados como um <strong>paciente</strong> veria.
              Esta é a primeira parte da simulação da pesquisa.
            </p>
          </div>
        </header>

        <!-- Legenda única para todos os resultados -->
        <div class="scale-legend flex justify-between mb-4 px-4 text-xs text-muted-foreground">
          <span>Normal</span>
          <span>Leve</span>
          <span>Moderado</span>
          <span>Severo</span>
          <span>Extremo</span>
        </div>

        <div class="results-grid space-y-6 mb-8">
          <div v-for="result in resultsData" :key="result.label" class="result-item border p-5 rounded-lg"
            :class="`border-${levelColorMap[getResultLevel(result.label, result.value)]}/50`">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold">{{ result.label }}</h3>
              <span class="text-2xl font-bold">{{ result.value }}</span>
            </div>

            <div class="level-indicator mb-5">
              <div class="bg-muted h-3 w-full rounded-full overflow-hidden">
                <div class="h-full rounded-full" :style="{
                  width: getLevelPercentage(result.label, result.value) + '%',
                  backgroundColor: getLevelColor(getResultLevel(result.label, result.value))
                }"></div>
              </div>
            </div>

            <p class="text-sm">{{ getResultDescription(result.label, result.value) }}</p>
          </div>
        </div>

        <div class="interpretation p-5 border rounded-lg mb-8 bg-primary-foreground/30">
          <h2 class="text-lg font-semibold mb-3">Entendendo seus resultados</h2>
          <p class="mb-3">Esta escala fornece uma medida da gravidade dos sintomas de depressão, ansiedade e estresse.
            Os resultados são baseados em suas respostas sobre como você se sentiu na última semana.</p>
          <p class="text-sm text-muted-foreground italic">Lembre-se que esta é uma ferramenta de auto-avaliação e não
            substitui o diagnóstico profissional. Se você estiver preocupado com seus resultados, considere consultar um
            profissional de saúde mental.</p>
        </div>

        <!-- Contexto da Pesquisa - Próximos Passos -->
        <div v-if="isResearchContext" class="research-next-steps p-5 border border-blue-200 rounded-lg mb-6 bg-blue-50">
          <div class="flex items-center gap-3 mb-3">
            <Icon name="arrow-right" class="text-blue-600 h-6 w-6" />
            <h3 class="text-lg font-medium text-blue-800">Próximo Passo da Pesquisa</h3>
          </div>
          <p class="mb-4 text-blue-700">
            Agora você verá como um <strong>psicólogo</strong> visualizaria e analisaria estes mesmos resultados no
            painel administrativo da plataforma SISTEP.
          </p>
          <Button @click="proceedToPanel" variant="default" class="w-full md:w-auto bg-blue-600 hover:bg-blue-700" data-testid="continue-to-panel">
            <Icon name="stethoscope" class="mr-2 h-4 w-4" />
            Continuar para Visão do Psicólogo
          </Button>
        </div>

        <!-- Diferenciação entre auto-aplicação e aplicação por profissional -->
        <div v-else-if="testStore.isRequestedByProfessional"
          class="professional-feedback p-5 border rounded-lg mb-6 bg-success/10 border-success/30">
          <div class="flex items-center gap-3 mb-3">
            <Icon name="check-circle" class="text-success h-6 w-6" />
            <h3 class="text-lg font-medium">Resultados enviados</h3>
          </div>
          <p class="mb-4">Seus resultados foram enviados para o {{ testStore.professionalName }}. Obrigado por completar
            este teste.</p>
          <Button @click="goToHome" variant="default" class="w-full md:w-auto">
            <Icon name="home" class="mr-2 h-4 w-4" /> Voltar para Início
          </Button>
        </div>

        <div v-else class="action-buttons grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button @click="restartTest" variant="outline" class="flex items-center justify-center">
            <Icon name="arrow-left" class="mr-2" /> Refazer Teste
          </Button>
          <Button @click="shareResults" variant="default" class="flex items-center justify-center">
            <Icon name="share" class="mr-2" /> Compartilhar Resultados
          </Button>
        </div>
      </section>
    </transition>
  </main>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useTestStore } from '~/stores/test'
import ResultCard from '~/components/ResultCard.vue'
import { Icon } from '@/components/ui/icon'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const testStore = useTestStore()

// Configurações do teste
const currentView = ref('intro') // 'intro', 'questions', 'results'
const isPaused = ref(false)

// Detectar contexto de pesquisa
const isResearchContext = computed(() => {
  return route.query.participant_id && route.query.research_return_url
})

// Dados do teste
const questions = ref([
  { text: 'Eu achei difícil acalmar-me.', category: 'stress' },
  { text: 'Minha boca ficou seca.', category: 'anxiety' },
  { text: 'Eu não consegui sentir nenhuma emoção positiva.', category: 'depression' },
  { text: 'Eu tive dificuldade para respirar (por exemplo, respiração excessivamente rápida, falta de ar na ausência de esforço físico).', category: 'anxiety' },
  { text: 'Eu achei difícil ter iniciativa para fazer as coisas.', category: 'depression' },
  { text: 'Eu reagi exageradamente a situações.', category: 'stress' },
  { text: 'Eu senti tremores (por exemplo, nas mãos).', category: 'anxiety' },
  { text: 'Eu senti que estava gastando muita energia.', category: 'stress' },
  { text: 'Eu fiquei preocupado com situações em que eu poderia entrar em pânico e parecer ridículo.', category: 'anxiety' },
  { text: 'Eu senti que não tinha nada para esperar.', category: 'depression' },
  { text: 'Eu achei que estava ficando impaciente.', category: 'stress' },
  { text: 'Eu senti que estava prestes a entrar em pânico.', category: 'anxiety' },
  { text: 'Eu não consegui me entusiasmar com nada.', category: 'depression' },
  { text: 'Eu senti que estava valendo muito pouco como pessoa.', category: 'depression' },
  { text: 'Eu senti que estava sendo intolerante com tudo.', category: 'stress' },
  { text: 'Eu senti que meu coração estava acelerado, mesmo sem esforço físico.', category: 'anxiety' },
  { text: 'Eu senti medo sem motivo.', category: 'anxiety' },
  { text: 'Eu senti que a vida não tinha sentido.', category: 'depression' },
  { text: 'Eu senti que estava inquieto.', category: 'stress' },
  { text: 'Eu senti que estava com medo.', category: 'anxiety' },
  { text: 'Eu senti que estava muito nervoso.', category: 'stress' }
])

const options = ['Não', 'Um pouco', 'Bastante', 'O tempo todo']
const currentQuestion = ref(0)
const selectedOption = ref(null)
const answers = ref([])
const results = ref({ depression: 0, anxiety: 0, stress: 0 })

testStore.initializeNewTest()

definePageMeta({
  layout: 'test'
})

// Watchers e métodos de controle do fluxo
watch(currentQuestion, (newVal) => {
  selectedOption.value = answers.value.find(a => a.questionIndex === newVal)?.answer || null
})

function startTest() {
  currentView.value = 'questions'
}

function togglePause() {
  isPaused.value = !isPaused.value
}

function answerQuestion() {
  if (selectedOption.value !== null) {
    const existingAnswer = answers.value.find(a => a.questionIndex === currentQuestion.value)
    if (existingAnswer) {
      existingAnswer.answer = selectedOption.value
    } else {
      answers.value.push({
        questionIndex: currentQuestion.value,
        question: questions.value[currentQuestion.value],
        answer: selectedOption.value
      })
    }
    currentQuestion.value++
    if (currentQuestion.value >= questions.value.length) {
      calculateResults()
      currentView.value = 'results'
    }
    selectedOption.value = null
  }
}

function goBack() {
  currentQuestion.value--
}

function calculateResults() {
  const scores = { depression: 0, anxiety: 0, stress: 0 }
  answers.value.forEach(({ question, answer }) => {
    scores[question.category] += parseInt(answer)
  })
  results.value = scores

  // Salvar os resultados no store para eventual envio ao profissional
  testStore.saveTestResults({
    test: 'DASS-21',
    timestamp: new Date().toISOString(),
    scores: scores,
    answers: answers.value.map(a => ({
      question: a.question.text,
      category: a.question.category,
      answer: parseInt(a.answer)
    }))
  })

  // Se for contexto de pesquisa, atualizar o currentStep para indicar conclusão do DASS-21
  if (isResearchContext.value && typeof localStorage !== 'undefined') {
    localStorage.setItem('researchCurrentStep', '3') // Próxima etapa: painel do psicólogo
    localStorage.setItem('researchDASS21Completed', 'true') // Flag de conclusão
  }
}

// Backend Requirement: Save test results for research participant
// The backend needs to store the test results associated with the participant_id
// for later retrieval in the admin panel
function proceedToPanel() {
  if (isResearchContext.value) {
    // Backend Requirement: POST test results to API
    // POST /api/research/test-results
    // Body: { participant_id, test_type: 'DASS-21', results: scores, answers: detailed_answers }

    // Redirect back to research page with completion status
    const returnUrl = route.query.research_return_url
    window.location.href = `${returnUrl}&participant_id=${route.query.participant_id}`
  }
}

function restartTest() {
  currentView.value = 'intro'
  currentQuestion.value = 0
  selectedOption.value = null
  answers.value = []
  results.value = { depression: 0, anxiety: 0, stress: 0 }
  isPaused.value = false
}

function shareResults() {
  const resultText = `Meus resultados do teste DASS-21: Depressão: ${results.value.depression}, Ansiedade: ${results.value.anxiety}, Estresse: ${results.value.stress}`

  try {
    navigator.share({
      title: 'Resultados do teste DASS-21',
      text: resultText,
    })
  } catch (error) {
    console.error('Compartilhamento não suportado neste navegador:', error)
    // Fallback para copiar para a área de transferência
    navigator.clipboard.writeText(resultText)
      .then(() => alert('Resultados copiados para a área de transferência!'))
      .catch(err => console.error('Erro ao copiar os resultados:', err))
  }
}

function goToHome() {
  router.push('/')
}

// Dados dos resultados
const resultsData = computed(() => [
  { label: 'Depressão', value: results.value.depression },
  { label: 'Ansiedade', value: results.value.anxiety },
  { label: 'Estresse', value: results.value.stress }
])

// Funções auxiliares para interpretação de resultados
function getResultLevel(category, value) {
  // Valores limites para interpretação do DASS-21
  const thresholds = {
    depression: { normal: 4, mild: 6, moderate: 10, severe: 13 },
    anxiety: { normal: 3, mild: 5, moderate: 7, severe: 10 },
    stress: { normal: 7, mild: 9, moderate: 12, severe: 16 }
  }

  const categoryKey = category.toLowerCase()
  if (categoryKey === 'Depressão'.toLowerCase()) {
    if (value <= thresholds.depression.normal) return 'normal'
    if (value <= thresholds.depression.mild) return 'mild'
    if (value <= thresholds.depression.moderate) return 'moderate'
    if (value <= thresholds.depression.severe) return 'severe'
    return 'extreme'
  } else if (categoryKey === 'Ansiedade'.toLowerCase()) {
    if (value <= thresholds.anxiety.normal) return 'normal'
    if (value <= thresholds.anxiety.mild) return 'mild'
    if (value <= thresholds.anxiety.moderate) return 'moderate'
    if (value <= thresholds.anxiety.severe) return 'severe'
    return 'extreme'
  } else if (categoryKey === 'Estresse'.toLowerCase()) {
    if (value <= thresholds.stress.normal) return 'normal'
    if (value <= thresholds.stress.mild) return 'mild'
    if (value <= thresholds.stress.moderate) return 'moderate'
    if (value <= thresholds.stress.severe) return 'severe'
    return 'extreme'
  }
  return 'normal'
}

function getResultDescription(category, value) {
  const level = getResultLevel(category, value)
  const descriptions = {
    depression: {
      normal: 'Nível normal de sintomas depressivos.',
      mild: 'Sintomas depressivos leves.',
      moderate: 'Sintomas depressivos moderados.',
      severe: 'Sintomas depressivos significativos.',
      extreme: 'Sintomas depressivos extremamente elevados.'
    },
    anxiety: {
      normal: 'Nível normal de sintomas de ansiedade.',
      mild: 'Sintomas leves de ansiedade.',
      moderate: 'Sintomas moderados de ansiedade.',
      severe: 'Sintomas significativos de ansiedade.',
      extreme: 'Sintomas de ansiedade extremamente elevados.'
    },
    stress: {
      normal: 'Nível normal de estresse.',
      mild: 'Nível leve de estresse.',
      moderate: 'Nível moderado de estresse.',
      severe: 'Nível significativo de estresse.',
      extreme: 'Nível extremamente elevado de estresse.'
    }
  }

  const categoryKey = category.toLowerCase()
  if (categoryKey === 'Depressão'.toLowerCase()) {
    return descriptions.depression[level]
  } else if (categoryKey === 'Ansiedade'.toLowerCase()) {
    return descriptions.anxiety[level]
  } else if (categoryKey === 'Estresse'.toLowerCase()) {
    return descriptions.stress[level]
  }
  return ''
}

// Mapeamento de níveis para cores
const levelColorMap = {
  normal: 'success',
  mild: 'warning',
  moderate: 'amber',
  severe: 'destructive',
  extreme: 'destructive'
}

// Função para obter a cor com base no nível
function getLevelColor(level) {
  const colorMap = {
    normal: '#16a34a', // verde
    mild: '#f59e0b',   // amarelo
    moderate: '#d97706', // âmbar
    severe: '#dc2626',   // vermelho
    extreme: '#991b1b'   // vermelho escuro
  }

  return colorMap[level] || colorMap.normal
}

// Função para calcular a porcentagem da barra de progresso
function getLevelPercentage(category, value) {
  const categoryKey = category.toLowerCase()

  // Valores máximos teóricos para cada categoria
  const maxValues = {
    'depressão': 21,
    'ansiedade': 21,
    'estresse': 21
  }

  const max = maxValues[categoryKey] || 21
  return Math.min(100, (value / max) * 100)
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.option-card:focus-within {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

.result-item {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.level-indicator {
  position: relative;
}

.level-indicator .bg-muted {
  background-color: rgba(0, 0, 0, 0.05);
}

.scale-legend {
  font-size: 0.7rem;
  opacity: 0.7;
}

/* Cores específicas para cada nível (modo claro) */
:root {
  --color-normal: 22, 163, 74;
  /* verde */
  --color-mild: 245, 158, 11;
  /* amarelo */
  --color-moderate: 217, 119, 6;
  /* âmbar */
  --color-severe: 220, 38, 38;
  /* vermelho */
  --color-extreme: 153, 27, 27;
  /* vermelho escuro */
}

/* Ajuste para modo escuro */
.dark .result-item {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .level-indicator .bg-muted {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Transição slide-fade para as perguntas */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

/* Ajuste para garantir que a transição funcione corretamente */
.question-card {
  overflow: hidden;
  min-height: 300px; /* Ajuste este valor conforme necessário */
}
</style>
