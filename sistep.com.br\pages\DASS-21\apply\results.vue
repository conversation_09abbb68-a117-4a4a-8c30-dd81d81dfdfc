<template>
  <main class="flex flex-1 flex-col leading-tight select-none p-4 w-full max-w-4xl mx-auto">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <Icon name="loader-2" class="h-8 w-8 animate-spin text-primary" />
      <span class="ml-2 text-muted-foreground">Calculando resultados...</span>
    </div>

    <!-- Results content -->
    <section v-else class="flex flex-col w-full max-w-3xl mx-auto">
      <header class="text-center mb-8">
        <h1 class="text-2xl font-bold mb-2">Resultados do DASS-21</h1>
        <p class="text-muted-foreground">Seus níveis de depressão, ansiedade e estresse</p>

        <!-- Contexto da Pesquisa -->
        <div v-if="isResearchContext" class="research-context mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="flex items-center gap-2 mb-2">
            <Icon name="info" class="text-blue-600 h-5 w-5" />
            <h3 class="text-sm font-semibold text-blue-800">Contexto da Pesquisa - Visão do Paciente</h3>
          </div>
          <p class="text-sm text-blue-700">
            Você está visualizando os resultados como um <strong>paciente</strong> veria.
            Esta é a primeira parte da simulação da pesquisa.
          </p>
        </div>
      </header>

      <!-- Test completion confirmation -->
      <div class="completion-banner p-6 bg-green-50 border border-green-200 rounded-lg mb-8">
        <div class="flex items-center gap-3 mb-3">
          <Icon name="check-circle" class="text-green-600 h-8 w-8" />
          <div>
            <h3 class="text-lg font-semibold text-green-800">Teste Concluído!</h3>
            <p class="text-sm text-green-700">Todas as 21 questões foram respondidas com sucesso.</p>
          </div>
        </div>
        <div class="text-xs text-green-600">
          Concluído em {{ formatCompletionTime() }}
        </div>
      </div>

      <!-- Legenda única para todos os resultados -->
      <div class="scale-legend flex justify-between mb-4 px-4 text-xs text-muted-foreground">
        <span>Normal</span>
        <span>Leve</span>
        <span>Moderado</span>
        <span>Severo</span>
        <span>Extremo</span>
      </div>

      <!-- Results grid -->
      <div class="results-grid space-y-6 mb-8">
        <div v-for="result in resultsData" :key="result.label" class="result-item border p-5 rounded-lg"
          :class="`border-${levelColorMap[getResultLevel(result.label, result.value)]}/50`">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold">{{ result.label }}</h3>
            <span class="text-2xl font-bold">{{ result.value }}</span>
          </div>

          <div class="level-indicator mb-5">
            <div class="bg-muted h-3 w-full rounded-full overflow-hidden">
              <div class="h-full rounded-full transition-all duration-1000 ease-out" :style="{
                width: getLevelPercentage(result.label, result.value) + '%',
                backgroundColor: getLevelColor(getResultLevel(result.label, result.value))
              }"></div>
            </div>
          </div>

          <p class="text-sm">{{ getResultDescription(result.label, result.value) }}</p>
        </div>
      </div>

      <!-- Interpretation -->
      <div class="interpretation p-5 border rounded-lg mb-8 bg-primary-foreground/30">
        <h2 class="text-lg font-semibold mb-3">Entendendo seus resultados</h2>
        <p class="mb-3">Esta escala fornece uma medida da gravidade dos sintomas de depressão, ansiedade e estresse.
          Os resultados são baseados em suas respostas sobre como você se sentiu na última semana.</p>
        <p class="text-sm text-muted-foreground italic">Lembre-se que esta é uma ferramenta de auto-avaliação e não
          substitui o diagnóstico profissional. Se você estiver preocupado com seus resultados, considere consultar um
          profissional de saúde mental.</p>
      </div>

      <!-- Contexto da Pesquisa - Próximos Passos -->
      <div v-if="isResearchContext" class="research-next-steps p-5 border border-blue-200 rounded-lg mb-6 bg-blue-50">
        <div class="flex items-center gap-3 mb-3">
          <Icon name="arrow-right" class="text-blue-600 h-6 w-6" />
          <h3 class="text-lg font-medium text-blue-800">Próximo Passo da Pesquisa</h3>
        </div>
        <p class="mb-4 text-blue-700">
          Agora você verá como um <strong>psicólogo</strong> visualizaria e analisaria estes mesmos resultados no
          painel administrativo da plataforma SISTEP.
        </p>
        <Button @click="proceedToPanel" variant="default" class="w-full md:w-auto bg-blue-600 hover:bg-blue-700" data-testid="continue-to-panel">
          <Icon name="stethoscope" class="mr-2 h-4 w-4" />
          Continuar para Visão do Psicólogo
        </Button>
      </div>

      <!-- Diferenciação entre auto-aplicação e aplicação por profissional -->
      <div v-else-if="testStore.isRequestedByProfessional"
        class="professional-feedback p-5 border rounded-lg mb-6 bg-success/10 border-success/30">
        <div class="flex items-center gap-3 mb-3">
          <Icon name="check-circle" class="text-success h-6 w-6" />
          <h3 class="text-lg font-medium">Resultados enviados</h3>
        </div>
        <p class="mb-4">Seus resultados foram enviados para o {{ testStore.professionalName }}. Obrigado por completar
          este teste.</p>
        <Button @click="goToHome" variant="default" class="w-full md:w-auto">
          <Icon name="home" class="mr-2 h-4 w-4" /> Voltar para Início
        </Button>
      </div>

      <!-- Action buttons for regular users -->
      <div v-else class="action-buttons grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button @click="restartTest" variant="outline" class="flex items-center justify-center">
          <Icon name="refresh-cw" class="mr-2" /> Refazer Teste
        </Button>
        <Button @click="shareResults" variant="default" class="flex items-center justify-center">
          <Icon name="share" class="mr-2" /> Compartilhar Resultados
        </Button>
        <Button @click="exportData" variant="outline" class="flex items-center justify-center">
          <Icon name="download" class="mr-2" /> Exportar Dados
        </Button>
      </div>

      <!-- Test summary -->
      <div class="test-summary mt-8 p-4 bg-muted/30 rounded-lg">
        <h3 class="font-semibold mb-2">Resumo do Teste</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span class="text-muted-foreground">Questões respondidas:</span>
            <div class="font-medium">{{ answeredQuestionsCount }}/{{ totalQuestions }}</div>
          </div>
          <div>
            <span class="text-muted-foreground">Tempo total:</span>
            <div class="font-medium">{{ calculateTestDuration() }}</div>
          </div>
          <div>
            <span class="text-muted-foreground">Última atualização:</span>
            <div class="font-medium">{{ formatTime(lastSavedAt) }}</div>
          </div>
          <div>
            <span class="text-muted-foreground">Status:</span>
            <div class="font-medium text-green-600">Completo</div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useDASS21 } from '~/composables/useDASS21.js'
import { useTestStore } from '~/stores/test'
import { clearTestData } from '~/utils/testPersistence.js'

const route = useRoute()
const router = useRouter()
const testStore = useTestStore()

// Usar o composable do DASS-21
const {
  // Estado
  results,
  resultsData,
  isLoading,
  lastSavedAt,
  totalQuestions,
  answeredQuestionsCount,
  answers,

  // Métodos
  calculateResults,
  getResultLevel,
  getResultDescription,
  getLevelColor,
  getLevelPercentage,
  levelColorMap,
  shareResults,
  restartTest,
  exportData,
  initialize
} = useDASS21()

// Estado local
const completionTime = ref(null)

// Computed properties
const isResearchContext = computed(() => {
  return route.query.participant_id && route.query.research_return_url
})

// Inicializar ao montar
onMounted(async () => {
  await initialize()
  
  // Calcular resultados se ainda não foram calculados
  if (!results.value || (results.value.depression === 0 && results.value.anxiety === 0 && results.value.stress === 0)) {
    calculateResults()
  }
  
  // Salvar resultados no store para compatibilidade
  testStore.saveTestResults({
    test: 'DASS-21',
    timestamp: new Date().toISOString(),
    scores: results.value,
    answers: Object.entries(answers.value || {}).map(([index, answer]) => ({
      questionIndex: parseInt(index),
      answer: parseInt(answer)
    }))
  })
  
  completionTime.value = new Date().toISOString()
})

/**
 * Formata o tempo de conclusão
 */
function formatCompletionTime() {
  if (!completionTime.value) return 'Agora'
  
  const date = new Date(completionTime.value)
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * Calcula a duração estimada do teste
 */
function calculateTestDuration() {
  // Estimativa baseada no número de questões (aproximadamente 30 segundos por questão)
  const estimatedMinutes = Math.ceil((answeredQuestionsCount.value * 30) / 60)
  return `~${estimatedMinutes} min`
}

/**
 * Formata timestamp para exibição
 */
function formatTime(timestamp) {
  if (!timestamp) return 'Nunca'
  
  const date = new Date(timestamp)
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * Procede para o painel de pesquisa
 */
function proceedToPanel() {
  if (isResearchContext.value) {
    // Backend Requirement: POST test results to API
    // POST /api/research/test-results
    // Body: { participant_id, test_type: 'DASS-21', results: scores, answers: detailed_answers }

    // Redirect back to research page with completion status
    const returnUrl = route.query.research_return_url
    window.location.href = `${returnUrl}&participant_id=${route.query.participant_id}`
  }
}

/**
 * Vai para a página inicial
 */
function goToHome() {
  router.push('/')
}

definePageMeta({
  layout: 'test'
})
</script>

<style scoped>
.result-item {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.level-indicator {
  position: relative;
}

.level-indicator .bg-muted {
  background-color: rgba(0, 0, 0, 0.05);
}

.scale-legend {
  font-size: 0.7rem;
  opacity: 0.7;
}

.completion-banner {
  animation: slideInFromTop 0.6s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Cores específicas para cada nível (modo claro) */
:root {
  --color-normal: 22, 163, 74;
  --color-mild: 245, 158, 11;
  --color-moderate: 217, 119, 6;
  --color-severe: 220, 38, 38;
  --color-extreme: 153, 27, 27;
}

/* Ajuste para modo escuro */
.dark .result-item {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .level-indicator .bg-muted {
  background-color: rgba(255, 255, 255, 0.05);
}
</style>
