<template>
    <div class="min-h-screen pb-20 md:pb-0">

        <section class="py-16">
            <div class="container mx-auto px-4 text-center">
                <div class="max-w-4xl mx-auto">
                    <h1 class="text-4xl font-bold text-foreground mb-6" title="Referência: ProjetoFinal.pdf linha 107, 108">
                        Pesquisa: Usabilidade de uma Plataforma de Aplicação e Correção de Instrumentos Não Privativos
                        em Psicologia (SISTEP)
                    </h1>
                    <p class="text-xl text-muted-foreground mb-8" title="Referência: ProjetoFinal.pdf linha 3, 109, 131">
                        Contribua para o avanço da psicologia digital participando da nossa pesquisa de mestrado sobre a
                        usabilidade e aceitação da plataforma SISTEP para aplicação e correção de instrumentos
                        psicológicos.
                    </p>
                    <div class="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
                        <span class="flex items-center">
                            <Clock class="w-4 h-4 mr-2" />
                            20-30 minutos (tempo estimado total)
                        </span>
                        <span class="flex items-center" title="Referência: ProjetoFinal.pdf linha 6">
                            <ShieldCheck class="w-4 h-4 mr-2" />
                            Dados anônimos
                        </span>
                        <span class="flex items-center">
                            <Award class="w-4 h-4 mr-2" />
                            Contribuição científica
                        </span>
                    </div>
                </div>
            </div>
        </section>

        <main class="container mx-auto px-4 py-12">
            <div class="grid lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 space-y-8">
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center">
                                <BookOpen class="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                                Sobre a Pesquisa
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div>
                                <h4 class="font-semibold text-foreground mb-2">Objetivo</h4>
                                <p class="text-muted-foreground">
                                    Esta pesquisa visa investigar o nível de usabilidade da plataforma SISTEP por
                                    psicólogos e estudantes de psicologia. <span title="Referência: ProjetoFinal.pdf linha 136"></span> A plataforma permite a aplicação
                                    e correção de instrumentos não privativos, começando com o DASS-21 (Depression,
                                    Anxiety and Stress Scale). <span title="Referência: ProjetoFinal.pdf linha 13, 111"></span>
                                </p>
                            </div>
                            <div>
                                <h4 class="font-semibold text-foreground mb-2">Vinculação Acadêmica</h4>
                                <p class="text-muted-foreground text-sm" title="Referência: ProjetoFinal.pdf linha 107">
                                    Programa de Pós-Graduação em Psicologia da Atitus Educação. <span title="Referência: ProjetoFinal.pdf linha 21"></span>
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center">
                                <Microscope class="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
                                Como Funciona
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div>
                                <h4 class="font-semibold text-foreground mb-2">O que você fará</h4>
                                <ul class="space-y-2 text-muted-foreground">
                                    <li class="flex items-start">
                                        <CheckCircle class="w-4 h-4 mr-2 mt-1 text-green-500 dark:text-green-400" />
                                        <span title="Referência: ProjetoFinal.pdf linha 26, 27, 111"><strong>Testar a plataforma SISTEP:</strong> Aplicar e visualizar resultados do DASS-21</span>
                                    </li>
                                    <li class="flex items-start">
                                        <CheckCircle class="w-4 h-4 mr-2 mt-1 text-green-500 dark:text-green-400" />
                                        <span title="Referência: ProjetoFinal.pdf linha 28, 29, 258"><strong>Avaliar usabilidade:</strong> Responder questionário SUS via Google Forms</span>
                                    </li>
                                    <li class="flex items-start">
                                        <CheckCircle class="w-4 h-4 mr-2 mt-1 text-green-500 dark:text-green-400" />
                                        <span title="Referência: ProjetoFinal.pdf linha 30, 31, 259"><strong>Avaliar experiência:</strong> Responder questionário UEQ via Google Forms</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">Importante: Navegação entre Sistemas</h4>
                                <p class="text-blue-800 dark:text-blue-200 text-sm">
                                    Durante o processo, você será redirecionado para outros sites (sistep.com.br e app.sistep.com.br) e retornará automaticamente aqui após cada etapa. Mantenha esta aba aberta.
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center">
                                <ShieldCheck class="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
                                Privacidade e Ética
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                                <ul class="space-y-1 text-purple-800 dark:text-purple-200 text-sm">
                                    <li title="Referência: ProjetoFinal.pdf linha 46">• Dados anônimos e confidenciais</li>
                                    <li title="Referência: ProjetoFinal.pdf linha 47">• Conformidade com a LGPD</li>
                                    <li title="Referência: ProjetoFinal.pdf linha 48">• Uso exclusivamente acadêmico</li>
                                    <li title="Referência: ProjetoFinal.pdf linha 297">• Retirada do consentimento a qualquer momento</li>
                                </ul>
                            </div>
                            <div>
                                <p class="text-muted-foreground text-sm">
                                    Pesquisa aprovada pelo CEP da Atitus Educação<span title="Referência: ProjetoFinal.pdf linha 293"></span>, seguindo diretrizes CNS 466/2012 e 510/2016. <span title="Referência: ProjetoFinal.pdf linha 51, 292"></span>
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <div class="grid md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle class="text-sm">Critérios de Participação</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ul class="space-y-2 text-sm text-muted-foreground">
                                    <li class="flex items-start" title="Referência: ProjetoFinal.pdf linha 68">
                                        <Check class="w-4 h-4 mr-2 mt-0.5 text-green-500 dark:text-green-400" />
                                        <span>Maior de 18 anos</span>
                                    </li>
                                    <li class="flex items-start" title="Referência: ProjetoFinal.pdf linha 69">
                                        <Check class="w-4 h-4 mr-2 mt-0.5 text-green-500 dark:text-green-400" />
                                        <span>Leitura e compreensão em português</span>
                                    </li>
                                    <li class="flex items-start" title="Referência: ProjetoFinal.pdf linha 71">
                                        <Check class="w-4 h-4 mr-2 mt-0.5 text-green-500 dark:text-green-400" />
                                        <span>Acesso à internet</span>
                                    </li>
                                    <li class="flex items-start" title="Referência: ProjetoFinal.pdf linha 256, 257">
                                        <Check class="w-4 h-4 mr-2 mt-0.5 text-green-500 dark:text-green-400" />
                                        <span>Psicólogo(a) registrado OU estudante de psicologia com disciplina de avaliação psicológica</span>
                                    </li>
                                </ul>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle class="text-sm">Contato da Pesquisa</CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-3 text-sm">
                                <div>
                                    <p class="font-medium text-foreground">Pesquisadora Responsável</p>
                                    <p class="text-muted-foreground" title="Referência: ProjetoFinal.pdf linha 108">Angélica Antunes de Oliveira (Mestranda)</p>
                                    <p class="text-muted-foreground" title="Referência: ProjetoFinal.pdf linha 434"><EMAIL></p>
                                </div>
                                <div>
                                    <p class="font-medium text-foreground">Orientador</p>
                                    <p class="text-muted-foreground" title="Referência: ProjetoFinal.pdf linha 108">Prof. Dr. Luis Henrique Paloski</p>
                                    <p class="text-muted-foreground" title="Referência: ProjetoFinal.pdf linha 434"><EMAIL></p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                <aside class="space-y-6">
                    <Card class="sticky top-6" id="participation-section">
                        <CardHeader>
                            <CardTitle class="text-center">{{ participationCardTitle }}</CardTitle>
                            <CardDescription class="text-center" title="Referência: ProjetoFinal.pdf linha 55">
                                {{ participationCardDescription }}
                            </CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="text-center mb-4">
                                <div
                                    class="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium px-3 py-1 rounded-full inline-block" title="Referência: ProjetoFinal.pdf linha 57">
                                    {{ participationStatus }}
                                </div>
                            </div>

                            <div class="space-y-3">
                                <!-- Etapa 1 -->
                                <div class="border rounded-lg">
                                    <div class="p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h5 class="font-semibold">Etapa 1: TCLE e Orientações</h5>
                                            <button @click="toggleStep(1)" class="text-xs text-muted-foreground hover:text-foreground">
                                                {{ expandedStep === 1 ? 'Ocultar' : 'Ver detalhes' }}
                                            </button>
                                        </div>
                                        <Button class="w-full mb-2" size="lg" @click="handleStep1_AcceptTCLE"
                                            :disabled="currentStep > 1">
                                            <FileText class="w-4 h-4 mr-2" />
                                            {{ currentStep === 1 ? 'Ler TCLE e Iniciar' : 'Concluído ✓' }}
                                        </Button>
                                        <p v-if="errorMsg && currentStep === 1" class="text-red-500 dark:text-red-400 text-xs">{{
                                            errorMsg }}</p>
                                    </div>
                                    
                                    <div v-if="expandedStep === 1 && activityLog.step1.length > 0" 
                                         class="px-4 pb-4 border-t bg-muted/20">
                                        <h6 class="text-xs font-medium text-muted-foreground mb-2 mt-2">Histórico de Atividades:</h6>
                                        <div class="space-y-1">
                                            <div v-for="activity in activityLog.step1" :key="activity" 
                                                 class="text-xs text-muted-foreground">
                                                {{ activity }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Etapa 2 -->
                                <div class="border rounded-lg" :class="{ 'opacity-50': currentStep < 2 }">
                                    <div class="p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h5 class="font-semibold">Etapa 2: Simular Paciente (DASS-21)</h5>
                                            <button @click="toggleStep(2)" class="text-xs text-muted-foreground hover:text-foreground"
                                                    v-if="activityLog.step2.length > 0">
                                                {{ expandedStep === 2 ? 'Ocultar' : 'Ver detalhes' }}
                                            </button>
                                        </div>
                                        <Button class="w-full mb-2" size="lg" @click="handleStep2_NavigateToDASS21"
                                            :disabled="currentStep !== 2">
                                            <Play class="w-4 h-4 mr-2" />
                                            {{ currentStep > 2 ? 'Concluído ✓' : 'Realizar DASS-21' }}
                                        </Button>
                                        <p class="text-xs text-muted-foreground">
                                            <strong>Redirecionamento:</strong> Você será levado para <code class="text-xs bg-muted px-1 py-0.5 rounded">sistep.com.br</code> e retornará automaticamente aqui.
                                        </p>
                                    </div>
                                    
                                    <div v-if="expandedStep === 2 && activityLog.step2.length > 0" 
                                         class="px-4 pb-4 border-t bg-muted/20">
                                        <h6 class="text-xs font-medium text-muted-foreground mb-2 mt-2">Histórico de Atividades:</h6>
                                        <div class="space-y-1">
                                            <div v-for="activity in activityLog.step2" :key="activity" 
                                                 class="text-xs text-muted-foreground">
                                                {{ activity }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Etapa 3 -->
                                <div class="border rounded-lg" :class="{ 'opacity-50': currentStep < 3 }">
                                    <div class="p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h5 class="font-semibold">Etapa 3: Simular Psicólogo (Painel)</h5>
                                            <button @click="toggleStep(3)" class="text-xs text-muted-foreground hover:text-foreground"
                                                    v-if="activityLog.step3.length > 0">
                                                {{ expandedStep === 3 ? 'Ocultar' : 'Ver detalhes' }}
                                            </button>
                                        </div>
                                        <Button class="w-full mb-2" size="lg" @click="handleStep3_NavigateToPanel"
                                            :disabled="currentStep !== 3">
                                            <BarChart2 class="w-4 h-4 mr-2" />
                                            {{ currentStep > 3 ? 'Concluído ✓' : 'Analisar Resultados' }}
                                        </Button>
                                        <p class="text-xs text-muted-foreground">
                                            <strong>Redirecionamento:</strong> Você será levado para <code class="text-xs bg-muted px-1 py-0.5 rounded">app.sistep.com.br</code> e retornará automaticamente aqui.
                                        </p>
                                    </div>
                                    
                                    <div v-if="expandedStep === 3 && activityLog.step3.length > 0" 
                                         class="px-4 pb-4 border-t bg-muted/20">
                                        <h6 class="text-xs font-medium text-muted-foreground mb-2 mt-2">Histórico de Atividades:</h6>
                                        <div class="space-y-1">
                                            <div v-for="activity in activityLog.step3" :key="activity" 
                                                 class="text-xs text-muted-foreground">
                                                {{ activity }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Etapa 4 -->
                                <div class="border rounded-lg" :class="{ 'opacity-50': currentStep < 4 }">
                                    <div class="p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h5 class="font-semibold">Etapa 4: Questionários da Pesquisa</h5>
                                            <button @click="toggleStep(4)" class="text-xs text-muted-foreground hover:text-foreground"
                                                    v-if="activityLog.step4.length > 0">
                                                {{ expandedStep === 4 ? 'Ocultar' : 'Ver detalhes' }}
                                            </button>
                                        </div>
                                        <Button class="w-full mb-2" size="lg" @click="handleStep4_NavigateToForms"
                                            :disabled="currentStep !== 4">
                                            <Edit3 class="w-4 h-4 mr-2" />
                                            {{ currentStep > 4 ? 'Concluído ✓' : 'Responder Questionários' }}
                                        </Button>
                                        <p class="text-xs text-muted-foreground">
                                            <strong>Redirecionamento:</strong> Você será levado para o Google Forms. Após enviar, sua participação estará completa.
                                        </p>
                                    </div>
                                    
                                    <div v-if="expandedStep === 4 && activityLog.step4.length > 0" 
                                         class="px-4 pb-4 border-t bg-muted/20">
                                        <h6 class="text-xs font-medium text-muted-foreground mb-2 mt-2">Histórico de Atividades:</h6>
                                        <div class="space-y-1">
                                            <div v-for="activity in activityLog.step4" :key="activity" 
                                                 class="text-xs text-muted-foreground">
                                                {{ activity }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Conclusão -->
                                <div v-if="currentStep === 5" class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-md">
                                    <CheckCircle class="w-8 h-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
                                    <h5 class="font-semibold text-green-700 dark:text-green-300">Participação Concluída!</h5>
                                    <p class="text-sm text-muted-foreground">Muito obrigado por sua valiosa contribuição!</p>
                                </div>
                            </div>

                            <div class="text-xs text-muted-foreground text-center mt-4 pt-4 border-t">
                                <p>Tempo total estimado: 20-30 minutos</p>
                                <p>Seus dados são anônimos e confidenciais.</p>
                            </div>
                        </CardContent>
                    </Card>
                </aside>
            </div>

            <section class="mt-12 text-center">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-900 dark:to-indigo-900 rounded-2xl p-8 text-white">
                    <h2 class="text-2xl font-bold mb-4">{{ ctaTitle }}</h2>
                    <p class="text-blue-100 mb-6 max-w-2xl mx-auto">
                        {{ ctaDescription }}
                    </p>
                    <div class="flex flex-wrap justify-center gap-4">
                        <Button v-if="currentStep === 1" @click="handleStep1_AcceptTCLE" size="lg" variant="secondary">
                            <FileText class="w-5 h-5 mr-2" />
                            Ler TCLE e Iniciar
                        </Button>
                        <Button v-else-if="currentStep === 2" @click="handleStep2_NavigateToDASS21" size="lg" variant="secondary">
                            <Play class="w-5 h-5 mr-2" />
                            Realizar DASS-21
                        </Button>
                        <Button v-else-if="currentStep === 3" @click="handleStep3_NavigateToPanel" size="lg" variant="secondary">
                            <BarChart2 class="w-5 h-5 mr-2" />
                            Acessar Painel
                        </Button>
                        <Button v-else-if="currentStep === 4" @click="handleStep4_NavigateToForms" size="lg" variant="secondary">
                            <Edit3 class="w-5 h-5 mr-2" />
                            Responder Questionários
                        </Button>
                        <Button v-else-if="currentStep === 5" size="lg" variant="secondary" disabled>
                            <CheckCircle class="w-5 h-5 mr-2" />
                            Participação Concluída
                        </Button>
                        <Button @click="scrollToTop" size="lg" variant="outline"
                            class="border-white text-foreground hover:bg-white hover:text-blue-600 dark:hover:bg-white/10 dark:hover:text-white">
                            <ArrowUp class="w-5 h-5 mr-2" />
                            Voltar ao Topo
                        </Button>
                    </div>
                </div>
            </section>
        </main>

        <footer class="bg-muted/20 border-t mt-16">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center text-sm text-muted-foreground">
                    <p class="mb-2" title="Referência: ProjetoFinal.pdf linha 102, 292">
                        Esta pesquisa está sendo conduzida em conformidade com as diretrizes éticas para pesquisa
                        envolvendo seres humanos (Res. CNS 466/12 e 510/16).
                    </p>
                </div>
            </div>
        </footer>

        <div v-if="showTCLEModal"
            class="fixed inset-0 bg-gray-800/75 dark:bg-black/75 flex items-center justify-center p-4 z-50">
            <Card class="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <CardHeader>
                    <CardTitle>Termo de Consentimento Livre e Esclarecido (TCLE)</CardTitle>
                </CardHeader>
                <CardContent>
                    <TCLEContent />
                </CardContent>
                <CardFooter class="flex justify-end gap-3">
                    <Button variant="outline" @click="declineTCLE">Não Concordo</Button>
                    <Button @click="acceptTCLE">Concordo e Continuar</Button>
                </CardFooter>
            </Card>
        </div>

    </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePageTitle } from '~/composables/usePageTitle'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '~/components/ui/card'
// Lucide Vue icons
import { 
    Clock, 
    ShieldCheck, 
    Award, 
    BookOpen, 
    Microscope, 
    CheckCircle, 
    FileText, 
    Play, 
    BarChart2, 
    Edit3, 
    Check,
    Target,
    BadgeCheck,
    Users,
    Lightbulb,
    ArrowUp
} from 'lucide-vue-next'
import TCLEContent from '~/components/ui/legal/TCLEContent.vue'

const router = useRouter()
const route = useRoute()
const { setPageTitle, clearPageTitle } = usePageTitle()

// SEO Meta Tags
useHead({
    title: 'Pesquisa Mestrado: Usabilidade Plataforma SISTEP (DASS-21) | Psicologia Digital', // [cite: 105, 107]
    meta: [
        {
            name: 'description',
            content: 'Participe da pesquisa de mestrado sobre a usabilidade da plataforma SISTEP para aplicação e correção digital do DASS-21. Sua contribuição é essencial para a psicologia digital.' // [cite: 131]
        },
        {
            name: 'keywords',
            content: 'pesquisa, mestrado, psicologia, DASS-21, SISTEP, usabilidade, testes psicológicos, digital, validação, Atitus' // [cite: 114]
        }
    ]
})

const currentStep = ref(1); // 1: TCLE, 2: DASS-21, 3: Panel, 4: Forms, 5: Complete
const participantId = ref(null); // To be generated/set
const errorMsg = ref('');
const showTCLEModal = ref(false);
const expandedStep = ref(null);
const activityLog = ref({
    step1: [],
    step2: [],
    step3: [],
    step4: []
});

// Computed properties for contextual messages
const participationCardTitle = computed(() => {
    switch (currentStep.value) {
        case 1:
            return 'Participe da Pesquisa'
        case 2:
            return 'Você está participando!'
        case 3:
            return 'Continue sua participação'
        case 4:
            return 'Finalize sua participação'
        case 5:
            return 'Participação Concluída'
        default:
            return 'Participe da Pesquisa'
    }
})

const participationCardDescription = computed(() => {
    switch (currentStep.value) {
        case 1:
            return 'Siga as etapas abaixo para contribuir.'
        case 2:
            return 'Prossiga para a simulação do teste DASS-21.'
        case 3:
            return 'Prossiga para analisar os resultados no painel.'
        case 4:
            return 'Finalize respondendo os questionários da pesquisa.'
        case 5:
            return 'Obrigado por sua valiosa contribuição!'
        default:
            return 'Siga as etapas abaixo para contribuir.'
    }
})

const participationStatus = computed(() => {
    switch (currentStep.value) {
        case 1:
            return 'Participação Voluntária e Anônima'
        case 2:
            return 'Participação em Andamento - Etapa 2'
        case 3:
            return 'Participação em Andamento - Etapa 3'
        case 4:
            return 'Participação em Andamento - Etapa Final'
        case 5:
            return 'Participação Concluída com Sucesso'
        default:
            return 'Participação Voluntária e Anônima'
    }
})

// ---- Configuration for URLs ----
// Ensure these are configurable if deploying to different environments
// Use import.meta.env.DEV to differentiate between development and production

const DASS21_BASE_URL = import.meta.env.DEV ? 'http://localhost:3000/DASS-21/apply' : 'https://sistep.com.br/DASS-21/apply';
// Backend Requirement: The DASS-21 app needs to accept participant_id and research_return_url as query parameters.
// It must redirect back to research_return_url with ?stepCompleted=dass&participant_id=...

const SISTEP_PANEL_BASE_URL = import.meta.env.DEV ? 'https://app.sistep.com.br.local' : 'https://app.sistep.com.br';
// Backend Requirement: The Panel app needs to accept participant_id and research_return_url as query parameters.
// It must redirect back to research_return_url with ?stepCompleted=panel&participant_id=...

// IMPORTANT: Replace with your actual Google Forms link
const GOOGLE_FORMS_BASE_URL = 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform';
// Replace with the actual ID of the Google Form field for participantId
const GOOGLE_FORMS_PARTICIPANT_ID_FIELD = 'entry.YOUR_PARTICIPANT_ID_FIELD_HERE';

const dass21Link = computed(() => {
    return `${DASS21_BASE_URL}?participant_id=${participantId.value}&current_step=2&research_return_url=${encodeURIComponent(window.location.origin + window.location.pathname + '?stepCompleted=dass')}`;
});

const sistepPanelLink = computed(() => {
    // The panel needs to know which test to show, or at least the participant
    // This might need adjustment based on how the panel identifies the specific test.
    // Include current_step to maintain research context in Laravel
    return `${SISTEP_PANEL_BASE_URL}?participant_id=${participantId.value}&current_step=3&research_return_url=${encodeURIComponent(window.location.origin + window.location.pathname + '?stepCompleted=panel')}`;
});

const googleFormsLink = computed(() => {
    return `${GOOGLE_FORMS_BASE_URL}?usp=pp_url&${GOOGLE_FORMS_PARTICIPANT_ID_FIELD}=${participantId.value}`;
});

// Function to generate a simple unique ID (for demo purposes)
// In a real scenario, this might come from a backend or a more robust UUID library
const generateUniqueId = () => 'pid_' + Date.now() + '_' + Math.random().toString(36).substring(2, 7);

// Activity logging functions
const addActivity = (step, message) => {
    const timestamp = new Date().toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
    const activity = `${timestamp} - ${message}`;
    activityLog.value[`step${step}`].push(activity);
    localStorage.setItem('researchActivityLog', JSON.stringify(activityLog.value));
};

const loadActivityLog = () => {
    const saved = localStorage.getItem('researchActivityLog');
    if (saved) {
        activityLog.value = JSON.parse(saved);
    }
};

onMounted(() => {
    // Set page title
    setPageTitle('Pesquisa')
    
    // Load activity log
    loadActivityLog();
    
    // Check for query params to resume state if returning from an external site
    const queryParticipantId = route.query.participant_id;
    const stepCompleted = route.query.stepCompleted;

    if (localStorage.getItem('researchParticipantId')) {
        participantId.value = localStorage.getItem('researchParticipantId');
    }
    if (localStorage.getItem('researchCurrentStep')) {
        currentStep.value = parseInt(localStorage.getItem('researchCurrentStep'), 10);
    }

    // Override with query params if they exist (means a return from external step)
    if (queryParticipantId) {
        participantId.value = queryParticipantId;
        localStorage.setItem('researchParticipantId', participantId.value);
    }

    if (stepCompleted && participantId.value) {
        if (stepCompleted === 'dass') {
            currentStep.value = 3; // Move to Panel step
            addActivity(2, 'Você concluiu o teste DASS-21');
        } else if (stepCompleted === 'panel') {
            currentStep.value = 4; // Move to Forms step
            addActivity(3, 'Você concluiu a análise no painel');
        }
        // Clean the URL
        router.replace({ query: {} });
    }
    localStorage.setItem('researchCurrentStep', currentStep.value.toString());

    // Listener para navegação do footer
    window.addEventListener('research-proceed-next', handleFooterProceed);
});

onUnmounted(() => {
    // Clear page title when leaving the page
    clearPageTitle()
    
    // Limpar event listener
    window.removeEventListener('research-proceed-next', handleFooterProceed);
});

function handleFooterProceed(event) {
    const currentStepFromFooter = event.detail.currentStep;
    
    // Acionar a função apropriada baseada na etapa atual
    if (currentStepFromFooter === 1) {
        handleStep1_AcceptTCLE();
    } else if (currentStepFromFooter === 2) {
        handleStep2_NavigateToDASS21();
    } else if (currentStepFromFooter === 3) {
        handleStep3_NavigateToPanel();
    } else if (currentStepFromFooter === 4) {
        handleStep4_NavigateToForms();
    }
}

const handleStep1_AcceptTCLE = () => {
    errorMsg.value = '';
    showTCLEModal.value = true;
};

const acceptTCLE = () => {

    // Simulate TCLE acceptance and participant ID generation
    if (!participantId.value) {
        participantId.value = generateUniqueId();
        localStorage.setItem('researchParticipantId', participantId.value);
        addActivity(1, `Participante ID gerado: ${participantId.value}`);
    }

    addActivity(1, 'Você aceitou o TCLE');
    currentStep.value = 2;
    localStorage.setItem('researchCurrentStep', currentStep.value.toString());
    showTCLEModal.value = false;
    // Optionally, scroll to participation section or next step button
    scrollToParticipationSection();
};

const declineTCLE = () => {
    showTCLEModal.value = false;
    errorMsg.value = 'Você precisa aceitar o TCLE para participar.';
    // Reset if needed
    // currentStep.value = 1;
    // participantId.value = null;
    // localStorage.removeItem('researchParticipantId');
    // localStorage.setItem('researchCurrentStep', currentStep.value.toString());
}

const handleStep2_NavigateToDASS21 = () => {
    if (!participantId.value) {
        errorMsg.value = "Erro: ID do participante não encontrado. Por favor, reinicie o processo aceitando o TCLE.";
        currentStep.value = 1; // Force back to TCLE
        localStorage.setItem('researchCurrentStep', currentStep.value.toString());
        return;
    }
    addActivity(2, 'Você iniciou o teste DASS-21');
    // The DASS-21 app MUST redirect back to this page with ?stepCompleted=dass&participant_id=...
    window.location.href = dass21Link.value;
};

const handleStep3_NavigateToPanel = () => {
    if (!participantId.value) {
        errorMsg.value = "Erro: ID do participante não encontrado. Por favor, complete a Etapa 1 e 2.";
        currentStep.value = 1; // Force back to TCLE
        localStorage.setItem('researchCurrentStep', currentStep.value.toString());
        return;
    }
    addActivity(3, 'Você acessou o painel de análise');
    // The Panel app MUST redirect back to this page with ?stepCompleted=panel&participant_id=...
    window.location.href = sistepPanelLink.value;
};

const handleStep4_NavigateToForms = () => {
    if (!participantId.value) {
        errorMsg.value = "Erro: ID do participante não encontrado. Por favor, complete as etapas anteriores.";
        currentStep.value = 1; // Force back to TCLE
        localStorage.setItem('researchCurrentStep', currentStep.value.toString());
        return;
    }
    addActivity(4, 'Você acessou os questionários de avaliação');
    // After submitting the form, the user is done with data input for this research.
    // Google Forms usually shows a "thanks" page. We can advance our state here.
    currentStep.value = 5; // Mark as complete
    localStorage.setItem('researchCurrentStep', currentStep.value.toString());
    addActivity(4, 'Participação concluída com sucesso');
    window.open(googleFormsLink.value, '_blank');
    // Consider adding a small delay or a button "I've completed the forms" if the redirect isn't instant
    // or if you want to ensure they saw the forms link.
    // For now, we assume they will complete it and we move our state.
};


function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function scrollToParticipationSection() {
    const participationElement = document.querySelector('.sticky.top-6'); // Adjust selector if needed
    if (participationElement) {
        participationElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

const ctaTitle = computed(() => {
    switch (currentStep.value) {
        case 1:
            return 'Pronto para Contribuir com a Pesquisa?'
        case 2:
            return 'Vamos Começar o DASS-21?'
        case 3:
            return 'Pronto para Analisar os Resultados?'
        case 4:
            return 'Última Etapa: Sua Opinião é Importante!'
        case 5:
            return 'Obrigado por sua Participação!'
        default:
            return 'Pronto para Iniciar sua Contribuição?'
    }
})

const ctaDescription = computed(() => {
    switch (currentStep.value) {
        case 1:
            return 'Sua participação voluntária é fundamental para o avanço da psicologia digital. Comece lendo e aceitando o TCLE.'
        case 2:
            return 'Vamos simular a experiência do paciente respondendo ao teste DASS-21.'
        case 3:
            return 'Agora você irá acessar o painel do psicólogo para analisar os resultados.'
        case 4:
            return 'Para finalizar, compartilhe sua experiência respondendo aos questionários de avaliação.'
        case 5:
            return 'Sua contribuição é muito valiosa para o avanço da psicologia digital. Muito obrigado!'
        default:
            return 'Sua participação voluntária é fundamental para o avanço da psicologia digital e para a melhoria das ferramentas disponíveis aos profissionais e estudantes.'
    }
})

function toggleStep(stepNumber) {
    expandedStep.value = expandedStep.value === stepNumber ? null : stepNumber;
}

</script>

<style scoped>
/* Your existing styles from pesquisa.vue.txt, e.g. .hero-commercial */
.hero-commercial {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* [cite: 106] */
}

/* Additional styling for disabled-like appearance */
.opacity-50 {
    opacity: 0.5;
}

/* Ensure buttons within a disabled-like step are not clickable if not truly disabled */
.opacity-50 button:not(:disabled) {
    pointer-events: none;
}
</style>