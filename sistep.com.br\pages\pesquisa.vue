<template>
    <div class="bg-background dark:bg-slate-900">

        <!-- Hero Section -->
        <section class="bg-gradient-primary min-h-[calc(60vh)] md:min-h-[calc(100vh-160px)] flex items-center">
            <div class="container mx-auto px-4 text-left ">
                <div class="grid md:grid-cols-2 pt-64 pb-12 md:pt-0 gap-12 place-items-end md:items-center">
                    <!-- Left Content -->
                    <div class="text-white space-y-4 animate-fade-in">
                        <div class="block highlight-box w-fit h-fit">
                            Participe da Pesquisa
                        </div>

                        <h1 class="font-display text-5xl lg:text-6xl leading-tight">
                            Validação do uso de testes psicológicos não privativos em <span
                                class="italic underline decoration-accent">formatos digitais.</span>
                        </h1>

                        <div class="space-y-0 text-lg md:text-xl">
                            <p class="font-medium">Aplique um teste, corrija os dados, avalie a experiência.</p>
                            <p class="text-gray-200 dark:text-gray-300">Leva no máximo 30 minutos. Participe!</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="py-0 px-4">
            <div class="container mx-auto p-8 md:p-16 text-center bg-white dark:bg-slate-800 rounded-lg -mt-8 md:-mt-16 shadow-lg dark:shadow-slate-900/20">
                <div class="max-w-4xl mx-auto space-y-4">
                    <div class="mx-auto block highlight-box w-fit h-fit">
                        O que é?
                    </div>
                    <h1 class="text-2xl md:text-3xl font-black text-foreground dark:text-white leading-tight"
                        title="Referência: ProjetoFinal.pdf linha 107, 108">
                        <u>Pesquisa de Mestrado:</u> Usabilidade de uma Plataforma de Aplicação e Correção de
                        Instrumentos
                        Não Privativos em Psicologia
                    </h1>

                    <div class="flex flex-wrap justify-center gap-x-2 lg:gap-4 text-xs lg:text-sm text-muted-foreground dark:text-slate-400 leading-tight">
                        <span class="flex items-center leading-none">
                            <Clock class="w-4 h-4 mr-2" />
                            20-30 minutos <span class="hidden md:inline"> (tempo estimado total)</span>
                        </span>
                        <span class="flex items-center leading-none" title="Referência: ProjetoFinal.pdf linha 6">
                            <ShieldCheck class="w-4 h-4 mr-2" />
                            Dados anônimos
                        </span>
                        <span class="flex items-center leading-none">
                            <Award class="w-4 h-4 mr-2" />
                            Contribuição científica
                        </span>
                    </div>

                    <p class="text-base md:text-xl text-muted-foreground dark:text-slate-300 mb-8"
                        title="Referência: ProjetoFinal.pdf linha 3, 109, 131">
                        Esta pesquisa busca validar o uso de ferramentas digitais na aplicação e correção de
                        instrumentos psicológicos, através de uma pesquisa guiada, composta por <strong>4
                            etapas</strong>:
                    </p>

                    <div class="flex flex-row gap-10 text-left pt-12 items-center justify-center">
                        <ResearchStepper 
                            ref="researchStepperRef"
                            :steps="steps" 
                            :current-step="currentStep"
                            :activity-log="activityLog"
                            :expanded-step="expandedAccordionStep"
                            @update:expanded-step="expandedAccordionStep = $event"
                            @step-action="handleStepAction"
                            @step-retry="handleStepRetry"
                            @step-transition-start="handleStepTransitionStart"
                            @step-transition-complete="handleStepTransitionComplete"
                        />
                    </div>

                </div>
            </div>
        </section>

        <section>

        </section>

        <section class="mt-12 text-center">
            <div class="container mx-auto">
                <!-- CTA dinâmico -->
                <div
                    class="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 rounded-2xl p-8 text-white shadow-lg dark:shadow-slate-900/20">
                    <h2 class="text-2xl font-bold mb-4 text-white">{{ ctaTitle }}</h2>
                    <p class="text-blue-100 dark:text-blue-200 mb-6 max-w-2xl mx-auto">
                        {{ ctaDescription }}
                    </p>
                    
                    <div class="flex flex-wrap justify-center gap-4">
                        <Button v-if="currentStep === 1" @click="startResearch" size="lg" variant="secondary"
                            class="bg-white text-blue-600 hover:bg-blue-50 dark:bg-slate-100 dark:text-blue-700 dark:hover:bg-white btn-transition">
                            Ir para Etapa 1
                            <ArrowUp class="w-5 h-5 ml-2" />
                        </Button>
                        <Button v-else-if="currentStep === 2" @click="startResearch" size="lg"
                            variant="secondary"
                            class="bg-white text-blue-600 hover:bg-blue-50 dark:bg-slate-100 dark:text-blue-700 dark:hover:bg-white btn-transition">
                            Ir para Etapa 2
                            <ArrowUp class="w-5 h-5 ml-2" />
                        </Button>
                        <Button v-else-if="currentStep === 3" @click="startResearch" size="lg"
                            variant="secondary"
                            class="bg-white text-blue-600 hover:bg-blue-50 dark:bg-slate-100 dark:text-blue-700 dark:hover:bg-white btn-transition">
                            Ir para Etapa 3
                            <ArrowUp class="w-5 h-5 ml-2" />
                        </Button>
                        <Button v-else-if="currentStep === 4" @click="startResearch" size="lg"
                            variant="secondary"
                            class="bg-white text-blue-600 hover:bg-blue-50 dark:bg-slate-100 dark:text-blue-700 dark:hover:bg-white btn-transition">
                            Ir para Etapa 4
                            <ArrowUp class="w-5 h-5 ml-2" />
                        </Button>
                        <Button v-else-if="currentStep === 5" size="lg" variant="secondary" disabled
                            class="bg-gray-200 text-gray-500 dark:bg-slate-600 dark:text-slate-400">
                            <CheckCircle class="w-5 h-5 mr-2" />
                            Participação Concluída
                        </Button>
                        
                        <Button @click="scrollToTop" size="lg" variant="outline"
                            class="border-white text-white hover:bg-white hover:text-blue-600 dark:border-slate-200 dark:text-white dark:hover:bg-white dark:hover:text-blue-700 btn-transition">
                            Voltar ao Topo
                            <ArrowUp class="w-5 h-5 ml-2" />
                        </Button>
                    </div>
                </div>

            </div>

        </section>

        <footer class="bg-muted/20 dark:bg-slate-800/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center text-sm text-muted-foreground dark:text-slate-400">
                    <p class="mb-2" title="Referência: ProjetoFinal.pdf linha 102, 292">
                        Esta pesquisa está sendo conduzida em conformidade com as diretrizes éticas para pesquisa
                        envolvendo seres humanos (Res. CNS 466/12 e 510/16).
                    </p>
                </div>
            </div>
        </footer>

        <div v-if="showTCLEModal"
            class="fixed inset-0 bg-gray-800/75 dark:bg-black/90 flex items-center justify-center p-4 z-50 animate-in fade-in duration-300">
            <Card class="max-w-2xl w-full max-h-[90vh] overflow-y-auto dark:bg-slate-900 dark:border-slate-700 animate-in zoom-in-95 duration-300">
                <CardHeader>
                    <CardTitle class="dark:text-white">Termo de Consentimento Livre e Esclarecido (TCLE)</CardTitle>
                </CardHeader>
                <CardContent class="dark:text-slate-300">
                    <TCLEContent />
                </CardContent>
                <CardFooter class="flex justify-end gap-3">
                    <Button 
                        variant="outline" 
                        @click="declineTCLE" 
                        :disabled="isTransitioning"
                        class="dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800 transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': isTransitioning }"
                    >
                        <Loader2 v-if="isTransitioning" class="w-4 h-4 mr-2 animate-spin" />
                        Não Concordo
                    </Button>
                    <Button 
                        @click="acceptTCLE"
                        :disabled="isTransitioning"
                        class="dark:bg-blue-600 dark:hover:bg-blue-700 transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': isTransitioning }"
                    >
                        <Loader2 v-if="isTransitioning" class="w-4 h-4 mr-2 animate-spin" />
                        Concordo e Continuar
                    </Button>
                </CardFooter>
            </Card>
        </div>

    </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePageTitle } from '~/composables/usePageTitle'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '~/components/ui/card'
// Lucide Vue icons
import {
    Clock,
    ShieldCheck,
    Award,
    BookOpen,
    Microscope,
    CheckCircle,
    FileText,
    Play,
    BarChart2,
    Edit3,
    Check,
    Target,
    BadgeCheck,
    Users,
    Lightbulb,
    ArrowUp,
    ExternalLink,
    RotateCcw,
    Loader2
} from 'lucide-vue-next'
import TCLEContent from '~/components/ui/legal/TCLEContent.vue'
import ResearchStepper from '@/components/ResearchStepper.vue'
import { useResearchStore } from '~/stores/research'

const router = useRouter()
const route = useRoute()
const { setPageTitle, clearPageTitle } = usePageTitle()
const researchStore = useResearchStore()

// Get runtime config
const runtimeConfig = useRuntimeConfig();

definePageMeta({
    layout: 'pesquisa'
})

const steps = [
    {
        step: 1,
        title: 'Etapa 1',
        description: 'Nesta etapa você lerá e concordará com o Termo de Consentimento Livre e Esclarecido (TCLE) para participar da pesquisa.',
        detailsText: 'Você deve ler e aceitar o Termo de Consentimento Livre e Esclarecido (TCLE) para iniciar sua participação na pesquisa. Este documento garante seus direitos como participante.',
        estimatedTime: '5 minutos',
        requirements: [
            'Ser maior de 18 anos',
            'Ter capacidade de leitura e compreensão em português',
            'Aceitar participar voluntariamente da pesquisa'
        ],
        actions: [
            {
                id: 'accept-tcle',
                text: 'Ler TCLE e Iniciar',
                icon: FileText,
                variant: 'primary',
                allowWhen: 'always'
            }
        ]
    },
    {
        step: 2,
        title: 'Etapa 2',
        description: 'Nesta etapa você aplicará uma versão digital do Teste de Depressão, Ansiedade e Estresse (DASS-21).',
        detailsText: 'Simule a experiência de um paciente respondendo ao teste DASS-21. Você será redirecionado para a plataforma SISTEP e retornará automaticamente aqui após a conclusão.',
        estimatedTime: '10-15 minutos',
        requirements: [
            'Ter concluído a Etapa 1',
            'Responder todas as 21 questões do teste',
            'Manter esta aba aberta durante o redirecionamento'
        ],
        actions: [
            {
                id: 'start-dass21',
                text: 'Realizar DASS-21',
                icon: Play,
                variant: 'primary',
                allowWhen: 'active'
            },
            // {
            //     id: 'view-instructions',
            //     text: 'Ver Instruções',
            //     icon: BookOpen,
            //     variant: 'outline',
            //     allowWhen: 'always'
            // }
        ],
        allowRetry: true
    },
    {
        step: 3,
        title: 'Etapa 3',
        description: 'Painel de Análise',
        detailsText: 'Acesse o painel do psicólogo para analisar os resultados do teste DASS-21. Explore as funcionalidades de visualização e interpretação dos dados.',
        estimatedTime: '5-10 minutos',
        requirements: [
            'Ter concluído a Etapa 2',
            'Explorar as funcionalidades do painel',
            'Analisar os resultados apresentados'
        ],
        actions: [
            {
                id: 'access-panel',
                text: 'Acessar Painel',
                icon: BarChart2,
                variant: 'primary',
                allowWhen: 'active'
            },
            // {
            //     id: 'view-demo',
            //     text: 'Ver Demo',
            //     icon: ExternalLink,
            //     variant: 'outline',
            //     allowWhen: 'always'
            // }
        ],
        allowRetry: true
    },
    {
        step: 4,
        title: 'Etapa 4',
        description: 'Questionários de Avaliação',
        detailsText: 'Finalize sua participação respondendo aos questionários de avaliação da experiência. Suas respostas são fundamentais para a pesquisa.',
        estimatedTime: '5-10 minutos',
        requirements: [
            'Ter concluído todas as etapas anteriores',
            'Responder o questionário SUS (usabilidade)',
            'Responder o questionário UEQ (experiência do usuário)'
        ],
        actions: [
            {
                id: 'answer-forms',
                text: 'Responder Questionários',
                icon: Edit3,
                variant: 'primary',
                allowWhen: 'active'
            },
            // {
            //     id: 'preview-forms',
            //     text: 'Visualizar Questionários',
            //     icon: ExternalLink,
            //     variant: 'outline',
            //     allowWhen: 'always'
            // }
        ],
        allowRetry: false
    }
]

// SEO Meta Tags
useHead({
    title: 'Pesquisa Mestrado: Usabilidade Plataforma SISTEP (DASS-21) | Psicologia Digital', // [cite: 105, 107]
    meta: [
        {
            name: 'description',
            content: 'Participe da pesquisa de mestrado sobre a usabilidade da plataforma SISTEP para aplicação e correção digital do DASS-21. Sua contribuição é essencial para a psicologia digital.' // [cite: 131]
        },
        {
            name: 'keywords',
            content: 'pesquisa, mestrado, psicologia, DASS-21, SISTEP, usabilidade, testes psicológicos, digital, validação, Atitus' // [cite: 114]
        }
    ]
})

// Use store state and computed properties
const currentStep = computed(() => researchStore.currentStep)
const participantId = computed(() => researchStore.participantId)
const isTransitioning = computed(() => researchStore.isTransitioning)
const showTCLEModal = computed(() => researchStore.showTCLEModal)
const errorMsg = computed(() => researchStore.errorMsg)
const expandedAccordionStep = computed({
    get: () => researchStore.expandedAccordionStep,
    set: (value) => researchStore.setExpandedAccordionStep(value)
})
const activityLog = computed(() => researchStore.activityLog)

// Reference to ResearchStepper component
const researchStepperRef = ref(null)

// Computed properties from store
const participationCardTitle = computed(() => researchStore.participationCardTitle)
const participationCardDescription = computed(() => researchStore.participationCardDescription)
const participationStatus = computed(() => researchStore.participationStatus)
const ctaTitle = computed(() => researchStore.ctaTitle)
const ctaDescription = computed(() => researchStore.ctaDescription)

// ---- Configuration for URLs ----
// Ensure these are configurable if deploying to different environments

const DASS21_BASE_URL = runtimeConfig.public.APP_URL + '/DASS-21/apply';
// Backend Requirement: The DASS-21 app needs to accept participant_id and research_return_url as query parameters.
// It must redirect back to research_return_url with ?stepCompleted=dass&participant_id=...

const SISTEP_PANEL_BASE_URL = runtimeConfig.public.BACK_URL;
// Backend Requirement: The Panel app needs to accept participant_id and research_return_url as query parameters.
// It must redirect back to research_return_url with ?stepCompleted=panel&participant_id=...

// IMPORTANT: Replace with your actual Google Forms link
const GOOGLE_FORMS_BASE_URL = 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform';
// Replace with the actual ID of the Google Form field for participantId
const GOOGLE_FORMS_PARTICIPANT_ID_FIELD = 'entry.YOUR_PARTICIPANT_ID_FIELD_HERE';

const dass21Link = computed(() => {
    return `${DASS21_BASE_URL}?participant_id=${participantId.value}&current_step=2&research_return_url=${encodeURIComponent(window.location.origin + window.location.pathname + '?stepCompleted=dass')}`;
});

const sistepPanelLink = computed(() => {
    // The panel needs to know which test to show, or at least the participant
    // This might need adjustment based on how the panel identifies the specific test.
    // Include current_step to maintain research context in Laravel
    return `${SISTEP_PANEL_BASE_URL}?participant_id=${participantId.value}&current_step=3&research_return_url=${encodeURIComponent(window.location.origin + window.location.pathname + '?stepCompleted=panel')}`;
});

const googleFormsLink = computed(() => {
    return `${GOOGLE_FORMS_BASE_URL}?usp=pp_url&${GOOGLE_FORMS_PARTICIPANT_ID_FIELD}=${participantId.value}`;
});

onMounted(() => {
    // Set page title
    setPageTitle('Pesquisa')

    // Initialize store from localStorage
    researchStore.initializeFromStorage()

    // Check for query params to resume state if returning from an external site
    const queryParticipantId = route.query.participant_id;
    const stepCompleted = route.query.stepCompleted;

    // Handle return from external sites
    if (queryParticipantId || stepCompleted) {
        const previousStep = currentStep.value;
        researchStore.handleReturnFromExternal(queryParticipantId, stepCompleted)
        
        // If step changed due to external completion, trigger visual transition
        if (currentStep.value > previousStep && researchStepperRef.value) {
            nextTick(() => {
                if (researchStepperRef.value.transitionToStep) {
                    researchStepperRef.value.transitionToStep(currentStep.value);
                }
            });
        }
        
        // Clean the URL
        router.replace({ query: {} });
    }

    // Listener for footer navigation
    window.addEventListener('research-proceed-next', handleFooterProceed);
    
    // Listener for header button
    window.addEventListener('start-research', startResearch);
    
    // Expose startResearch function globally for header access
    window.startResearch = startResearch;
});

onUnmounted(() => {
    // Clear page title when leaving the page
    clearPageTitle()

    // Clean up event listeners
    window.removeEventListener('research-proceed-next', handleFooterProceed);
    window.removeEventListener('start-research', startResearch);
    
    // Clean up global function
    if (window.startResearch) {
        delete window.startResearch;
    }
});

function handleFooterProceed(event) {
    const currentStepFromFooter = event.detail.currentStep;

    // Trigger appropriate function based on current step
    if (currentStepFromFooter === 1) {
        handleStep1_AcceptTCLE();
    } else if (currentStepFromFooter === 2) {
        handleStep2_NavigateToDASS21();
    } else if (currentStepFromFooter === 3) {
        handleStep3_NavigateToPanel();
    } else if (currentStepFromFooter === 4) {
        handleStep4_NavigateToForms();
    }
}

const handleStep1_AcceptTCLE = () => {
    researchStore.setErrorMessage('');
    researchStore.setTCLEModal(true);
};

const acceptTCLE = async () => {
    try {
        // Iniciar estado de transição
        researchStore.setTransitioning(true);
        
        // Gerar ID do participante se não existir
        if (!participantId.value) {
            const newId = researchStore.generateParticipantId();
            researchStore.addActivity(1, `Participante ID gerado: ${newId}`);
        }

        // Adicionar atividade de aceitação do TCLE
        researchStore.addActivity(1, 'TCLE aceito com sucesso');
        
        // Fechar modal com animação suave
        researchStore.setTCLEModal(false);
        
        // Aguardar o modal fechar completamente
        await new Promise(resolve => setTimeout(resolve, 400));
        
        // Atualizar para o próximo step ANTES da transição visual
        researchStore.setCurrentStep(2);
        
        // Adicionar feedback visual de progresso
        researchStore.addActivity(2, 'Etapa 2 ativada - pronto para iniciar DASS-21');
        
        // A transição visual será feita pelo ResearchStepper quando o botão for clicado
        // Não fazemos transição aqui para evitar duplicação
        
    } catch (error) {
        console.error('Erro durante aceitação do TCLE:', error);
        researchStore.setErrorMessage('Erro ao processar aceitação do TCLE. Tente novamente.');
    } finally {
        // Finalizar estado de transição
        researchStore.setTransitioning(false);
    }
};

const declineTCLE = async () => {
    try {
        // Iniciar estado de transição
        researchStore.setTransitioning(true);
        
        // Fechar modal com animação suave
        researchStore.setTCLEModal(false);
        
        // Aguardar o modal fechar completamente
        await new Promise(resolve => setTimeout(resolve, 400));
        
        // Adicionar feedback visual
        researchStore.addActivity(1, 'TCLE recusado - resetando pesquisa...');
        
        // Aguardar um momento para feedback visual
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Colapsar step atual se estiver expandido
        if (expandedAccordionStep.value) {
            expandedAccordionStep.value = null;
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        // Resetar estado da pesquisa
        researchStore.resetResearch();
        researchStore.setErrorMessage('Você precisa aceitar o TCLE para participar da pesquisa.');
        
        // Adicionar atividade final
        researchStore.addActivity(1, 'Pesquisa resetada - você pode tentar novamente');
        
        // Fazer scroll suave para o step 1 e expandir
        if (researchStepperRef.value && researchStepperRef.value.transitionToStep) {
            await researchStepperRef.value.transitionToStep(1);
        } else {
            // Fallback
            scrollToParticipationSection();
            expandedAccordionStep.value = 'step-1';
        }
        
    } catch (error) {
        console.error('Erro durante recusa do TCLE:', error);
        researchStore.setErrorMessage('Erro ao processar recusa. Tente novamente.');
    } finally {
        // Finalizar estado de transição
        researchStore.setTransitioning(false);
    }
}

const handleStep2_NavigateToDASS21 = () => {
    if (!participantId.value) {
        researchStore.setErrorMessage("Erro: ID do participante não encontrado. Por favor, reinicie o processo aceitando o TCLE.");
        researchStore.setCurrentStep(1); // Force back to TCLE
        return;
    }
    
    // NÃO atualizar para próxima etapa ainda - só após retornar
    researchStore.addActivity(2, 'Você iniciou o teste DASS-21');
    researchStore.addActivity(2, 'Redirecionando para aplicação do teste...');
    
    // The DASS-21 app MUST redirect back to this page with ?stepCompleted=dass&participant_id=...
    window.location.href = dass21Link.value;
};

const handleStep3_NavigateToPanel = () => {
    if (!participantId.value) {
        researchStore.setErrorMessage("Erro: ID do participante não encontrado. Por favor, complete a Etapa 1 e 2.");
        researchStore.setCurrentStep(1); // Force back to TCLE
        return;
    }
    
    // NÃO atualizar para próxima etapa ainda - só após retornar
    researchStore.addActivity(3, 'Você acessou o painel de análise');
    researchStore.addActivity(3, 'Redirecionando para painel...');
    
    // The Panel app MUST redirect back to this page with ?stepCompleted=panel&participant_id=...
    window.location.href = sistepPanelLink.value;
};

const handleStep4_NavigateToForms = () => {
    if (!participantId.value) {
        researchStore.setErrorMessage("Erro: ID do participante não encontrado. Por favor, complete as etapas anteriores.");
        researchStore.setCurrentStep(1); // Force back to TCLE
        return;
    }
    
    researchStore.addActivity(4, 'Você acessou os questionários de avaliação');
    researchStore.addActivity(4, 'Abrindo questionários em nova aba...');
    
    // Mark as complete after opening forms
    researchStore.setCurrentStep(5);
    researchStore.addActivity(4, 'Participação concluída com sucesso');
    
    window.open(googleFormsLink.value, '_blank');
    // Consider adding a small delay or a button "I've completed the forms" if the redirect isn't instant
    // or if you want to ensure they saw the forms link.
    // For now, we assume they will complete it and we move our state.
};

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function scrollToParticipationSection() {
    const participationElement = document.querySelector('.fixed.top-6'); // Adjust selector if needed
    if (participationElement) {
        participationElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// Function to scroll to specific step and expand it
function scrollToStepAndExpand(stepNumber) {
    // First scroll to the ResearchStepper component
    const stepperElement = document.querySelector('.research-stepper');
    if (stepperElement) {
        stepperElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Wait for scroll to complete, then expand the step
        setTimeout(() => {
            expandedAccordionStep.value = `step-${stepNumber}`;
        }, 500);
    }
}

// Function to start research (called by header/CTA buttons)
function startResearch() {
    // If user hasn't started, scroll to step 1 and expand it
    if (currentStep.value === 1 && !participantId.value) {
        scrollToStepAndExpand(1);
    } else {
        // If user has started, scroll to current step
        scrollToStepAndExpand(currentStep.value);
    }
}

function toggleStep(stepNumber) {
    expandedAccordionStep.value = expandedAccordionStep.value === stepNumber ? null : stepNumber;
}

// Event handlers for ResearchStepper transitions
const handleStepTransitionStart = (fromStep, toStep) => {
    researchStore.setTransitioning(true);
    researchStore.addActivity(toStep, `Transição da etapa ${fromStep} para ${toStep} iniciada`);
}

const handleStepTransitionComplete = (step) => {
    researchStore.setTransitioning(false);
    researchStore.addActivity(step, `Etapa ${step} ativada`);
}

// Event handlers for ResearchStepper
const handleStepAction = async (stepNumber, actionId, url) => {
    // Don't process if already transitioning
    if (isTransitioning.value) return;
    
    // Add activity log before action
    researchStore.addActivity(stepNumber, `Ação iniciada: ${actionId}`);
    
    // Execute the appropriate action - ResearchStepper já fez a transição visual
    switch (stepNumber) {
        case 1:
            if (actionId === 'accept-tcle') {
                handleStep1_AcceptTCLE();
            }
            break;
        case 2:
            if (actionId === 'start-dass21') {
                // Não precisa fazer transição aqui, ResearchStepper já fez
                handleStep2_NavigateToDASS21();
            } else if (actionId === 'view-instructions') {
                // Show instructions modal or navigate to instructions page
                alert('Instruções do DASS-21: Responda todas as 21 questões com sinceridade...');
            }
            break;
        case 3:
            if (actionId === 'access-panel') {
                // Não precisa fazer transição aqui, ResearchStepper já fez
                handleStep3_NavigateToPanel();
            } else if (actionId === 'view-demo') {
                // Open demo in new tab
                window.open('https://app.sistep.com.br/demo', '_blank');
            }
            break;
        case 4:
            if (actionId === 'answer-forms') {
                // Não precisa fazer transição aqui, ResearchStepper já fez
                handleStep4_NavigateToForms();
            } else if (actionId === 'preview-forms') {
                // Open forms preview in new tab
                window.open('https://docs.google.com/forms/preview', '_blank');
            }
            break;
    }
}

const handleStepRetry = async (stepNumber) => {
    // Don't process if already transitioning
    if (isTransitioning.value) return;
    
    researchStore.setTransitioning(true);
    
    // Reset to the specified step and clear subsequent activities
    const previousStep = currentStep.value;
    researchStore.setCurrentStep(stepNumber);
    
    // Clear activities for this step and subsequent steps
    researchStore.clearActivitiesFromStep(stepNumber);
    
    researchStore.addActivity(stepNumber, `Etapa ${stepNumber} reiniciada (anterior: ${previousStep})`);
    
    // Use ResearchStepper transition
    if (researchStepperRef.value && researchStepperRef.value.transitionToStep) {
        await researchStepperRef.value.transitionToStep(stepNumber);
    }
    
    researchStore.setTransitioning(false);
}

</script>

<style scoped>
/* Your existing styles from pesquisa.vue.txt, e.g. .hero-commercial */
.hero-commercial {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* [cite: 106] */
}

/* Additional styling for disabled-like appearance */
.opacity-50 {
    opacity: 0.5;
}

/* Ensure buttons within a disabled-like step are not clickable if not truly disabled */
.opacity-50 button:not(:disabled) {
    pointer-events: none;
}

/* Hero Section Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #110a2a 0%, #2d0a2f5b 100%),
        url('/img/Whisk_7dcdc55355.jpg');
    background-size: cover;
    background-repeat: no-repeat, no-repeat;
    background-position: center, right center;
    background-blend-mode: multiply;
}

/* Mobile adjustments for background image */
@media (max-width: 1024px) {
    .bg-gradient-primary {
        background-position: bottom, 50%;
    }
}

/* Mobile adjustments for background image */
@media (max-width: 768px) {
    .bg-gradient-primary {
        background-position: bottom, 70%;
    }
}

/* Mobile adjustments for background image */
@media (max-width: 480px) {
    .bg-gradient-primary {
        background-position: bottom, 60%;
    }
}

.highlight-box {
    background: yellow;
    padding: 0.05rem 1rem 0.05rem 1rem;
    border-radius: 0rem;
    font-size: 0.75rem;
    font-weight: 700;
    color: black;
    text-align: center;
    min-width: 130px;
}

.font-display {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
}

.decoration-accent {
    text-decoration-color: #f59e0b;
}

.bg-accent {
    background-color: #f59e0b;
}

.btn-accent {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.btn-accent:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.6);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInModal {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes zoomInModal {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out 0.3s forwards;
    opacity: 0;
}

/* Modal animations */
.animate-in {
    animation-fill-mode: both;
}

.fade-in {
    animation: fadeInModal 0.3s ease-out;
}

.zoom-in-95 {
    animation: zoomInModal 0.3s ease-out;
}

.duration-300 {
    animation-duration: 0.3s;
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Loading state animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Improved button transitions */
.btn-transition {
    transition: all 0.2s ease-in-out;
    transform: translateY(0);
}

.btn-transition:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-transition:active:not(:disabled) {
    transform: translateY(0);
}

/* Disabled state improvements */
.opacity-50 {
    opacity: 0.5;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

/* Research stepper transition improvements */
.research-stepper-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
}

/* Estilos para o sistema de cores do projeto */
.sistep-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
}

.sistep-text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Utilitários para responsividade */
.container-sistep {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container-sistep {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container-sistep {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container-sistep {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container-sistep {
        max-width: 1280px;
    }
}

@media (min-width: 1536px) {
    .container-sistep {
        max-width: 1536px;
    }
}

/* CSS customizado para forçar sticky a funcionar */
.sticky-nav-container {
    position: sticky !important;
    top: 1rem !important;
    z-index: 10;
}

.grid-layout-container {
    display: grid;
    grid-template-columns: 1fr 4fr;
    min-height: 100vh;
    gap: 0;
}

.sidebar-column {
    border-right: 1px solid hsl(var(--border));
}

.content-column {
    padding: 1.5rem;
}

@media (max-width: 1024px) {
    .grid-layout-container {
        grid-template-columns: 1fr;
    }

    .sidebar-column {
        border-right: none;
        border-bottom: 1px solid hsl(var(--border));
    }

    .sticky-nav-container {
        position: relative !important;
    }

    .font-display {
        font-size: 2.5rem;
        line-height: 1.2;
    }
}

@media (max-width: 768px) {
    .font-display {
        font-size: 2rem;
        line-height: 1.3;
    }

    .bg-gradient-primary {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
}
</style>