import { defineStore } from 'pinia'

export const useResearchStore = defineStore('research', {
  state: () => ({
    currentStep: 1, // 1: TCLE, 2: DASS-21, 3: Panel, 4: Forms, 5: Complete
    participantId: null,
    isTransitioning: false,
    expandedAccordionStep: null,
    showTCLEModal: false,
    errorMsg: '',
    activityLog: {
      step1: [],
      step2: [],
      step3: [],
      step4: []
    }
  }),

  getters: {
    // Computed property for research button text based on current step and location
    researchButtonText: (state) => (isOnResearchPage = false) => {
      const step = state.currentStep;
      
      // If research is completed
      if (step >= 5) {
        return isOnResearchPage ? 'Pesquisa Concluída' : 'Ver Pesquisa Concluída';
      }
      
      // If not started yet (no participant_id)
      if (!state.participantId) {
        return 'Participar da Pesquisa';
      }
      
      // Texts based on current step and location
      if (isOnResearchPage) {
        switch (step) {
          case 1:
            return 'Iniciar Pesquisa';
          case 2:
            return 'Continuar Pesquisa';
          case 3:
            return 'Continuar Pesquisa';
          case 4:
            return 'Continuar Pesquisa';
          default:
            return 'Continuar Pesquisa';
        }
      } else {
        // When not on research page
        switch (step) {
          case 1:
            return 'Iniciar Pesquisa';
          case 2:
            return 'Continuar DASS-21';
          case 3:
            return 'Continuar Análise';
          case 4:
            return 'Finalizar Pesquisa';
          default:
            return 'Voltar à Pesquisa';
        }
      }
    },

    // CTA title based on current step
    ctaTitle: (state) => {
      switch (state.currentStep) {
        case 1:
          return 'Pronto para Contribuir com a Pesquisa?';
        case 2:
          return 'Vamos Começar o DASS-21?';
        case 3:
          return 'Muito bem! Você já está na etapa 3';
        case 4:
          return 'Última Etapa: Sua Opinião é Importante!';
        case 5:
          return 'Obrigado por sua Participação!';
        default:
          return 'Pronto para Iniciar sua Contribuição?';
      }
    },

    // CTA description based on current step
    ctaDescription: (state) => {
      switch (state.currentStep) {
        case 1:
          return 'Sua participação voluntária é fundamental para o avanço da psicologia digital. Comece lendo e aceitando o TCLE.';
        case 2:
          return 'Vamos simular a experiência do paciente respondendo ao teste DASS-21.';
        case 3:
          return 'Agora você irá acessar o painel do psicólogo para analisar os resultados. Você pode fazer isso agora ou mais tarde.';
        case 4:
          return 'Para finalizar, compartilhe sua experiência respondendo aos questionários de avaliação.';
        case 5:
          return 'Sua contribuição é muito valiosa para o avanço da psicologia digital. Muito obrigado!';
        default:
          return 'Sua participação voluntária é fundamental para o avanço da psicologia digital e para a melhoria das ferramentas disponíveis aos profissionais e estudantes.';
      }
    },

    // Participation card title
    participationCardTitle: (state) => {
      switch (state.currentStep) {
        case 1:
          return 'Participe da Pesquisa';
        case 2:
          return 'Você está participando!';
        case 3:
          return 'Continue sua participação';
        case 4:
          return 'Finalize sua participação';
        case 5:
          return 'Participação Concluída';
        default:
          return 'Participe da Pesquisa';
      }
    },

    // Participation card description
    participationCardDescription: (state) => {
      switch (state.currentStep) {
        case 1:
          return 'Siga as etapas abaixo para contribuir.';
        case 2:
          return 'Prossiga para a simulação do teste DASS-21.';
        case 3:
          return 'Prossiga para analisar os resultados no painel.';
        case 4:
          return 'Finalize respondendo os questionários da pesquisa.';
        case 5:
          return 'Obrigado por sua valiosa contribuição!';
        default:
          return 'Siga as etapas abaixo para contribuir.';
      }
    },

    // Participation status
    participationStatus: (state) => {
      switch (state.currentStep) {
        case 1:
          return 'Participação Voluntária e Anônima';
        case 2:
          return 'Participação em Andamento - Etapa 2';
        case 3:
          return 'Participação em Andamento - Etapa 3';
        case 4:
          return 'Participação em Andamento - Etapa Final';
        case 5:
          return 'Participação Concluída com Sucesso';
        default:
          return 'Participação Voluntária e Anônima';
      }
    },

    // Check if research context exists
    isResearchContext: (state) => {
      return !!state.participantId;
    },

    // Check if can proceed to next step
    canProceedToNext: (state) => {
      return state.currentStep < 5;
    },

    // Check if needs attention (for header icon)
    needsAttention: (state) => {
      return state.currentStep < 5 && state.participantId;
    },

    // Get current step description for header banner
    currentStepDescription: (state) => {
      const descriptions = {
        1: 'Aceite do TCLE.',
        2: 'Teste DASS-21.',
        3: 'Painel do psicólogo.',
        4: 'Questionário no Google Forms.'
      };
      return descriptions[state.currentStep] || 'Continue sua participação.';
    },

    // Get next step button text for header banner
    nextStepButtonText: (state) => {
      const texts = {
        1: 'Aceitar TCLE.',
        2: 'Continuar DASS-21.',
        3: 'Acessar Painel.',
        4: 'Responder Questionários.'
      };
      return texts[state.currentStep] || 'Continuar';
    }
  },

  actions: {
    // Initialize research state from localStorage
    initializeFromStorage() {
      if (process.client) {
        const savedParticipantId = localStorage.getItem('researchParticipantId');
        const savedCurrentStep = localStorage.getItem('researchCurrentStep');
        const savedActivityLog = localStorage.getItem('researchActivityLog');

        if (savedParticipantId) {
          this.participantId = savedParticipantId;
        }
        if (savedCurrentStep) {
          this.currentStep = parseInt(savedCurrentStep, 10);
        }
        if (savedActivityLog) {
          this.activityLog = JSON.parse(savedActivityLog);
        }
      }
    },

    // Generate unique participant ID
    generateParticipantId() {
      const id = 'pid_' + Date.now() + '_' + Math.random().toString(36).substring(2, 7);
      this.participantId = id;
      this.saveToStorage();
      return id;
    },

    // Update current step
    setCurrentStep(step) {
      this.currentStep = step;
      this.saveToStorage();
    },

    // Add activity to log
    addActivity(step, message) {
      const timestamp = new Date().toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });
      const activity = `${timestamp} - ${message}`;
      this.activityLog[`step${step}`].push(activity);
      this.saveToStorage();
    },

    // Clear activity log for specific step and subsequent steps
    clearActivitiesFromStep(stepNumber) {
      for (let i = stepNumber; i <= 4; i++) {
        this.activityLog[`step${i}`] = [];
      }
      this.saveToStorage();
    },

    // Save state to localStorage
    saveToStorage() {
      if (process.client) {
        if (this.participantId) {
          localStorage.setItem('researchParticipantId', this.participantId);
        }
        localStorage.setItem('researchCurrentStep', this.currentStep.toString());
        localStorage.setItem('researchActivityLog', JSON.stringify(this.activityLog));
      }
    },

    // Reset research state completely
    resetResearch() {
      this.currentStep = 1;
      this.participantId = null;
      this.isTransitioning = false;
      this.expandedAccordionStep = null;
      this.showTCLEModal = false;
      this.errorMsg = '';
      this.activityLog = {
        step1: [],
        step2: [],
        step3: [],
        step4: []
      };

      if (process.client) {
        localStorage.removeItem('researchParticipantId');
        localStorage.removeItem('researchCurrentStep');
        localStorage.removeItem('researchActivityLog');
      }
    },

    // Handle query parameters from external redirects
    handleReturnFromExternal(queryParticipantId, stepCompleted) {
      if (queryParticipantId) {
        this.participantId = queryParticipantId;
      }

      if (stepCompleted && this.participantId) {
        if (stepCompleted === 'dass') {
          this.setCurrentStep(3); // Move to Panel step
          this.addActivity(2, 'Você concluiu o teste DASS-21');
        } else if (stepCompleted === 'panel') {
          this.setCurrentStep(4); // Move to Forms step
          this.addActivity(3, 'Você concluiu a análise no painel');
        }
      }
    },

    // Set transition state
    setTransitioning(isTransitioning) {
      this.isTransitioning = isTransitioning;
    },

    // Set expanded accordion step
    setExpandedAccordionStep(step) {
      this.expandedAccordionStep = step;
    },

    // Set TCLE modal visibility
    setTCLEModal(show) {
      this.showTCLEModal = show;
    },

    // Set error message
    setErrorMessage(message) {
      this.errorMsg = message;
    }
  }
}) 