import { defineStore } from 'pinia'

export const useTestStore = defineStore('test', {
  state: () => ({
    currentTest: null,
    testType: null, // 'DASS-21', 'BDI', etc.
    questions: [],
    answers: {},
    fontSize: 'base', // <PERSON><PERSON><PERSON> padrão
    // Informações sobre a origem do teste
    isRequestedByProfessional: false,
    professionalCode: null,
    professionalName: null,
    testResults: null
  }),
  actions: {
    initializeNewTest(testType = null) {
      // Limpar dados de teste anterior
      this.answers = {};
      this.testResults = null;
      
      // Definir o tipo de teste se fornecido
      if (testType) {
        this.testType = testType;
      }
      
      // Verificar se há parâmetros na URL que indiquem que o teste foi solicitado por um profissional
      if (process.client) {
        const urlParams = new URLSearchParams(window.location.search);
        const refCode = urlParams.get('ref');
        const profCode = urlParams.get('code');
        const profName = urlParams.get('name');
        
        if (refCode === 'professional' && profCode) {
          this.isRequestedByProfessional = true;
          this.professionalCode = profCode;
          this.professionalName = profName || 'profissional responsável';
        } else {
          this.isRequestedByProfessional = false;
          this.professionalCode = null;
          this.professionalName = null;
        }
      }
    },
    async loadTestQuestions(testType) {
      // Atualizar o tipo de teste
      this.testType = testType;
      
      // Lógica para carregar perguntas específicas para cada tipo de teste
      switch (testType) {
        case 'DASS-21':
          // Carregará perguntas do DASS-21
          // Por enquanto, podemos simular com dados estáticos
          break;
        case 'BDI':
          // Carregará perguntas do BDI
          break;
        default:
          console.error(`Tipo de teste não reconhecido: ${testType}`);
      }
    },
    async loadTestFromQRCode(qrCodeData) {
      const testData = await fetchTestFromAPI(qrCodeData)
      this.currentTest = testData
      this.testType = testData.type
      this.questions = testData.questions
      this.answers = {}
    },
    async loadTestFromCode(testCode) {
      const testData = await fetchTestFromAPI(testCode)
      this.currentTest = testData
      this.testType = testData.type
      this.questions = testData.questions
      this.answers = {}
    },
    setAnswer(questionId, answer) {
      this.answers[questionId] = answer
    },
    setFontSize(size) {
      this.fontSize = size; // Apenas atualiza o estado
      
      if (process.client) {
        localStorage.setItem('fontSize', size);
      }
    },
    saveTestResults(results) {
      this.testResults = results;
      
      // Se o teste foi solicitado por um profissional, enviar os resultados
      if (this.isRequestedByProfessional && this.professionalCode) {
        this.sendResultsToProfessional(results);
      }
    },
    async sendResultsToProfessional(results) {
      try {
        // Aqui seria implementada a chamada API para o backend Laravel
        // Por enquanto simularemos o envio com um console.log
        console.log('Enviando resultados para o profissional:', {
          profCode: this.professionalCode,
          testType: this.testType,
          results: results
        });
        
        // A implementação real seria algo como:
        /*
        const response = await fetch('/api/submit-test-results', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            professionalCode: this.professionalCode,
            testType: this.testType,
            results: results
          }),
        });
        
        if (!response.ok) {
          throw new Error('Falha ao enviar resultados');
        }
        */
        
        return true;
      } catch (error) {
        console.error('Erro ao enviar resultados:', error);
        return false;
      }
    }
  },
})

async function fetchTestFromAPI(testIdentifier) {
  // Implementar chamada à API para buscar os dados do teste
  // Retornar os dados do teste
  
  // Por enquanto, vamos simular uma resposta
  return {
    type: 'DASS-21',
    questions: []
  };
}