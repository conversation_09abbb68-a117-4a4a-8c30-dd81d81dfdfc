// Armazenar dados no localStorage
export function saveToLocalStorage(key, value) {
    if (typeof window !== 'undefined') {
        localStorage.setItem(key, JSON.stringify(value));
    }
}

// Recuperar dados do localStorage
export function getFromLocalStorage(key) {
    if (typeof window !== 'undefined') {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    }
    return null;
}

export function removeFromLocalStorage(keys) {
    if (typeof window !== 'undefined') {
        for (const key of keys) {
            localStorage.removeItem(key);
        }
    }
}

// Verificar se localStorage está disponível
export function isLocalStorageAvailable() {
    return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
}