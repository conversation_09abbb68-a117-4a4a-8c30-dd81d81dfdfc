import { saveToLocalStorage, getFromLocalStorage, removeFromLocalStorage, isLocalStorageAvailable } from './localstorage.js'

/**
 * Utilitário para persistência de dados de testes psicológicos
 * Suporta múltiplos tipos de teste de forma genérica
 */

// Chaves do localStorage para diferentes tipos de dados
const STORAGE_KEYS = {
  TEST_PROGRESS: 'test_progress',
  TEST_ANSWERS: 'test_answers',
  TEST_METADATA: 'test_metadata',
  TEST_TIMESTAMP: 'test_timestamp'
}

/**
 * Salva o progresso atual do teste
 * @param {string} testType - Tipo do teste (ex: 'DASS-21', 'BDI')
 * @param {Object} progressData - Dados do progresso
 */
export function saveTestProgress(testType, progressData) {
  if (!isLocalStorageAvailable()) return false

  const key = `${STORAGE_KEYS.TEST_PROGRESS}_${testType}`
  const dataToSave = {
    ...progressData,
    testType,
    lastUpdated: new Date().toISOString(),
    version: '1.0' // Para futuras migrações
  }
  
  saveToLocalStorage(key, dataToSave)
  return true
}

/**
 * Recupera o progresso salvo do teste
 * @param {string} testType - Tipo do teste
 * @returns {Object|null} Dados do progresso ou null se não existir
 */
export function getTestProgress(testType) {
  if (!isLocalStorageAvailable()) return null

  const key = `${STORAGE_KEYS.TEST_PROGRESS}_${testType}`
  return getFromLocalStorage(key)
}

/**
 * Salva uma resposta específica do teste
 * @param {string} testType - Tipo do teste
 * @param {number} questionIndex - Índice da questão
 * @param {any} answer - Resposta da questão
 * @param {Object} questionData - Dados adicionais da questão
 */
export function saveTestAnswer(testType, questionIndex, answer, questionData = {}) {
  if (!isLocalStorageAvailable()) return false

  const key = `${STORAGE_KEYS.TEST_ANSWERS}_${testType}`
  const existingAnswers = getFromLocalStorage(key) || {}
  
  existingAnswers[questionIndex] = {
    answer,
    questionData,
    answeredAt: new Date().toISOString()
  }
  
  saveToLocalStorage(key, existingAnswers)
  
  // Atualizar timestamp geral
  saveToLocalStorage(`${STORAGE_KEYS.TEST_TIMESTAMP}_${testType}`, new Date().toISOString())
  
  return true
}

/**
 * Recupera todas as respostas salvas do teste
 * @param {string} testType - Tipo do teste
 * @returns {Object} Objeto com as respostas indexadas
 */
export function getTestAnswers(testType) {
  if (!isLocalStorageAvailable()) return {}

  const key = `${STORAGE_KEYS.TEST_ANSWERS}_${testType}`
  return getFromLocalStorage(key) || {}
}

/**
 * Recupera uma resposta específica
 * @param {string} testType - Tipo do teste
 * @param {number} questionIndex - Índice da questão
 * @returns {any|null} Resposta ou null se não existir
 */
export function getTestAnswer(testType, questionIndex) {
  const answers = getTestAnswers(testType)
  return answers[questionIndex]?.answer || null
}

/**
 * Salva metadados do teste (configurações, contexto, etc.)
 * @param {string} testType - Tipo do teste
 * @param {Object} metadata - Metadados do teste
 */
export function saveTestMetadata(testType, metadata) {
  if (!isLocalStorageAvailable()) return false

  const key = `${STORAGE_KEYS.TEST_METADATA}_${testType}`
  const dataToSave = {
    ...metadata,
    savedAt: new Date().toISOString()
  }
  
  saveToLocalStorage(key, dataToSave)
  return true
}

/**
 * Recupera metadados do teste
 * @param {string} testType - Tipo do teste
 * @returns {Object|null} Metadados ou null se não existir
 */
export function getTestMetadata(testType) {
  if (!isLocalStorageAvailable()) return null

  const key = `${STORAGE_KEYS.TEST_METADATA}_${testType}`
  return getFromLocalStorage(key)
}

/**
 * Verifica se existe progresso salvo para um teste
 * @param {string} testType - Tipo do teste
 * @returns {boolean} True se existe progresso salvo
 */
export function hasTestProgress(testType) {
  const progress = getTestProgress(testType)
  const answers = getTestAnswers(testType)
  
  return !!(progress || Object.keys(answers).length > 0)
}

/**
 * Calcula estatísticas do progresso do teste
 * @param {string} testType - Tipo do teste
 * @param {number} totalQuestions - Total de questões do teste
 * @returns {Object} Estatísticas do progresso
 */
export function getTestProgressStats(testType, totalQuestions) {
  const answers = getTestAnswers(testType)
  const answeredCount = Object.keys(answers).length
  const progress = getTestProgress(testType)
  
  return {
    answeredCount,
    totalQuestions,
    percentComplete: totalQuestions > 0 ? Math.round((answeredCount / totalQuestions) * 100) : 0,
    lastUpdated: progress?.lastUpdated || null,
    isComplete: answeredCount >= totalQuestions
  }
}

/**
 * Limpa todos os dados salvos de um teste específico
 * @param {string} testType - Tipo do teste
 */
export function clearTestData(testType) {
  if (!isLocalStorageAvailable()) return false

  const keysToRemove = [
    `${STORAGE_KEYS.TEST_PROGRESS}_${testType}`,
    `${STORAGE_KEYS.TEST_ANSWERS}_${testType}`,
    `${STORAGE_KEYS.TEST_METADATA}_${testType}`,
    `${STORAGE_KEYS.TEST_TIMESTAMP}_${testType}`
  ]
  
  removeFromLocalStorage(keysToRemove)
  return true
}

/**
 * Exporta todos os dados do teste para backup/análise
 * @param {string} testType - Tipo do teste
 * @returns {Object} Todos os dados do teste
 */
export function exportTestData(testType) {
  return {
    progress: getTestProgress(testType),
    answers: getTestAnswers(testType),
    metadata: getTestMetadata(testType),
    stats: getTestProgressStats(testType, 21), // Assumindo 21 questões por padrão
    exportedAt: new Date().toISOString()
  }
}

/**
 * Valida se os dados salvos são válidos e consistentes
 * @param {string} testType - Tipo do teste
 * @returns {Object} Resultado da validação
 */
export function validateTestData(testType) {
  const progress = getTestProgress(testType)
  const answers = getTestAnswers(testType)
  const metadata = getTestMetadata(testType)
  
  const issues = []
  
  // Verificar consistência entre progresso e respostas
  if (progress && progress.currentQuestion !== undefined) {
    const answeredCount = Object.keys(answers).length
    if (progress.currentQuestion > answeredCount) {
      issues.push('Inconsistência entre questão atual e respostas salvas')
    }
  }
  
  // Verificar se há respostas órfãs (sem dados da questão)
  Object.entries(answers).forEach(([index, data]) => {
    if (!data.questionData || !data.answeredAt) {
      issues.push(`Resposta ${index} com dados incompletos`)
    }
  })
  
  return {
    isValid: issues.length === 0,
    issues,
    checkedAt: new Date().toISOString()
  }
}
